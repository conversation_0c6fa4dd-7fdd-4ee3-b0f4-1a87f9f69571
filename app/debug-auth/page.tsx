'use client'

import { useEffect, useState } from 'react'
import { useAuthSafe } from '@/lib/auth-provider'
import { supabase } from '@/lib/supabase-client'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function DebugAuthPage() {
  const { user, signOut, providerAvailable } = useAuthSafe()
  const [dbUser, setDbUser] = useState<any>(null)
  const [familyMembers, setFamilyMembers] = useState<any[]>([])
  const [loading, setLoading] = useState(false)
  const [logs, setLogs] = useState<string[]>([])

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`])
  }

  const checkDbUser = async () => {
    if (!user) return
    
    setLoading(true)
    addLog('Checking user in database...')
    
    try {
      const { data, error } = await supabase
        .from('users')
        .select('*')
        .eq('auth_user_id', user.id)
        .maybeSingle()

      if (error) {
        addLog(`Error: ${error.message}`)
      } else if (data) {
        setDbUser(data)
        addLog(`User found in DB: ${data.email}`)
      } else {
        addLog('User not found in database')
      }
    } catch (error) {
      addLog(`Exception: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  const checkFamilyMembers = async () => {
    if (!dbUser) return
    
    setLoading(true)
    addLog('Checking family members...')
    
    try {
      const { data, error } = await supabase
        .from('family_members')
        .select('*')
        .eq('user_id', dbUser.id)

      if (error) {
        addLog(`Error: ${error.message}`)
      } else {
        setFamilyMembers(data || [])
        addLog(`Found ${data?.length || 0} family members`)
      }
    } catch (error) {
      addLog(`Exception: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  const testApiCall = async () => {
    if (!user) return
    
    setLoading(true)
    addLog('Testing API call...')
    
    try {
      const { data: { session } } = await supabase.auth.getSession()
      const token = session?.access_token
      
      const response = await fetch(`/api/onboarding/users?email=${encodeURIComponent(user.email!)}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      
      const result = await response.json()
      addLog(`API Response: ${response.status} - ${JSON.stringify(result)}`)
    } catch (error) {
      addLog(`API Error: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  const createTestUser = async () => {
    if (!user) return
    
    setLoading(true)
    addLog('Creating test user...')
    
    try {
      const { data: { session } } = await supabase.auth.getSession()
      const token = session?.access_token
      
      const response = await fetch('/api/onboarding/users', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          email: user.email,
          name: user.user_metadata?.full_name || 'Test User',
          role: 'parent'
        })
      })
      
      const result = await response.json()
      addLog(`Create User Response: ${response.status} - ${JSON.stringify(result)}`)
      
      if (response.ok) {
        checkDbUser()
      }
    } catch (error) {
      addLog(`Create User Error: ${error}`)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (user) {
      addLog(`Auth user: ${user.email}`)
      checkDbUser()
    }
  }, [user])

  if (!providerAvailable) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardHeader>
            <CardTitle>Debug Auth - Provider Not Mounted</CardTitle>
          </CardHeader>
          <CardContent>
            <p>This page requires the AuthProvider at runtime. In build/prerender, we render a safe fallback.</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardHeader>
            <CardTitle>Debug Auth - Not Authenticated</CardTitle>
          </CardHeader>
          <CardContent>
            <p>Please go to /onboarding to sign in first</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Debug Auth Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h3 className="font-semibold">Auth User</h3>
            <pre className="bg-gray-100 p-2 rounded text-xs">
              {JSON.stringify(user, null, 2)}
            </pre>
          </div>

          {dbUser && (
            <div>
              <h3 className="font-semibold">Database User</h3>
              <pre className="bg-gray-100 p-2 rounded text-xs">
                {JSON.stringify(dbUser, null, 2)}
              </pre>
            </div>
          )}

          <div className="flex gap-2 flex-wrap">
            <Button onClick={checkDbUser} disabled={loading}>
              Check DB User
            </Button>
            <Button onClick={checkFamilyMembers} disabled={loading || !dbUser}>
              Check Family Members
            </Button>
            <Button onClick={testApiCall} disabled={loading}>
              Test API Call
            </Button>
            <Button onClick={createTestUser} disabled={loading}>
              Create Test User
            </Button>
            <Button onClick={signOut} variant="outline">
              Sign Out
            </Button>
          </div>

          {familyMembers.length > 0 && (
            <div>
              <h3 className="font-semibold">Family Members</h3>
              <pre className="bg-gray-100 p-2 rounded text-xs">
                {JSON.stringify(familyMembers, null, 2)}
              </pre>
            </div>
          )}

          <div>
            <h3 className="font-semibold">Debug Log</h3>
            <div className="bg-black text-green-400 p-2 rounded text-xs h-48 overflow-y-auto">
              {logs.map((log, i) => (
                <div key={i}>{log}</div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}