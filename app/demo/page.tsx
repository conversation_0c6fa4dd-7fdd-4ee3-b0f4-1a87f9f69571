"use client"

import * as React from "react"
import { Header } from "@/components/header"
import Footer from "@/components/footer"
import { AspectRatio } from "@/components/ui/aspect-ratio"
import { motion } from "framer-motion"
import Image from "next/image"
import { Sparkles } from "lucide-react"

export default function DemoPage() {
  const [isPlaying1, setIsPlaying1] = React.useState(false)
  const [isPlaying2, setIsPlaying2] = React.useState(false)
  const [isPlaying3, setIsPlaying3] = React.useState(false)
  const [overlay1Visible, setOverlay1Visible] = React.useState(true)
  const [overlay2Visible, setOverlay2Visible] = React.useState(true)
  const [overlay3Visible, setOverlay3Visible] = React.useState(true)
  return (
    <main className="min-h-screen flex flex-col bg-background">
      <Header />

      {/* Hero */}
      <section className="relative overflow-hidden">
        <div className="absolute inset-0 -z-10 bg-gradient-to-b from-teal-500/10 via-transparent to-blue-600/10" />
        {/* ambient glows */}
        <div className="pointer-events-none absolute -top-28 -left-28 -z-10 h-[36rem] w-[36rem] rounded-full bg-teal-500/15 blur-3xl" />
        <div className="pointer-events-none absolute -bottom-32 -right-20 -z-10 h-[32rem] w-[32rem] rounded-full bg-blue-600/15 blur-3xl" />
        <div className="container mx-auto px-4 py-16 md:py-24">
          <motion.div
            initial={{ opacity: 0, y: 12 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-4xl mx-auto text-center space-y-6"
          >
            <div className="inline-flex items-center gap-2 rounded-full bg-white/10 px-4 py-1.5 text-xs text-white/90 ring-1 ring-white/20 backdrop-blur">
              <Sparkles className="h-3.5 w-3.5 text-teal-300" />
              <span>New • Reels of Mae in action</span>
            </div>

            <h1 className="text-4xl md:text-5xl font-semibold tracking-tight">
              <motion.span
                className="bg-clip-text text-transparent"
                style={{
                  backgroundImage:
                    "linear-gradient(90deg, #14b8a6, #06b6d4, #2563eb, #14b8a6)",
                  backgroundSize: "200% 100%",
                }}
                animate={{ backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"] }}
                transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
              >
                Product Demo Reels
              </motion.span>
            </h1>

            <p className="mx-auto max-w-2xl text-base md:text-lg text-muted-foreground">
              See Mae in action... Click any frame to enter.
            </p>

            {/* CTA buttons removed per request; videos are directly below */}

            <motion.div
              initial={{ width: 0, opacity: 0 }}
              animate={{ width: 200, opacity: 1 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="mx-auto h-[3px] rounded-full bg-gradient-to-r from-teal-400 via-cyan-400 to-blue-500"
            />
          </motion.div>
        </div>
      </section>

      {/* Letterbox Gallery */}
      <section className="pb-20 md:pb-28">
        <div className="container mx-auto px-4 grid gap-24 md:gap-32">
          {/* Design 1: Mae Context-aware */}
          <motion.div
            initial={{ opacity: 0, y: 24 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.4 }}
            transition={{ duration: 0.6 }}
            className="space-y-6"
          >

            <div id="first-reel" className="relative group">
              <div className="absolute -inset-0.5 rounded-3xl bg-gradient-to-r from-teal-500 via-cyan-500 to-blue-600 opacity-70 blur-lg" />
              <div className="relative rounded-3xl ring-1 ring-teal-500/40 bg-black overflow-hidden shadow-2xl">
                <AspectRatio ratio={21 / 9}>
                  <div className="relative h-full w-full bg-black">
                    {/* letterbox bars */}
                    <div className="pointer-events-none absolute top-0 left-0 right-0 h-10 md:h-12 bg-gradient-to-b from-black to-transparent" />
                    <div className="pointer-events-none absolute bottom-0 left-0 right-0 h-10 md:h-12 bg-gradient-to-t from-black to-transparent" />
                    <video
                      className="h-full w-full object-contain"
                      src="/videos/mae-context-aware.mp4"
                      controls
                      preload="metadata"
                      playsInline
                      muted
                      aria-label="Demo: Context-aware Mae capabilities"
                      onPlay={() => setIsPlaying1(true)}
                      onPause={() => setIsPlaying1(false)}
                      onEnded={() => setIsPlaying1(false)}
                    />
                    {/* Logo overlay */}
                    <motion.div
                      initial={{ opacity: 1 }}
                      animate={{ opacity: overlay1Visible ? 1 : 0 }}
                      className={`absolute inset-0 z-10 rounded-3xl bg-black flex items-center justify-center transition-opacity duration-300 ${overlay1Visible ? "pointer-events-auto" : "pointer-events-none"}`}
                      onClick={() => setOverlay1Visible(false)}
                    >
                      <div className="group transform transition-transform duration-500 ease-out hover:rotate-6 select-none">
                        <div className="relative w-44 h-44 md:w-64 md:h-64">
                          <Image src="/OKdark.svg" alt="Our Kidz" fill priority={false} className="object-contain drop-shadow-[0_10px_25px_rgba(20,184,166,0.35)]" />
                        </div>
                      </div>
                    </motion.div>
                    {/* subtle rim */}
                    <div className="pointer-events-none absolute inset-0 ring-1 ring-white/10 rounded-3xl" />
                  </div>
                </AspectRatio>
              </div>
            </div>
          </motion.div>

          {/* Design 2: Cinematic Focus */}
          <motion.div
            initial={{ opacity: 0, y: 24 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.4 }}
            transition={{ duration: 0.6, delay: 0.05 }}
            className="space-y-6"
          >

            <div className="relative group">
              <div className="absolute -inset-0.5 rounded-3xl bg-gradient-to-r from-teal-500 via-cyan-500 to-blue-600 opacity-70 blur-lg" />
              <div className="relative rounded-3xl ring-1 ring-teal-500/40 bg-black overflow-hidden shadow-2xl">
                <AspectRatio ratio={21 / 9}>
                  <div className="relative h-full w-full bg-black">
                    {/* letterbox bars */}
                    <div className="pointer-events-none absolute top-0 left-0 right-0 h-10 md:h-12 bg-gradient-to-b from-black to-transparent" />
                    <div className="pointer-events-none absolute bottom-0 left-0 right-0 h-10 md:h-12 bg-gradient-to-t from-black to-transparent" />
                    <video
                      className="h-full w-full object-contain"
                      src="/videos/mae-map2.mp4"
                      controls
                      preload="metadata"
                      playsInline
                      muted
                      aria-label="Demo: Provider map and navigation overview"
                      onPlay={() => setIsPlaying2(true)}
                      onPause={() => setIsPlaying2(false)}
                      onEnded={() => setIsPlaying2(false)}
                    />
                    {/* Logo overlay */}
                    <motion.div
                      initial={{ opacity: 1 }}
                      animate={{ opacity: overlay2Visible ? 1 : 0 }}
                      className={`absolute inset-0 z-10 rounded-3xl bg-black flex items-center justify-center transition-opacity duration-300 ${overlay2Visible ? "pointer-events-auto" : "pointer-events-none"}`}
                      onClick={() => setOverlay2Visible(false)}
                    >
                      <div className="group transform transition-transform duration-500 ease-out hover:rotate-6 select-none">
                        <div className="relative w-44 h-44 md:w-64 md:h-64">
                          <Image src="/OKdark.svg" alt="Our Kidz" fill priority={false} className="object-contain drop-shadow-[0_10px_25px_rgba(20,184,166,0.35)]" />
                        </div>
                      </div>
                    </motion.div>
                    {/* subtle rim */}
                    <div className="pointer-events-none absolute inset-0 ring-1 ring-white/10 rounded-3xl" />
                  </div>
                </AspectRatio>
              </div>
            </div>
          </motion.div>

          {/* Design 3: Cinematic Focus */}
          <motion.div
            initial={{ opacity: 0, y: 24 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true, amount: 0.4 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="space-y-6"
          >

            <div className="relative group">
              <div className="absolute -inset-0.5 rounded-3xl bg-gradient-to-r from-teal-500 via-cyan-500 to-blue-600 opacity-70 blur-lg" />
              <div className="relative rounded-3xl ring-1 ring-teal-500/40 bg-black overflow-hidden shadow-2xl">
                <AspectRatio ratio={21 / 9}>
                  <div className="relative h-full w-full bg-black">
                    {/* letterbox bars */}
                    <div className="pointer-events-none absolute top-0 left-0 right-0 h-10 md:h-12 bg-gradient-to-b from-black to-transparent" />
                    <div className="pointer-events-none absolute bottom-0 left-0 right-0 h-10 md:h-12 bg-gradient-to-t from-black to-transparent" />
                    <video
                      className="h-full w-full object-contain"
                      src="/videos/ok-promo-final.mp4"
                      controls
                      preload="metadata"
                      playsInline
                      muted
                      aria-label="Promo: Our Kidz overview promo"
                      onPlay={() => setIsPlaying3(true)}
                      onPause={() => setIsPlaying3(false)}
                      onEnded={() => setIsPlaying3(false)}
                    />
                    {/* Logo overlay */}
                    <motion.div
                      initial={{ opacity: 1 }}
                      animate={{ opacity: overlay3Visible ? 1 : 0 }}
                      className={`absolute inset-0 z-10 rounded-3xl bg-black flex items-center justify-center transition-opacity duration-300 ${overlay3Visible ? "pointer-events-auto" : "pointer-events-none"}`}
                      onClick={() => setOverlay3Visible(false)}
                    >
                      <div className="group transform transition-transform duration-500 ease-out hover:rotate-6 select-none">
                        <div className="relative w-44 h-44 md:w-64 md:h-64">
                          <Image src="/OKdark.svg" alt="Our Kidz" fill priority={false} className="object-contain drop-shadow-[0_10px_25px_rgba(20,184,166,0.35)]" />
                        </div>
                      </div>
                    </motion.div>
                    {/* subtle rim */}
                    <div className="pointer-events-none absolute inset-0 ring-1 ring-white/10 rounded-3xl" />
                  </div>
                </AspectRatio>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </main>
  )
}


