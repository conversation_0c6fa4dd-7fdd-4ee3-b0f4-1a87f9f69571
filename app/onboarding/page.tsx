"use client"

import React, { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@clerk/nextjs'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { Progress } from '@/components/ui/progress'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  ArrowLeft, 
  Heart, 
  User, 
  Users, 
  CheckCircle2, 
  Mic,
  MessageCircle,
  Sparkles,
  ChevronRight,
  Home,
  Activity,
  Zap,
  Star
} from 'lucide-react'
import { OnboardingInterface } from '@/components/onboarding-interface'
import { ThemeToggle } from '@/components/theme-toggle'
import { MaeSessionContinuationHandler } from '@/components/global-navigation-handler'
import { useMaeActivityTracker } from '@/hooks/use-mae-activity-tracker'
import { useGlobalMaeSession } from '@/components/global-mae-provider'
import AgentPlan from '@/components/ui/agent-plan'
import { MaeVoiceStatusPanel } from '@/components/mae-voice-status-panel'
import { MaeOnboardingProgress } from '@/components/mae-onboarding-progress'
import { MaeActivityFeed } from '@/components/mae-activity-feed'
import { cn } from '@/lib/utils'
import { initializeCrossTabCommunication } from '@/lib/cross-tab-communication'

interface OnboardingPageProps {}

const onboardingSteps = [
  { id: 'welcome', label: 'Welcome', icon: Heart, description: 'Get started with Our Kidz' },
  { id: 'user_info', label: 'Your Information', icon: User, description: 'Tell us about yourself' },
  { id: 'family_info', label: 'Family Members', icon: Users, description: 'Add your family' },
  { id: 'complete', label: 'Complete', icon: CheckCircle2, description: 'All set!' },
]

export default function OnboardingPage({}: OnboardingPageProps) {
  const router = useRouter()
  const { isSignedIn, isLoaded } = useAuth()
  const [showOnboardingForm, setShowOnboardingForm] = useState(true)
  const [currentStepIndex, setCurrentStepIndex] = useState(0)
  const [completedSteps, setCompletedSteps] = useState<string[]>([])
  
  // Use Mae activity tracker for enhanced status
  const maeActivity = useMaeActivityTracker()
  const { sessionState, isSessionActive } = useGlobalMaeSession()
  
  // Calculate progress percentage
  const progressPercentage = ((currentStepIndex + 1) / onboardingSteps.length) * 100
  
  // Get current step
  const currentStep = onboardingSteps[currentStepIndex]

  // Redirect to auth if not signed in
  useEffect(() => {
    if (isLoaded && !isSignedIn) {
      router.push('/auth')
    }
  }, [isSignedIn, isLoaded, router])

  const handleOnboardingComplete = (data: any) => {
    console.log('🎉 Onboarding completed:', data)
    setCompletedSteps([...completedSteps, currentStep.id])
    if (currentStepIndex < onboardingSteps.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1)
    }
  }

  const handleBackToHome = () => {
    router.push('/')
  }

  const handleStepClick = (index: number) => {
    if (index <= completedSteps.length) {
      setCurrentStepIndex(index)
    }
  }

  // Initialize cross-tab communication and listen for authentication success
  useEffect(() => {
    // Initialize cross-tab communication to handle auth callbacks from other tabs
    const communicator = initializeCrossTabCommunication()
    console.log('🔗 Cross-tab communication initialized for onboarding page')

    const handleMaeContinueSession = (event: CustomEvent) => {
      console.log('🎤 Mae session continued on onboarding page:', event.detail)
    }

    const handleUserAuthenticated = (event: CustomEvent) => {
      console.log('🔐 User authenticated, updating Mae context:', event.detail)
      const { email, returning_from_auth, user_id } = event.detail
      
      if (returning_from_auth) {
        // Dispatch event to Mae with user context
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('mae-user-context-update', {
            detail: {
              user_email: email,
              user_id: user_id,
              authenticated: true,
              returning_from_auth: true,
              message: 'User has successfully authenticated and returned to onboarding'
            }
          }))
        }, 1000)
      }
    }

    if (typeof window !== 'undefined') {
      window.addEventListener('mae-continue-session', handleMaeContinueSession as EventListener)
      window.addEventListener('user-authenticated', handleUserAuthenticated as EventListener)
      
      // Check URL parameters for auth success (fallback)
      const urlParams = new URLSearchParams(window.location.search)
      const authSuccess = urlParams.get('auth_success')
      const email = urlParams.get('email')
      
      if (authSuccess === 'true' && email) {
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('mae-user-context-update', {
            detail: {
              user_email: email,
              authenticated: true,
              returning_from_auth: true,
              message: 'User has successfully authenticated and returned to onboarding'
            }
          }))
        }, 1000)
      }
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('mae-continue-session', handleMaeContinueSession as EventListener)
        window.removeEventListener('user-authenticated', handleUserAuthenticated as EventListener)
      }
      // Cleanup cross-tab communicator
      if (communicator) {
        communicator.destroy()
      }
    }
  }, [])

  // Animation variants
  const fadeIn = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    exit: { opacity: 0, y: -20 },
    transition: { duration: 0.3 }
  }

  const slideIn = {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    exit: { opacity: 0, x: -20 },
    transition: { duration: 0.3 }
  }

  // Show loading state while checking auth
  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-primary/5 via-background to-primary/10">
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="flex flex-col items-center gap-4"
        >
          <div className="relative">
            <div className="absolute inset-0 animate-ping rounded-full h-16 w-16 bg-primary/20"></div>
            <div className="animate-spin rounded-full h-16 w-16 border-4 border-primary/20 border-t-primary"></div>
          </div>
          <p className="text-muted-foreground animate-pulse">Loading your experience...</p>
        </motion.div>
      </div>
    )
  }

  // Only show content if authenticated
  if (!isSignedIn) {
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
      {/* Mae Session Continuation Handler */}
      <MaeSessionContinuationHandler />
      
      {/* Enhanced Header with Progress */}
      <header className="sticky top-0 z-50 w-full bg-background/80 backdrop-blur-xl border-b border-border/50">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between py-4">
            <div className="flex items-center gap-4">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBackToHome}
                className="group flex items-center gap-2 hover:bg-primary/10"
              >
                <ArrowLeft className="h-4 w-4 transition-transform group-hover:-translate-x-1" />
                <span className="hidden sm:inline">Back to Home</span>
                <Home className="h-4 w-4 sm:hidden" />
              </Button>
              <Separator orientation="vertical" className="h-6 hidden sm:block" />
              <div className="flex items-center gap-3">
                <motion.div
                  whileHover={{ scale: 1.1, rotate: 5 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <img src="/OKdarkTsp.png" alt="Our Kidz Logo" className="w-8 h-8" />
                </motion.div>
                <div className="hidden sm:block">
                  <h1 className="text-xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                    Welcome to Our Kidz
                  </h1>
                  <p className="text-sm text-muted-foreground">Setting up your family dashboard</p>
                </div>
              </div>
            </div>

            {/* Theme Toggle and Mae Status */}
            <div className="flex items-center gap-3 ">
              <ThemeToggle />
              <AnimatePresence>
                {isSessionActive && (
                  <motion.div
                    initial={{ scale: 0, opacity: 0 }}
                    animate={{ scale: 1, opacity: 1 }}
                    exit={{ scale: 0, opacity: 0 }}
                    transition={{ type: "spring", stiffness: 500, damping: 30 }}
                  >
                    <Badge 
                      variant="outline" 
                      className={cn(
                        "flex items-center gap-2 border-primary/50 bg-primary/5",
                        sessionState.isRecording && "animate-pulse bg-primary/10 border-primary"
                      )}
                    >
                      <div className={cn(
                        "w-2 h-2 rounded-full",
                        sessionState.isRecording ? "bg-red-500 animate-pulse" : "bg-green-500"
                      )} />
                      <span className="text-xs font-medium">
                        Mae {sessionState.isRecording ? 'Listening' : 'Connected'}
                      </span>
                    </Badge>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>
          
          {/* Progress Bar */}
          <div className="pb-3">
            <Progress value={progressPercentage} className="h-1.5 bg-muted" />
          </div>
        </div>
      </header>

      {/* Main Content with Enhanced Layout */}
      <div className="container mx-full px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-2 mx-full">

          {/* Left Sidebar - Mae Voice Controls & Progress */}
          <div className="lg:col-span-3 mx-full space-y-6">
            {/* Mae Voice Status Panel */}
            {/* <MaeVoiceStatusPanel
              currentStep={currentStep.id}
              className="transition-all duration-300"
            /> */}

            {/* Enhanced Progress with Mae Guidance */}
            {/* <MaeOnboardingProgress
              currentStepIndex={currentStepIndex}
              completedSteps={completedSteps}
              onStepClick={handleStepClick}
              showMaeGuidance={true}
              className="transition-all duration-300"
            /> */}

            {/* Mae Activity Feed */}
            {/* <MaeActivityFeed
              maxItems={5}
              compact={false}
              showTimestamps={true}
              className="hidden lg:block transition-all duration-300"
            /> */}
          </div>

          {/* Main Content Area - Center */}
          <div className="lg:col-span-9">
            <AnimatePresence mode="wait">
              {showOnboardingForm ? (
                <motion.div
                  key="onboarding-form"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  {/* Enhanced Step Header with Mae Status */}
                  <div className="mb-6">
                    <motion.div
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="space-y-4"
                    >
                      {/* Step Title */}
                      <div className="flex items-center gap-4">
                        <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-primary via-primary/90 to-primary/70 flex items-center justify-center shadow-xl shadow-primary/30">
                          {React.createElement(currentStep.icon, { className: "h-7 w-7 text-primary-foreground" })}
                          {/* {React.createElement(currentStep.icon, { className: "h-7 w-7 text-primary-foreground" })} */}
                        </div>
                        <div className="flex-1">
                          <h2 className="text-3xl font-bold bg-gradient-to-r from-foreground to-foreground/80 bg-clip-text text-transparent">
                            {currentStep.label}
                          </h2>
                          <p className="text-muted-foreground mt-1">
                            Step {currentStepIndex + 1} of {onboardingSteps.length} • {currentStep.description}
                          </p>
                        </div>
                      </div>

                      {/* Mae Voice Guidance Banner */}
                      <AnimatePresence>
                        {isSessionActive && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: 'auto' }}
                            exit={{ opacity: 0, height: 0 }}
                            className="bg-gradient-to-r from-primary/10 via-primary/5 to-primary/10 border border-primary/20 rounded-xl p-4"
                          >
                            <div className="flex items-center gap-3">
                              <motion.div
                                animate={{
                                  scale: sessionState?.isRecording ? [1, 1.1, 1] : 1
                                }}
                                transition={{
                                  duration: 1,
                                  repeat: sessionState?.isRecording ? Infinity : 0
                                }}
                                className="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center"
                              >
                                <Sparkles className="h-5 w-5 text-primary" />
                              </motion.div>
                              <div className="flex-1">
                                <p className="text-sm font-medium text-primary">
                                  {sessionState?.isRecording
                                    ? "Mae is listening - speak naturally to fill out the form"
                                    : "Mae is ready to help - just start speaking!"
                                  }
                                </p>
                                <p className="text-xs text-muted-foreground">
                                  Voice guidance enabled • Real-time form assistance
                                </p>
                              </div>
                              {sessionState?.isRecording && (
                                <div className="flex items-center gap-1">
                                  {Array.from({ length: 3 }, (_, i) => (
                                    <motion.div
                                      key={i}
                                      className="w-1 bg-primary rounded-full"
                                      animate={{
                                        height: [6, 18, 6],
                                        opacity: [0.5, 1, 0.5]
                                      }}
                                      transition={{
                                        duration: 0.8,
                                        repeat: Infinity,
                                        delay: i * 0.2
                                      }}
                                    />
                                  ))}
                                </div>
                              )}
                            </div>
                          </motion.div>
                        )}
                      </AnimatePresence>
                    </motion.div>
                  </div>

                  {/* Onboarding Interface with enhanced styling */}
                  <div className="bg-card/50 backdrop-blur-sm rounded-xl border border-border/50 shadow-2xl p-1">
                    <OnboardingInterface
                      initialStep={currentStep.id as any}
                      isVoiceEnabled={true}
                      onComplete={handleOnboardingComplete}
                    />
                  </div>
                </motion.div>
              ) : (
                <motion.div
                  key="welcome-card"
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.95 }}
                  transition={{ duration: 0.3 }}
                >
                  <Card className="border-border/50 bg-gradient-to-br from-card via-card to-primary/5 shadow-2xl">
                    <CardHeader className="text-center pb-8">
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ type: "spring", stiffness: 200, damping: 20 }}
                        className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-primary to-primary/70 rounded-2xl flex items-center justify-center shadow-xl shadow-primary/25"
                      >
                        <Heart className="h-10 w-10 text-primary-foreground" />
                      </motion.div>
                      <CardTitle className="text-3xl font-bold bg-gradient-to-r from-primary to-primary/70 bg-clip-text text-transparent">
                        Welcome to Our Kidz
                      </CardTitle>
                      <CardDescription className="text-base mt-2">
                        Let's set up your family's personalized health dashboard
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-6">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          {[
                            { icon: Mic, title: "Voice Guided", desc: "Mae helps you through voice" },
                            { icon: Activity, title: "Personalized", desc: "Tailored to your family" },
                            { icon: Star, title: "Smart Insights", desc: "AI-powered recommendations" }
                          ].map((feature, index) => (
                            <motion.div
                              key={feature.title}
                              initial={{ opacity: 0, y: 20 }}
                              animate={{ opacity: 1, y: 0 }}
                              transition={{ delay: index * 0.1 }}
                              className="bg-muted/50 rounded-lg p-4 text-center"
                            >
                              <div className="w-12 h-12 mx-auto mb-2 bg-primary/10 rounded-lg flex items-center justify-center">
                                <feature.icon className="h-6 w-6 text-primary" />
                              </div>
                              <h3 className="font-semibold text-sm">{feature.title}</h3>
                              <p className="text-xs text-muted-foreground mt-1">{feature.desc}</p>
                            </motion.div>
                          ))}
                        </div>

                        <Separator className="my-6" />

                        <div className="text-center space-y-4">
                          <p className="text-muted-foreground">
                            Ready to begin? Mae will guide you through each step.
                          </p>
                          <motion.div
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            <Button
                              size="lg"
                              onClick={() => setShowOnboardingForm(true)}
                              className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 shadow-lg shadow-primary/25"
                            >
                              <Sparkles className="h-5 w-5 mr-2" />
                              Start Setup
                              <ChevronRight className="h-5 w-5 ml-2" />
                            </Button>
                          </motion.div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

        </div>
      </div>
    </div>
  )
}
