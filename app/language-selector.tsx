import { Globe } from 'lucide-react';
import { useEffect, useState } from 'react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export function LanguageSelector() {
  const [mounted, setMounted] = useState(false);
  const [currentLanguage, setCurrentLanguage] = useState('en');

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return null;

  const handleLanguageChange = async (lang: string) => {
    setCurrentLanguage(lang);
    // Here we would integrate with Google Translate API
    // This requires setting up Google Cloud Translation API
    // and adding the API key securely through environment variables
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button className="relative inline-flex items-center justify-center rounded-md p-2 text-current transition-colors hover:text-purple-400">
          <Globe className="h-5 w-5" style={{ animation: 'spin 3s linear infinite' }} />
        </button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => handleLanguageChange('en')}>🇺🇸 English</DropdownMenuItem>
        <DropdownMenuItem onClick={() => handleLanguageChange('es')}>🇪🇸 Español</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
