"use client"

import { useAuth, useUser, useClerk } from "@clerk/nextjs"
import { useState, useEffect } from "react"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"

export default function TestClerkPage() {
  const { isLoaded: authLoaded, userId, sessionId, getToken } = useAuth()
  const { isLoaded: userLoaded, isSignedIn, user } = useUser()
  const clerk = useClerk()
  const [clerkInfo, setClerkInfo] = useState<any>(null)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (clerk) {
      try {
        // Get Clerk instance info
        const info = {
          version: clerk.version,
          // @ts-ignore
          domain: clerk.domain || "Not set",
          // @ts-ignore
          frontendApi: clerk.frontendApi || "Not set",
          loaded: clerk.loaded,
        }
        setClerkInfo(info)
      } catch (err) {
        setError(err instanceof Error ? err.message : "Unknown error")
      }
    }
  }, [clerk])

  const testAuth = async () => {
    try {
      const token = await getToken()
      alert(`Token retrieved successfully: ${token ? 'Yes' : 'No'}`)
    } catch (err) {
      alert(`Error getting token: ${err instanceof Error ? err.message : 'Unknown error'}`)
    }
  }

  return (
    <div className="container mx-auto p-8 max-w-4xl">
      <h1 className="text-3xl font-bold mb-8">Clerk Configuration Test</h1>
      
      <div className="space-y-6">
        {/* Environment Info */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Environment Configuration</h2>
          <div className="space-y-2 font-mono text-sm">
            <div>
              <span className="text-muted-foreground">NEXT_PUBLIC_CLERK_DOMAIN:</span>{" "}
              <span className="text-primary">{process.env.NEXT_PUBLIC_CLERK_DOMAIN || "Not set"}</span>
            </div>
            <div>
              <span className="text-muted-foreground">NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY:</span>{" "}
              <span className="text-primary">
                {process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY 
                  ? `${process.env.NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY.substring(0, 20)}...` 
                  : "Not set"}
              </span>
            </div>
            <div>
              <span className="text-muted-foreground">App URL:</span>{" "}
              <span className="text-primary">
                {typeof window !== 'undefined' ? window.location.origin : 'Server Render'}
              </span>
            </div>
          </div>
        </Card>

        {/* Clerk Instance Info */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Clerk Instance</h2>
          {clerkInfo ? (
            <div className="space-y-2 font-mono text-sm">
              <div>
                <span className="text-muted-foreground">Version:</span>{" "}
                <span className="text-primary">{clerkInfo.version}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Domain:</span>{" "}
                <span className="text-primary">{clerkInfo.domain}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Frontend API:</span>{" "}
                <span className="text-primary">{clerkInfo.frontendApi}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Loaded:</span>{" "}
                <span className={clerkInfo.loaded ? "text-green-500" : "text-red-500"}>
                  {clerkInfo.loaded ? "✓ Yes" : "✗ No"}
                </span>
              </div>
            </div>
          ) : (
            <p className="text-muted-foreground">Loading Clerk instance...</p>
          )}
          {error && (
            <div className="mt-4 p-3 bg-red-50 dark:bg-red-950 text-red-600 dark:text-red-400 rounded">
              Error: {error}
            </div>
          )}
        </Card>

        {/* Auth Status */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Authentication Status</h2>
          <div className="space-y-2 font-mono text-sm">
            <div>
              <span className="text-muted-foreground">Auth Loaded:</span>{" "}
              <span className={authLoaded ? "text-green-500" : "text-yellow-500"}>
                {authLoaded ? "✓ Yes" : "⏳ Loading..."}
              </span>
            </div>
            <div>
              <span className="text-muted-foreground">User Loaded:</span>{" "}
              <span className={userLoaded ? "text-green-500" : "text-yellow-500"}>
                {userLoaded ? "✓ Yes" : "⏳ Loading..."}
              </span>
            </div>
            <div>
              <span className="text-muted-foreground">Signed In:</span>{" "}
              <span className={isSignedIn ? "text-green-500" : "text-red-500"}>
                {isSignedIn ? "✓ Yes" : "✗ No"}
              </span>
            </div>
            <div>
              <span className="text-muted-foreground">User ID:</span>{" "}
              <span className="text-primary">{userId || "None"}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Session ID:</span>{" "}
              <span className="text-primary">{sessionId || "None"}</span>
            </div>
          </div>
        </Card>

        {/* User Info */}
        {user && (
          <Card className="p-6">
            <h2 className="text-xl font-semibold mb-4">User Information</h2>
            <div className="space-y-2 font-mono text-sm">
              <div>
                <span className="text-muted-foreground">Email:</span>{" "}
                <span className="text-primary">{user.primaryEmailAddress?.emailAddress || "None"}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Name:</span>{" "}
                <span className="text-primary">{user.fullName || "Not set"}</span>
              </div>
              <div>
                <span className="text-muted-foreground">Created:</span>{" "}
                <span className="text-primary">
                  {user.createdAt ? new Date(user.createdAt).toLocaleString() : "Unknown"}
                </span>
              </div>
            </div>
          </Card>
        )}

        {/* Actions */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Test Actions</h2>
          <div className="flex gap-4">
            <Button onClick={testAuth} disabled={!isSignedIn}>
              Test Get Token
            </Button>
            {isSignedIn ? (
              <Button onClick={() => clerk.signOut()} variant="outline">
                Sign Out
              </Button>
            ) : (
              <Button onClick={() => clerk.openSignIn()} variant="outline">
                Sign In
              </Button>
            )}
          </div>
        </Card>

        {/* DNS Status Check */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">DNS Configuration</h2>
          <div className="space-y-2 text-sm">
            <p className="text-muted-foreground">
              Your Clerk custom domain DNS records appear to be configured correctly.
            </p>
            <div className="font-mono">
              <div className="text-green-500">✓ clerk.our-kidz.com → frontend-api.clerk.services</div>
              <div className="text-green-500">✓ accounts.our-kidz.com → accounts.clerk.services</div>
              <div className="text-green-500">✓ Email DKIM records configured</div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  )
}