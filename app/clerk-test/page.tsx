"use client"

import { useAuth, useUser } from "@clerk/nextjs"
import Link from "next/link"
import { Button } from "@/components/ui/button"

export default function ClerkTestPage() {
  const { isLoaded, userId, sessionId } = useAuth()
  const { user } = useUser()

  return (
    <div className="container mx-auto p-8 max-w-2xl">
      <h1 className="text-2xl font-bold mb-6">Clerk Status Check</h1>
      
      <div className="space-y-4">
        <div className="p-4 border rounded">
          <h2 className="font-semibold mb-2">Environment</h2>
          <p className="text-sm text-muted-foreground">
            Domain: {process.env.NEXT_PUBLIC_CLERK_DOMAIN || "Not set"}
          </p>
          <p className="text-sm text-muted-foreground">
            URL: {typeof window !== 'undefined' ? window.location.origin : 'SSR'}
          </p>
        </div>

        <div className="p-4 border rounded">
          <h2 className="font-semibold mb-2">Auth Status</h2>
          <p>Loaded: {isLoaded ? '✅ Yes' : '⏳ Loading'}</p>
          <p>User ID: {userId || 'Not signed in'}</p>
          <p>Session: {sessionId || 'No session'}</p>
        </div>

        {user && (
          <div className="p-4 border rounded">
            <h2 className="font-semibold mb-2">User Info</h2>
            <p>Email: {user.primaryEmailAddress?.emailAddress}</p>
            <p>Name: {user.fullName || user.firstName || 'Not set'}</p>
          </div>
        )}

        <div className="flex gap-4">
          <Link href="/auth">
            <Button>Go to Auth Page</Button>
          </Link>
          <Link href="/">
            <Button variant="outline">Go Home</Button>
          </Link>
        </div>
      </div>
    </div>
  )
}