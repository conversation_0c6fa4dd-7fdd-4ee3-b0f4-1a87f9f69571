'use client'

import { useUser, useAuth } from '@clerk/nextjs'
import { useState, useEffect } from 'react'
import { fetchUserByClerkId } from '@/lib/clerk-supabase-client'

export default function TestIntegrationPage() {
  const { user, isLoaded: clerkLoaded } = useUser()
  const { getToken } = useAuth()
  const [supabaseUser, setSupabaseUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [syncing, setSyncing] = useState(false)
  const [syncResult, setSyncResult] = useState<any>(null)

  useEffect(() => {
    async function fetchSupabaseUser() {
      if (!clerkLoaded || !user) {
        setLoading(false)
        return
      }

      try {
        // Try to fetch user by clerk_id
        const data = await fetchUserByClerkId(user.id)
        setSupabaseUser(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setLoading(false)
      }
    }

    fetchSupabaseUser()
  }, [user, clerkLoaded, getToken])

  const handleManualSync = async () => {
    setSyncing(true)
    setSyncResult(null)
    setError(null)

    try {
      const response = await fetch('/api/sync-user')
      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'Sync failed')
      }

      setSyncResult(data)
      
      // Refresh Supabase user data after sync
      try {
        const updatedUser = await fetchUserByClerkId(user?.id!)
        setSupabaseUser(updatedUser)
      } catch (err) {
        // Ignore fetch errors after sync, the sync result is what matters
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Sync failed')
    } finally {
      setSyncing(false)
    }
  }

  if (!clerkLoaded || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading integration status...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md">
          <h2 className="text-red-800 font-semibold mb-2">Error</h2>
          <p className="text-red-600">{error}</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Not Signed In</h1>
          <p className="text-gray-600 mb-4">Please sign in to test the integration</p>
          <a href="/sign-in" className="bg-teal-600 text-white px-6 py-2 rounded-lg hover:bg-teal-700">
            Sign In
          </a>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-3xl font-bold mb-8">Clerk-Supabase Integration Test</h1>
        
        <div className="grid md:grid-cols-2 gap-6">
          {/* Clerk User Info */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4 text-blue-600">Clerk User</h2>
            <div className="space-y-2">
              <div>
                <span className="font-medium">ID:</span>
                <span className="ml-2 text-sm font-mono bg-gray-100 px-2 py-1 rounded">{user.id}</span>
              </div>
              <div>
                <span className="font-medium">Email:</span>
                <span className="ml-2">{user.emailAddresses[0]?.emailAddress}</span>
              </div>
              <div>
                <span className="font-medium">Name:</span>
                <span className="ml-2">{user.fullName || 'Not set'}</span>
              </div>
              <div>
                <span className="font-medium">Created:</span>
                <span className="ml-2">{new Date(user.createdAt!).toLocaleDateString()}</span>
              </div>
            </div>
          </div>

          {/* Supabase User Info */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold mb-4 text-green-600">Supabase User</h2>
            {supabaseUser ? (
              <div className="space-y-2">
                <div>
                  <span className="font-medium">ID:</span>
                  <span className="ml-2 text-sm font-mono bg-gray-100 px-2 py-1 rounded">{supabaseUser.id}</span>
                </div>
                <div>
                  <span className="font-medium">Clerk ID:</span>
                  <span className="ml-2 text-sm font-mono bg-gray-100 px-2 py-1 rounded">{supabaseUser.clerk_id}</span>
                </div>
                <div>
                  <span className="font-medium">Auth User ID:</span>
                  <span className="ml-2 text-sm font-mono bg-gray-100 px-2 py-1 rounded">
                    {supabaseUser.auth_user_id || 'Not linked'}
                  </span>
                </div>
                <div>
                  <span className="font-medium">Email:</span>
                  <span className="ml-2">{supabaseUser.email}</span>
                </div>
                <div>
                  <span className="font-medium">Name:</span>
                  <span className="ml-2">{supabaseUser.name}</span>
                </div>
                <div>
                  <span className="font-medium">Onboarding:</span>
                  <span className="ml-2">
                    {supabaseUser.onboarding_completed ? (
                      <span className="text-green-600">✓ Completed</span>
                    ) : (
                      <span className="text-yellow-600">Step: {supabaseUser.onboarding_step}</span>
                    )}
                  </span>
                </div>
              </div>
            ) : (
              <div className="text-red-600">
                <p className="font-semibold mb-2">⚠️ User not found in Supabase</p>
                <p className="text-sm text-gray-600 mb-4">
                  The webhook may not have fired yet. Click the button below to manually sync.
                </p>
                <button
                  onClick={handleManualSync}
                  disabled={syncing}
                  className="bg-teal-600 text-white px-4 py-2 rounded-lg hover:bg-teal-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {syncing ? 'Syncing...' : 'Manual Sync'}
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Sync Result */}
        {syncResult && (
          <div className={`mt-6 ${syncResult.status === 'created' || syncResult.status === 'updated' ? 'bg-green-50 border-green-200' : 'bg-blue-50 border-blue-200'} border rounded-lg p-6`}>
            <h3 className={`font-semibold mb-2 ${syncResult.status === 'created' || syncResult.status === 'updated' ? 'text-green-800' : 'text-blue-800'}`}>
              Sync Result: {syncResult.status}
            </h3>
            <p className={syncResult.status === 'created' || syncResult.status === 'updated' ? 'text-green-700' : 'text-blue-700'}>
              {syncResult.message}
            </p>
            {syncResult.user && (
              <div className="mt-4">
                <p className="text-sm font-medium">User ID: {syncResult.user.id}</p>
                <p className="text-sm">Clerk ID: {syncResult.user.clerk_id}</p>
              </div>
            )}
          </div>
        )}

        {/* Integration Status */}
        <div className="mt-6 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold mb-4">Integration Status</h2>
          <div className="space-y-3">
            <div className="flex items-center">
              <span className={`w-3 h-3 rounded-full mr-3 ${user ? 'bg-green-500' : 'bg-red-500'}`}></span>
              <span>Clerk Authentication: {user ? 'Connected' : 'Not Connected'}</span>
            </div>
            <div className="flex items-center">
              <span className={`w-3 h-3 rounded-full mr-3 ${supabaseUser ? 'bg-green-500' : 'bg-yellow-500'}`}></span>
              <span>Supabase User Record: {supabaseUser ? 'Synced' : 'Not Synced'}</span>
            </div>
            <div className="flex items-center">
              <span className={`w-3 h-3 rounded-full mr-3 ${supabaseUser?.clerk_id === user?.id ? 'bg-green-500' : 'bg-red-500'}`}></span>
              <span>Clerk ID Match: {supabaseUser?.clerk_id === user?.id ? 'Verified' : 'Mismatch'}</span>
            </div>
            <div className="flex items-center">
              <span className={`w-3 h-3 rounded-full mr-3 ${supabaseUser?.auth_user_id ? 'bg-green-500' : 'bg-yellow-500'}`}></span>
              <span>Auth User Link: {supabaseUser?.auth_user_id ? 'Linked' : 'Not Linked'}</span>
            </div>
          </div>
        </div>

        {/* Manual Sync Instructions */}
        {!supabaseUser && user && (
          <div className="mt-6 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h3 className="font-semibold text-yellow-800 mb-2">Manual Sync Required</h3>
            <p className="text-yellow-700 mb-4">
              To manually sync this user, run the following SQL in your Supabase dashboard:
            </p>
            <pre className="bg-white border border-yellow-300 rounded p-3 text-sm overflow-x-auto">
              <code>{`UPDATE public.users 
SET clerk_id = '${user.id}'
WHERE email = '${user.emailAddresses[0]?.emailAddress}';`}</code>
            </pre>
            <p className="text-yellow-700 mt-4 text-sm">
              Or if the user doesn't exist yet:
            </p>
            <pre className="bg-white border border-yellow-300 rounded p-3 text-sm overflow-x-auto">
              <code>{`INSERT INTO public.users (clerk_id, email, name, role, onboarding_completed, onboarding_step)
VALUES ('${user.id}', '${user.emailAddresses[0]?.emailAddress}', '${user.fullName || 'User'}', 'parent', false, 'welcome');`}</code>
            </pre>
          </div>
        )}
      </div>
    </div>
  )
}