export default function DebugEnvPage() {
  return (
    <div className="container mx-auto p-6 max-w-4xl">
      <div className="bg-white rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold mb-4">Environment Debug</h1>
        <p className="text-gray-600">This page is temporarily simplified to resolve build issues.</p>
        
        <div className="mt-6">
          <h2 className="text-lg font-semibold mb-2">Environment Status:</h2>
          <div className="space-y-2">
            <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
              <span>NODE_ENV</span>
              <span className="font-mono text-sm">{process.env.NODE_ENV || 'Not set'}</span>
            </div>
            <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
              <span>NEXT_PUBLIC_SUPABASE_URL</span>
              <span className="font-mono text-sm">
                {process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Set' : 'Not set'}
              </span>
            </div>
            <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
              <span>NEXT_PUBLIC_SUPABASE_ANON_KEY</span>
              <span className="font-mono text-sm">
                {process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Not set'}
              </span>
            </div>
          </div>
        </div>
        
        <div className="mt-6">
          <p className="text-sm text-gray-500">
            Server-side environment variables are not accessible in this client-side context.
          </p>
        </div>
      </div>
    </div>
  )
} 