"use client"

import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Mail, Calendar, User, MessageSquare, ExternalLink, RefreshCw } from "lucide-react"

interface EmailInteraction {
  id: number
  session_id: string
  user_email: string
  question: string
  response: string
  sources: { title: string; url: string }[]
  email_details: {
    message_id: string
    subject: string
    sent_at: string
    sources_count: number
  }
  created_at: string
  updated_at: string
}

interface ApiResponse {
  success: boolean
  data: EmailInteraction[]
  pagination: {
    total: number
    limit: number
    offset: number
    has_more: boolean
  }
  filters: {
    email?: string
    start_date?: string
    end_date?: string
  }
}

export default function EmailInteractionsPage() {
  const [interactions, setInteractions] = useState<EmailInteraction[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [filters, setFilters] = useState({
    email: '',
    start_date: '',
    end_date: '',
    limit: 20,
    offset: 0
  })
  const [pagination, setPagination] = useState({
    total: 0,
    limit: 20,
    offset: 0,
    has_more: false
  })

  const fetchInteractions = async () => {
    setLoading(true)
    setError('')
    
    try {
      const params = new URLSearchParams()
      if (filters.email) params.append('email', filters.email)
      if (filters.start_date) params.append('start_date', filters.start_date)
      if (filters.end_date) params.append('end_date', filters.end_date)
      params.append('limit', filters.limit.toString())
      params.append('offset', filters.offset.toString())

      const response = await fetch(`/api/email-interactions?${params.toString()}`)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data: ApiResponse = await response.json()
      
      if (data.success) {
        setInteractions(data.data)
        setPagination(data.pagination)
      } else {
        setError('Failed to fetch email interactions')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error occurred')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchInteractions()
  }, [filters.offset])

  const handleSearch = () => {
    setFilters(prev => ({ ...prev, offset: 0 }))
    fetchInteractions()
  }

  const handleNextPage = () => {
    if (pagination.has_more) {
      setFilters(prev => ({ ...prev, offset: prev.offset + prev.limit }))
    }
  }

  const handlePrevPage = () => {
    if (pagination.offset > 0) {
      setFilters(prev => ({ ...prev, offset: Math.max(0, prev.offset - prev.limit) }))
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const truncateText = (text: string, maxLength: number = 150) => {
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-foreground mb-2">Mae&apos;s Email Interactions</h1>
        <p className="text-muted-foreground">
          Monitor and analyze email conversations sent by Mae AI assistant
        </p>
      </div>

      {/* Filters */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg">Filters</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="text-sm font-medium mb-2 block">Email</label>
              <Input
                placeholder="Search by email..."
                value={filters.email}
                onChange={(e) => setFilters(prev => ({ ...prev, email: e.target.value }))}
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">Start Date</label>
              <Input
                type="date"
                value={filters.start_date}
                onChange={(e) => setFilters(prev => ({ ...prev, start_date: e.target.value }))}
              />
            </div>
            <div>
              <label className="text-sm font-medium mb-2 block">End Date</label>
              <Input
                type="date"
                value={filters.end_date}
                onChange={(e) => setFilters(prev => ({ ...prev, end_date: e.target.value }))}
              />
            </div>
            <div className="flex items-end gap-2">
              <Button onClick={handleSearch} className="flex-1">
                Search
              </Button>
              <Button variant="outline" onClick={fetchInteractions}>
                <RefreshCw className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <Mail className="h-5 w-5 text-primary" />
              <div>
                <p className="text-sm text-muted-foreground">Total Interactions</p>
                <p className="text-2xl font-bold">{pagination.total}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <User className="h-5 w-5 text-primary" />
              <div>
                <p className="text-sm text-muted-foreground">Unique Users</p>
                <p className="text-2xl font-bold">
                  {new Set(interactions.map(i => i.user_email)).size}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-2">
              <ExternalLink className="h-5 w-5 text-primary" />
              <div>
                <p className="text-sm text-muted-foreground">Avg Sources</p>
                <p className="text-2xl font-bold">
                  {interactions.length > 0 
                    ? Math.round(interactions.reduce((sum, i) => sum + i.email_details.sources_count, 0) / interactions.length)
                    : 0
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Error State */}
      {error && (
        <Card className="mb-6 border-destructive">
          <CardContent className="p-6">
            <p className="text-destructive">Error: {error}</p>
          </CardContent>
        </Card>
      )}

      {/* Loading State */}
      {loading && (
        <Card>
          <CardContent className="p-6 text-center">
            <RefreshCw className="h-6 w-6 animate-spin mx-auto mb-2" />
            <p>Loading email interactions...</p>
          </CardContent>
        </Card>
      )}

      {/* Interactions List */}
      {!loading && interactions.length === 0 && (
        <Card>
          <CardContent className="p-6 text-center">
            <Mail className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No email interactions found</p>
          </CardContent>
        </Card>
      )}

      {!loading && interactions.length > 0 && (
        <div className="space-y-4">
          {interactions.map((interaction) => (
            <Card key={interaction.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <User className="h-4 w-4" />
                      {interaction.user_email}
                    </CardTitle>
                    <CardDescription className="flex items-center gap-4 mt-1">
                      <span className="flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {formatDate(interaction.created_at)}
                      </span>
                      <Badge variant="secondary">
                        {interaction.email_details.sources_count} sources
                      </Badge>
                    </CardDescription>
                  </div>
                  <Badge variant="outline">
                    ID: {interaction.session_id.substring(0, 8)}...
                  </Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-1 flex items-center gap-1">
                      <MessageSquare className="h-3 w-3" />
                      Question
                    </h4>
                    <p className="text-sm bg-muted p-3 rounded">
                      {truncateText(interaction.question)}
                    </p>
                  </div>
                  
                  <div>
                    <h4 className="font-medium text-sm text-muted-foreground mb-1">
                      Mae&apos;s Response
                    </h4>
                    <p className="text-sm bg-primary/5 p-3 rounded">
                      {truncateText(interaction.response)}
                    </p>
                  </div>

                  {interaction.sources.length > 0 && (
                    <div>
                      <h4 className="font-medium text-sm text-muted-foreground mb-2">
                        Sources ({interaction.sources.length})
                      </h4>
                      <div className="flex flex-wrap gap-2">
                        {interaction.sources.slice(0, 3).map((source, idx) => (
                          <a
                            key={idx}
                            href={source.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-xs bg-secondary hover:bg-secondary/80 px-2 py-1 rounded flex items-center gap-1"
                          >
                            <ExternalLink className="h-3 w-3" />
                            {truncateText(source.title, 30)}
                          </a>
                        ))}
                        {interaction.sources.length > 3 && (
                          <Badge variant="outline" className="text-xs">
                            +{interaction.sources.length - 3} more
                          </Badge>
                        )}
                      </div>
                    </div>
                  )}

                  <div className="text-xs text-muted-foreground pt-2 border-t">
                    <p>Email Subject: {interaction.email_details.subject}</p>
                    <p>Message ID: {interaction.email_details.message_id}</p>
                    <p>Sent: {formatDate(interaction.email_details.sent_at)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Pagination */}
      {!loading && interactions.length > 0 && (
        <div className="flex justify-between items-center mt-6">
          <p className="text-sm text-muted-foreground">
            Showing {pagination.offset + 1} to {Math.min(pagination.offset + pagination.limit, pagination.total)} of {pagination.total} interactions
          </p>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              onClick={handlePrevPage}
              disabled={pagination.offset === 0}
            >
              Previous
            </Button>
            <Button 
              variant="outline" 
              onClick={handleNextPage}
              disabled={!pagination.has_more}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
