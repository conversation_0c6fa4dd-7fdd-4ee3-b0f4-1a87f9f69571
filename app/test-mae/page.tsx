"use client"

import React, { useState } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  handleCollectUserInfo, 
  handleAddFamilyMember, 
  handleCompleteOnboarding,
  handleNavigateToOnboarding
} from '@/lib/onboarding-function-tools'

/**
 * Test page for <PERSON>'s onboarding function tools
 * This page allows manual testing of the function tools to ensure they work correctly
 */
export default function TestMaePage() {
  const [testEmail, setTestEmail] = useState('<EMAIL>')
  const [results, setResults] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(false)

  const addResult = (result: any) => {
    setResults(prev => [...prev, { timestamp: new Date().toISOString(), ...result }])
  }

  const testCollectUserInfo = async () => {
    setIsLoading(true)
    try {
      const result = await handleCollectUserInfo({
        field: 'name',
        value: '<PERSON>',
        user_email: testEmail
      })
      addResult({ test: 'Collect User Info', result })
    } catch (error) {
      addResult({ test: 'Collect User Info', error: error.message })
    }
    setIsLoading(false)
  }

  const testCollectEmail = async () => {
    setIsLoading(true)
    try {
      const result = await handleCollectUserInfo({
        field: 'email',
        value: testEmail,
        user_email: testEmail
      })
      addResult({ test: 'Collect Email', result })
    } catch (error) {
      addResult({ test: 'Collect Email', error: error.message })
    }
    setIsLoading(false)
  }

  const testAddFamilyMember = async () => {
    setIsLoading(true)
    try {
      const result = await handleAddFamilyMember({
        name: 'Emma Doe',
        date_of_birth: '2018-05-15',
        relationship: 'daughter',
        user_email: testEmail
      })
      addResult({ test: 'Add Family Member', result })
    } catch (error) {
      addResult({ test: 'Add Family Member', error: error.message })
    }
    setIsLoading(false)
  }

  const testAddFamilyMemberWithoutUser = async () => {
    setIsLoading(true)
    try {
      const result = await handleAddFamilyMember({
        name: 'Test Child',
        date_of_birth: '2020-01-01',
        relationship: 'child',
        user_email: '<EMAIL>'
      })
      addResult({ test: 'Add Family Member (No User)', result })
    } catch (error) {
      addResult({ test: 'Add Family Member (No User)', error: error.message })
    }
    setIsLoading(false)
  }

  const testCompleteOnboarding = async () => {
    setIsLoading(true)
    try {
      const result = await handleCompleteOnboarding({
        user_email: testEmail,
        send_welcome_email: false
      })
      addResult({ test: 'Complete Onboarding', result })
    } catch (error) {
      addResult({ test: 'Complete Onboarding', error: error.message })
    }
    setIsLoading(false)
  }

  const testNavigation = async () => {
    setIsLoading(true)
    try {
      const result = await handleNavigateToOnboarding({
        message: 'Testing navigation from test page'
      })
      addResult({ test: 'Navigate to Onboarding', result })
    } catch (error) {
      addResult({ test: 'Navigate to Onboarding', error: error.message })
    }
    setIsLoading(false)
  }

  const clearResults = () => {
    setResults([])
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Mae Function Tools Test Page</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="testEmail">Test Email</Label>
            <Input
              id="testEmail"
              value={testEmail}
              onChange={(e) => setTestEmail(e.target.value)}
              placeholder="Enter test email"
            />
          </div>
          
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <Button onClick={testCollectEmail} disabled={isLoading}>
              Test Collect Email
            </Button>
            <Button onClick={testCollectUserInfo} disabled={isLoading}>
              Test Collect Name
            </Button>
            <Button onClick={testAddFamilyMember} disabled={isLoading}>
              Test Add Family Member
            </Button>
            <Button onClick={testAddFamilyMemberWithoutUser} disabled={isLoading} variant="outline">
              Test Add Family (No User)
            </Button>
            <Button onClick={testCompleteOnboarding} disabled={isLoading}>
              Test Complete Onboarding
            </Button>
            <Button onClick={testNavigation} disabled={isLoading}>
              Test Navigation
            </Button>
            <Button onClick={clearResults} variant="outline">
              Clear Results
            </Button>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Test Results</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4 max-h-96 overflow-y-auto">
            {results.length === 0 ? (
              <p className="text-muted-foreground">No test results yet. Run some tests above.</p>
            ) : (
              results.map((result, index) => (
                <div key={index} className="border rounded p-3 space-y-2">
                  <div className="flex justify-between items-center">
                    <strong>{result.test}</strong>
                    <span className="text-sm text-muted-foreground">
                      {new Date(result.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                  <pre className="text-sm bg-muted p-2 rounded overflow-x-auto">
                    {JSON.stringify(result.result || result.error, null, 2)}
                  </pre>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2">
          <p><strong>Test Order:</strong></p>
          <ol className="list-decimal list-inside space-y-1">
            <li>First test "Collect Email" to create the user with email</li>
            <li>Then test "Collect Name" to add the name (this should trigger database save)</li>
            <li>Test "Add Family Member" to add a child</li>
            <li>Test "Complete Onboarding" to mark onboarding as complete</li>
            <li>Test "Navigation" to verify page navigation works</li>
          </ol>
          <p className="text-sm text-muted-foreground mt-4">
            Check the browser console for detailed logs and the database for saved data.
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
