"use client"

import type React from "react"
import { useState } from "react"
import Image from "next/image"
import { MapPin, Phone, Mail, Heart, Clock, Shield, Send } from "lucide-react"
import { Header } from "@/components/header"
import Footer from "@/components/footer"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import confetti from "canvas-confetti"

export default function ContactPage() {
  const { toast } = useToast()
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    zipCode: "",
    phone: "",
    country: "",
    subject: "",
    message: "",
    childrenAges: "",
  })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }))
  }

  const triggerConfetti = () => {
    try {
      // Create a burst of confetti from multiple angles
      const duration = 3000
      const animationEnd = Date.now() + duration
      const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 0 }

      function randomInRange(min: number, max: number) {
        return Math.random() * (max - min) + min
      }

      const interval: NodeJS.Timeout = setInterval(function() {
        const timeLeft = animationEnd - Date.now()

        if (timeLeft <= 0) {
          clearInterval(interval)
          return
        }

        const particleCount = Math.max(1, Math.floor(50 * (timeLeft / duration)))

        // Left side
        confetti({
          ...defaults,
          particleCount,
          origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 }
        })

        // Right side
        confetti({
          ...defaults,
          particleCount,
          origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 }
        })
      }, 250)
    } catch (error) {
      console.error('Confetti error:', error)
      // Fail silently if confetti doesn't work
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Submit to Supabase database
      const response = await fetch('/api/contact-form', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          contactData: {
            first_name: formData.firstName,
            last_name: formData.lastName,
            email: formData.email,
            zip_code: formData.zipCode,
            phone: formData.phone || null,
            country: formData.country || null,
            children_ages: formData.childrenAges || null,
            subject: formData.subject,
            message: formData.message,
          }
        }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const result = await response.json()
      console.log("Form submitted successfully:", result)
      setIsSubmitted(true)

      // Trigger confetti celebration
      triggerConfetti()

      // Show success toast
      toast({
        title: "🎉 Message Sent Successfully!",
        description: "Thank you for reaching out! We'll get back to you within 24 hours.",
        duration: 5000,
      })

      // Reset form after successful submission
      setTimeout(() => {
        setIsSubmitted(false)
        setFormData({
          firstName: "",
          lastName: "",
          email: "",
          zipCode: "",
          phone: "",
          country: "",
          subject: "",
          message: "",
          childrenAges: "",
        })
      }, 3000)
    } catch (error) {
      console.error("Form submission error:", error)

      // Show error toast with more detailed information
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred"
      toast({
        title: "❌ Submission Failed",
        description: `Something went wrong: ${errorMessage}. Please try again or contact us directly.`,
        variant: "destructive",
        duration: 5000,
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <main className="min-h-screen flex flex-col">
      <Header />

      {/* Hero Section */}
      <section className="relative h-[400px] md:h-[500px] w-full overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-primary/10 to-background" />
        <Image
          src="/mother-child-care.png"
          alt="Healthcare professional with family"
          fill
          className="object-cover object-center opacity-50"
          priority
        />
        <div className="absolute inset-0 z-20 flex items-center">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl text-center mx-auto space-y-6">
              <Badge variant="outline" className="mb-4 bg-gradient-to-r from-teal-500 to-blue-400 text-white w-fit h-8 mx-auto px-10 mb-10 text-md border-gradient-to-r from-teal-500 to-blue-400 font-inter font-medium">
                Get in Touch
              </Badge>
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-6">
                We&apos;re Here to Help Your Family
              </h1>
              <p className="text-lg md:text-xl max-w-3xl mx-auto leading-relaxed">
                Have questions about pediatric care? Need support with our platform?
                Our healthcare-focused team is ready to assist you every step of the way.
              </p>
              <div className="flex items-center justify-center space-x-8 pt-4">
                <div className="flex items-center space-x-2 text-sm ">
                  <Clock className="h-4 w-4 text-primary" />
                  <span>24/7 Support</span>
                </div>
                <div className="flex items-center space-x-2 text-sm ">
                  <Shield className="h-4 w-4 text-primary" />
                  <span>HIPAA Compliant</span>
                </div>
                <div className="flex items-center space-x-2 text-sm ">
                  <Heart className="h-4 w-4 text-primary" />
                  <span>Family Focused</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form and Info Section */}
      <section className="py-16 md:py-24 bg-background">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <Card className="healthcare-card">
              <CardHeader>
                <CardTitle className="text-2xl md:text-3xl">Send us a message</CardTitle>
                <CardDescription className="text-base">
                  Tell us about your family&apos;s needs and how we can help provide the best pediatric care guidance.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label htmlFor="firstName" className="text-sm font-medium text-foreground">
                        First Name *
                      </label>
                      <Input
                        type="text"
                        id="firstName"
                        name="firstName"
                        required
                        value={formData.firstName}
                        onChange={handleInputChange}
                        placeholder="Enter your first name"
                        className="focus:ring-primary focus:border-primary"
                      />
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="lastName" className="text-sm font-medium text-foreground">
                        Last Name *
                      </label>
                      <Input
                        type="text"
                        id="lastName"
                        name="lastName"
                        required
                        value={formData.lastName}
                        onChange={handleInputChange}
                        placeholder="Enter your last name"
                        className="focus:ring-primary focus:border-primary"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="email" className="text-sm font-medium text-foreground">
                      Email Address *
                    </label>
                    <Input
                      type="email"
                      id="email"
                      name="email"
                      required
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder="<EMAIL>"
                      className="focus:ring-primary focus:border-primary"
                    />
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label htmlFor="zipCode" className="text-sm font-medium text-foreground">
                        Zip Code *
                      </label>
                      <Input
                        type="text"
                        id="zipCode"
                        name="zipCode"
                        required
                        value={formData.zipCode}
                        onChange={handleInputChange}
                        placeholder="12345"
                        className="focus:ring-primary focus:border-primary"
                      />
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="phone" className="text-sm font-medium text-foreground">
                        Phone Number
                      </label>
                      <Input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        placeholder="(*************"
                        className="focus:ring-primary focus:border-primary"
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <label htmlFor="country" className="text-sm font-medium text-foreground">
                        Country
                      </label>
                      <Select value={formData.country} onValueChange={(value) => setFormData(prev => ({ ...prev, country: value }))}>
                        <SelectTrigger className="focus:ring-primary focus:border-primary">
                          <SelectValue placeholder="Select a country" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="US">United States</SelectItem>
                          <SelectItem value="CA">Canada</SelectItem>
                          <SelectItem value="UK">United Kingdom</SelectItem>
                          <SelectItem value="DE">Germany</SelectItem>
                          <SelectItem value="FR">France</SelectItem>
                          <SelectItem value="CH">Switzerland</SelectItem>
                          <SelectItem value="AU">Australia</SelectItem>
                          <SelectItem value="other">Other</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <label htmlFor="childrenAges" className="text-sm font-medium text-foreground">
                        Children&apos;s Ages
                      </label>
                      <Input
                        type="text"
                        id="childrenAges"
                        name="childrenAges"
                        value={formData.childrenAges}
                        onChange={handleInputChange}
                        placeholder="e.g., 3, 7, 12"
                        className="focus:ring-primary focus:border-primary"
                      />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="subject" className="text-sm font-medium text-foreground">
                      How can we help? *
                    </label>
                    <Select value={formData.subject} onValueChange={(value) => setFormData(prev => ({ ...prev, subject: value }))}>
                      <SelectTrigger className="focus:ring-primary focus:border-primary">
                        <SelectValue placeholder="Select how we can help" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ai-assistant">AI Assistant Questions</SelectItem>
                        <SelectItem value="demo-request">Schedule a Demo</SelectItem>
                        <SelectItem value="early-access">Early Access Program</SelectItem>
                        <SelectItem value="healthcare-provider">Healthcare Provider Partnership</SelectItem>
                        <SelectItem value="technical-support">Technical Support</SelectItem>
                        <SelectItem value="privacy-security">Privacy & Security Questions</SelectItem>
                        <SelectItem value="billing">Billing & Pricing</SelectItem>
                        <SelectItem value="media">Media Inquiry</SelectItem>
                        <SelectItem value="careers">Careers</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <label htmlFor="message" className="text-sm font-medium text-foreground">
                      Tell us more
                    </label>
                    <Textarea
                      id="message"
                      name="message"
                      required
                      rows={6}
                      value={formData.message}
                      onChange={handleInputChange}
                      placeholder="Tell us about your family's needs, questions about pediatric care, or how we can help..."
                      className="focus:ring-primary focus:border-primary resize-none"
                    />
                  </div>

                  <Button
                    type="submit"
                    disabled={isSubmitting || isSubmitted}
                    className="w-full btn-healthcare"
                    size="lg"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                        Sending...
                      </>
                    ) : isSubmitted ? (
                      <>
                        <Heart className="w-4 h-4 mr-2" />
                        Message Sent!
                      </>
                    ) : (
                      <>
                        <Send className="w-4 h-4 mr-2" />
                        Send Message
                      </>
                    )}
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <div className="space-y-8">
              {/* <div>
                <h2 className="text-2xl md:text-3xl font-bold mb-4">Ready to Transform Your Family's Healthcare Journey?</h2>
                <p className="text-lg text-muted-foreground mb-6">
                  Whether you have questions about our AI assistant, need support with our platform, or want to explore
                  partnership opportunities, our healthcare-focused team is here to help.
                </p>
                <div className="flex flex-wrap gap-4 mb-8">
                  <Badge variant="secondary" className="flex items-center space-x-1">
                    <Clock className="h-3 w-3" />
                    <span>Response within 24 hours</span>
                  </Badge>
                  <Badge variant="secondary" className="flex items-center space-x-1">
                    <Shield className="h-3 w-3" />
                    <span>HIPAA Compliant</span>
                  </Badge>
                  <Badge variant="secondary" className="flex items-center space-x-1">
                    <Heart className="h-3 w-3" />
                    <span>Family-First Support</span>
                  </Badge>
                </div>
              </div> */}

              <div className="space-y-6">
                <Card className="healthcare-card">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                        <MapPin className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg mb-1">Headquarters</h3>
                        <p className="text-muted-foreground">
                          Our Kidz Healthcare Platform
                          <br />
                          Boise, ID
                          <br />
                          United States
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="healthcare-card">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                        <Phone className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg mb-1">24/7 Support Line</h3>
                        <p className="text-muted-foreground">
                          <a href="tel:+18000URKIDZ" className="hover:text-primary transition-colors">
                            1-800-OUR-KIDZ
                          </a>
                        </p>
                        <p className="text-sm text-muted-foreground mt-1">
                          Available for urgent healthcare questions
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card className="healthcare-card">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-4">
                      <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center flex-shrink-0">
                        <Mail className="w-6 h-6 text-primary" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-lg mb-1">Email Support</h3>
                        <p className="text-muted-foreground">
                          <a href="mailto:<EMAIL>" className="hover:text-primary transition-colors">
                            <EMAIL>
                          </a>
                        </p>
                        <p className="text-sm text-muted-foreground mt-1">
                          General inquiries and support
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </main>
  )
}
