import { NextRequest } from 'next/server';

export async function POST(req: NextRequest) {
  try {
    const { query, location, radius } = await req.json();
    
    // Validate required parameters
    if (!query || !location) {
      return Response.json({ 
        error: 'Missing required parameters', 
        details: 'query and location are required' 
      }, { status: 400 });
    }
    
    const apiKey = process.env.GOOGLE_MAPS_API_KEY;
    
    if (!apiKey) {
      console.error('❌ Google Maps API key not found in environment variables');
      return Response.json({ error: 'Google Maps API key not configured' }, { status: 500 });
    }
    
    console.log('✅ Google Maps API key found:', apiKey.substring(0, 10) + '...');
    console.log('🔍 Testing API key with geocoding request...');

    console.log(`🔍 Searching for "${query}" near "${location}" within ${radius || 10} miles`);

    // First, geocode the location
    const geocodeUrl = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(location)}&key=${apiKey}`;
    console.log(`🌍 Geocoding location: "${location}"`);
    
    const geocodeResponse = await fetch(geocodeUrl);
    const geocodeData = await geocodeResponse.json();
    
    console.log(`📍 Geocoding response status: ${geocodeData.status}`);
    
    if (geocodeData.status !== 'OK') {
      console.error(`❌ Geocoding failed with status: ${geocodeData.status}`, geocodeData);
      
      // Provide more specific error messages
      let errorMessage = 'Could not find the specified location';
      switch (geocodeData.status) {
        case 'ZERO_RESULTS':
          errorMessage = `No results found for location: "${location}". Please try a more specific address, city, or zip code.`;
          break;
        case 'INVALID_REQUEST':
          errorMessage = `Invalid location format: "${location}". Please provide a valid address, city, or zip code.`;
          break;
        case 'OVER_DAILY_LIMIT':
        case 'OVER_QUERY_LIMIT':
          errorMessage = 'Location service temporarily unavailable. Please try again later.';
          break;
        case 'REQUEST_DENIED':
          console.error('❌ Google Maps API Key Issue - Key Details:');
          console.error('   - Key length:', apiKey.length);
          console.error('   - Key prefix:', apiKey.substring(0, 15));
          console.error('   - Possible issues: API not enabled, key restrictions, billing not set up');
          errorMessage = 'Google Maps API access denied. The API key may not be configured correctly or the required APIs (Geocoding, Places) may not be enabled.';
          break;
        default:
          errorMessage = `Location lookup failed: ${geocodeData.status}`;
      }
      
      return Response.json({ error: errorMessage }, { status: 400 });
    }
    
    if (!geocodeData.results.length) {
      console.error(`❌ No geocoding results for location: "${location}"`);
      return Response.json({ 
        error: `No results found for location: "${location}". Please try a more specific address, city, or zip code.` 
      }, { status: 400 });
    }

    const { lat, lng } = geocodeData.results[0].geometry.location;
    const radiusMeters = radius * 1609.34; // Convert miles to meters

    // Search for places using Google Places API
    const placesUrl = `https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=${lat},${lng}&radius=${radiusMeters}&keyword=${encodeURIComponent(query)}&key=${apiKey}`;
    console.log(`🔍 Searching places near coordinates: ${lat}, ${lng} with radius ${radiusMeters}m for keyword: "${query}"`);
    
    const placesResponse = await fetch(placesUrl);
    const placesData = await placesResponse.json();
    
    console.log(`🏥 Places API response status: ${placesData.status}`);

    if (placesData.status !== 'OK') {
      console.error(`❌ Places API failed with status: ${placesData.status}`, placesData);
      
      let errorMessage = 'Search service error';
      switch (placesData.status) {
        case 'ZERO_RESULTS':
          errorMessage = `No ${query} found within ${radius} miles of "${location}". Try expanding your search radius or using different search terms.`;
          break;
        case 'INVALID_REQUEST':
          errorMessage = 'Invalid search request. Please try different search terms.';
          break;
        case 'OVER_QUERY_LIMIT':
          errorMessage = 'Search service temporarily unavailable due to high demand. Please try again in a few minutes.';
          break;
        case 'REQUEST_DENIED':
          errorMessage = 'Search service access denied. Please contact support.';
          break;
        default:
          errorMessage = `Search failed: ${placesData.status}`;
      }
      
      return Response.json({ error: errorMessage }, { status: 500 });
    }
    
    if (!placesData.results || placesData.results.length === 0) {
      console.log(`ℹ️ No results found for "${query}" near "${location}"`);
      return Response.json({ 
        providers: [],
        message: `No ${query} found within ${radius} miles of "${location}". Try expanding your search radius or using different search terms.`
      });
    }
    
    console.log(`✅ Found ${placesData.results.length} places`);

    // Convert Google Places results to our format
    const providers = await Promise.all(
      placesData.results.slice(0, 20).map(async (place: any) => {
        // Get additional details for each place
        const detailsUrl = `https://maps.googleapis.com/maps/api/place/details/json?place_id=${place.place_id}&fields=name,formatted_address,formatted_phone_number,website,rating,user_ratings_total,opening_hours&key=${apiKey}`;
        const detailsResponse = await fetch(detailsUrl);
        const detailsData = await detailsResponse.json();
        const details = detailsData.result;

        // Calculate distance
        const distance = calculateDistance(lat, lng, place.geometry.location.lat, place.geometry.location.lng);

        return {
          name: details.name || place.name,
          address: details.formatted_address || place.vicinity,
          phone: details.formatted_phone_number || 'Phone not available',
          website: details.website,
          rating: details.rating || 0,
          reviews_count: details.user_ratings_total || 0,
          distance: parseFloat(distance.toFixed(1)),
          coordinates: {
            lat: place.geometry.location.lat,
            lng: place.geometry.location.lng
          },
          hours: details.opening_hours?.weekday_text ? {
            'Hours': details.opening_hours.weekday_text.join(', ')
          } : undefined
        };
      })
    );

    // Sort by distance
    const sortedProviders = providers.sort((a, b) => a.distance - b.distance);

    return Response.json({ providers: sortedProviders });
  } catch (error) {
    console.error('Search providers API error:', error);
    
    // Log detailed error for debugging
    if (error instanceof Error) {
      console.error('Error details:', error.message);
      console.error('Error stack:', error.stack);
    }
    
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    return Response.json({ 
      error: 'Failed to search for providers', 
      details: errorMessage 
    }, { status: 500 });
  }
}

// Calculate distance between two coordinates in miles
function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 3959; // Earth's radius in miles
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLng = (lng2 - lng1) * Math.PI / 180;
  const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng / 2) * Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c;
}