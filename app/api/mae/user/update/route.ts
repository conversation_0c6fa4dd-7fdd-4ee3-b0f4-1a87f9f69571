import { NextRequest, NextResponse } from 'next/server'
import { updateUserInfo } from '@/lib/mae-family-function-tools'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const {
      name,
      phone,
      zip,
      preferences,
      emergency_contact
    } = body

    console.log('🔵 Mae updating user info')

    // Build updates object with only provided fields
    const updates: any = {}
    if (name !== undefined) updates.name = name
    if (phone !== undefined) updates.phone = phone
    if (zip !== undefined) updates.zip = zip
    if (preferences !== undefined) updates.preferences = preferences
    if (emergency_contact !== undefined) updates.emergency_contact = emergency_contact

    const result = await updateUserInfo(updates)

    return NextResponse.json(result)

  } catch (error) {
    console.error('❌ Error in Mae update user info API:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}