import { NextRequest, NextResponse } from 'next/server'
import { addFamilyMember } from '@/lib/mae-family-function-tools'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const {
      name,
      date_of_birth,
      gender,
      relationship,
      medical_conditions,
      allergies,
      additional_notes,
      is_primary
    } = body

    // Validate required fields
    if (!name || !date_of_birth || !relationship) {
      return NextResponse.json({
        success: false,
        error: 'Missing required fields: name, date_of_birth, and relationship are required'
      }, { status: 400 })
    }

    console.log('🔵 Mae adding family member:', name, 'DOB:', date_of_birth)

    const result = await addFamilyMember({
      name,
      date_of_birth,
      gender,
      relationship,
      medical_conditions: medical_conditions || [],
      allergies: allergies || [],
      additional_notes,
      is_primary: is_primary || false
    })

    return NextResponse.json(result)

  } catch (error) {
    console.error('❌ Error in Mae add family member API:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}