import { NextRequest, NextResponse } from 'next/server'
import { searchFamilyMembers } from '@/lib/mae-family-function-tools'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const query = searchParams.get('query')

    if (!query) {
      return NextResponse.json({
        success: false,
        error: 'Missing query parameter'
      }, { status: 400 })
    }

    console.log('🔵 Mae searching family members:', query)

    const result = await searchFamilyMembers(query)

    return NextResponse.json(result)

  } catch (error) {
    console.error('❌ Error in Mae search family members API:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}