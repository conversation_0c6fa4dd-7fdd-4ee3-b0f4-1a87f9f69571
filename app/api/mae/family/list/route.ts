import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUserContext } from '@/lib/mae-family-function-tools'

export async function GET(request: NextRequest) {
  try {
    console.log('🔵 Mae getting family list')

    const result = await getCurrentUserContext()

    if (!result.success) {
      return NextResponse.json(result, { status: 401 })
    }

    // Format the response for <PERSON>'s understanding
    const familyMembers = result.data?.family_members || []
    const familyList = familyMembers.map(member => {
      const birthDate = new Date(member.date_of_birth)
      const today = new Date()
      let age = today.getFullYear() - birthDate.getFullYear()
      const monthDiff = today.getMonth() - birthDate.getMonth()
      
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--
      }

      return {
        id: member.id,
        name: member.name,
        age,
        age_description: age < 1 ? `${Math.floor(age * 12)} months old` : `${age} years old`,
        relationship: member.relationship,
        gender: member.gender,
        medical_conditions: member.medical_conditions || [],
        allergies: member.allergies || [],
        additional_notes: member.additional_notes,
        is_primary: member.is_primary
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        user_name: result.data?.user_name,
        family_count: result.data?.family_count || 0,
        family_members: familyList
      },
      message: `Found ${familyList.length} family members for ${result.data?.user_name}`
    })

  } catch (error) {
    console.error('❌ Error in Mae family list API:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}