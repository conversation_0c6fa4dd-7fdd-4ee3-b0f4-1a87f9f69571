import { NextRequest, NextResponse } from 'next/server'
import { getFamilyMemberDetails } from '@/lib/mae-family-function-tools'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const memberId = searchParams.get('member_id')

    if (!memberId) {
      return NextResponse.json({
        success: false,
        error: 'Missing member_id parameter'
      }, { status: 400 })
    }

    console.log('🔵 Mae getting family member details:', memberId)

    const result = await getFamilyMemberDetails(memberId)

    return NextResponse.json(result)

  } catch (error) {
    console.error('❌ Error in Mae family member details API:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}