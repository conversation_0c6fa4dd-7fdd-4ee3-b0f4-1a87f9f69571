import { NextRequest, NextResponse } from 'next/server'
import { updateFamilyMember } from '@/lib/mae-family-function-tools'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    const {
      member_id,
      name,
      date_of_birth,
      gender,
      relationship,
      medical_conditions,
      allergies,
      additional_notes,
      is_primary
    } = body

    // Validate required fields
    if (!member_id) {
      return NextResponse.json({
        success: false,
        error: 'Missing required field: member_id'
      }, { status: 400 })
    }

    console.log('🔵 Mae updating family member:', member_id)

    // Build updates object with only provided fields
    const updates: any = {}
    if (name !== undefined) updates.name = name
    if (date_of_birth !== undefined) updates.date_of_birth = date_of_birth
    if (gender !== undefined) updates.gender = gender
    if (relationship !== undefined) updates.relationship = relationship
    if (medical_conditions !== undefined) updates.medical_conditions = medical_conditions
    if (allergies !== undefined) updates.allergies = allergies
    if (additional_notes !== undefined) updates.additional_notes = additional_notes
    if (is_primary !== undefined) updates.is_primary = is_primary

    const result = await updateFamilyMember(member_id, updates)

    return NextResponse.json(result)

  } catch (error) {
    console.error('❌ Error in Mae update family member API:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}