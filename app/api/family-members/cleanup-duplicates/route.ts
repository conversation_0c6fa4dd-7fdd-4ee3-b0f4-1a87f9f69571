import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY
    
    if (!supabaseUrl || !supabaseKey) {
      return NextResponse.json({ 
        success: false, 
        error: 'Database configuration missing' 
      }, { status: 500 })
    }
    
    const supabase = createClient(supabaseUrl, supabaseKey)
    const { userId, action, duplicateIds, keepId, mergedData } = await request.json()
    
    if (!userId || !action) {
      return NextResponse.json({ 
        success: false, 
        error: 'User ID and action are required' 
      }, { status: 400 })
    }

    console.log('🧹 Cleaning up family member duplicates:', { userId, action, duplicateIds, keepId })

    switch (action) {
      case 'delete_duplicates':
        if (!duplicateIds || !Array.isArray(duplicateIds)) {
          return NextResponse.json({ 
            success: false, 
            error: 'duplicateIds array is required for delete action' 
          }, { status: 400 })
        }

        const { error: deleteError } = await supabase
          .from('family_members')
          .delete()
          .in('id', duplicateIds)
          .eq('user_id', userId)

        if (deleteError) {
          console.error('❌ Error deleting duplicates:', deleteError)
          return NextResponse.json({ 
            success: false, 
            error: 'Failed to delete duplicate entries' 
          }, { status: 500 })
        }

        return NextResponse.json({
          success: true,
          action: 'deleted',
          deletedCount: duplicateIds.length,
          message: `Successfully deleted ${duplicateIds.length} duplicate family member(s)`
        })

      case 'merge_duplicates':
        if (!duplicateIds || !keepId || !mergedData) {
          return NextResponse.json({ 
            success: false, 
            error: 'duplicateIds, keepId, and mergedData are required for merge action' 
          }, { status: 400 })
        }

        // Update the kept record with merged data
        const { error: updateError } = await supabase
          .from('family_members')
          .update(mergedData)
          .eq('id', keepId)
          .eq('user_id', userId)

        if (updateError) {
          console.error('❌ Error updating merged record:', updateError)
          return NextResponse.json({ 
            success: false, 
            error: 'Failed to update merged record' 
          }, { status: 500 })
        }

        // Delete the other duplicates
        const duplicatesToDelete = duplicateIds.filter((id: string) => id !== keepId)
        if (duplicatesToDelete.length > 0) {
          const { error: deleteMergeError } = await supabase
            .from('family_members')
            .delete()
            .in('id', duplicatesToDelete)
            .eq('user_id', userId)

          if (deleteMergeError) {
            console.error('❌ Error deleting merged duplicates:', deleteMergeError)
            return NextResponse.json({ 
              success: false, 
              error: 'Failed to delete duplicate entries after merge' 
            }, { status: 500 })
          }
        }

        return NextResponse.json({
          success: true,
          action: 'merged',
          keptId: keepId,
          deletedCount: duplicatesToDelete.length,
          message: `Successfully merged ${duplicateIds.length} duplicate entries into one record`
        })

      case 'list_duplicates':
        // Find potential duplicates for a user
        const { data: allMembers, error: listError } = await supabase
          .from('family_members')
          .select('*')
          .eq('user_id', userId)
          .order('name', { ascending: true })

        if (listError) {
          console.error('❌ Error listing family members:', listError)
          return NextResponse.json({ 
            success: false, 
            error: 'Failed to fetch family members' 
          }, { status: 500 })
        }

        // Group by similar names to find duplicates
        const duplicateGroups = groupDuplicates(allMembers || [])

        return NextResponse.json({
          success: true,
          action: 'listed',
          duplicateGroups,
          totalMembers: allMembers?.length || 0,
          duplicateCount: duplicateGroups.reduce((sum, group) => sum + group.members.length, 0),
          message: `Found ${duplicateGroups.length} duplicate group(s)`
        })

      default:
        return NextResponse.json({ 
          success: false, 
          error: 'Invalid action. Must be delete_duplicates, merge_duplicates, or list_duplicates' 
        }, { status: 400 })
    }

  } catch (error) {
    console.error('❌ Error in cleanup duplicates API:', error)
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}

// Helper function to group family members by similar names
function groupDuplicates(members: any[]): Array<{name: string, members: any[], isDuplicate: boolean}> {
  const groups: { [key: string]: any[] } = {}
  
  // Group by normalized name
  members.forEach(member => {
    const normalizedName = member.name.toLowerCase().trim()
    if (!groups[normalizedName]) {
      groups[normalizedName] = []
    }
    groups[normalizedName].push(member)
  })
  
  // Find groups with more than one member (duplicates)
  return Object.entries(groups)
    .filter(([_, memberGroup]) => memberGroup.length > 1)
    .map(([name, memberGroup]) => ({
      name,
      members: memberGroup,
      isDuplicate: true
    }))
}