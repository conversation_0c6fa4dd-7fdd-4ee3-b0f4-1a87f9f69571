import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY
    
    if (!supabaseUrl || !supabaseKey) {
      return NextResponse.json({ 
        success: false, 
        error: 'Database configuration missing' 
      }, { status: 500 })
    }
    
    const supabase = createClient(supabaseUrl, supabaseKey)
    const { userId, name, dateOfBirth, relationship } = await request.json()

    if (!userId || !name) {
      return NextResponse.json({
        success: false,
        error: 'User ID and name are required'
      }, { status: 400 })
    }

    console.log('🔍 Checking for duplicate family member:', { userId, name, dateOfBirth, relationship })

    // First, resolve the userId to a proper UUID
    // Check if userId is already a UUID or if it's a Clerk ID/email
    let actualUserId = userId

    // If userId looks like an email or Clerk ID, look up the actual user UUID
    if (!userId.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
      console.log('🔍 Looking up user by Clerk ID or email:', userId)

      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('id')
        .or(`clerk_id.eq.${userId},auth_user_id.eq.${userId},email.eq.${userId}`)
        .single()

      if (userError || !userData) {
        console.log('❌ User not found for identifier:', userId, userError)
        return NextResponse.json({
          success: false,
          error: 'User not found in database. Please ensure the user is synced first.',
          details: `No user found for identifier: ${userId}. This could mean the user needs to complete the sync process.`,
          action_required: 'sync_user'
        }, { status: 404 })
      }

      actualUserId = userData.id
      console.log('✅ Found user UUID:', actualUserId)
    }

    // Check for exact name match first
    const { data: exactMatches, error: exactError } = await supabase
      .from('family_members')
      .select('*')
      .eq('user_id', actualUserId)
      .ilike('name', name.trim())

    if (exactError) {
      console.error('❌ Error checking exact name matches:', exactError)
      return NextResponse.json({ 
        success: false, 
        error: 'Database query failed' 
      }, { status: 500 })
    }

    // Check for similar names (fuzzy matching)
    const { data: allFamilyMembers, error: allError } = await supabase
      .from('family_members')
      .select('*')
      .eq('user_id', actualUserId)

    if (allError) {
      console.error('❌ Error fetching family members:', allError)
      return NextResponse.json({ 
        success: false, 
        error: 'Database query failed' 
      }, { status: 500 })
    }

    // Fuzzy matching logic
    const similarMatches = allFamilyMembers?.filter(member => {
      const memberName = member.name.toLowerCase().trim()
      const inputName = name.toLowerCase().trim()
      
      // Check for exact match (case insensitive)
      if (memberName === inputName) return true
      
      // Check for very similar names (accounting for common variations)
      if (isSimilarName(memberName, inputName)) return true
      
      return false
    }) || []

    const duplicateAnalysis = {
      hasExactMatch: exactMatches && exactMatches.length > 0,
      hasSimilarMatch: similarMatches.length > 0,
      exactMatches: exactMatches || [],
      similarMatches: similarMatches,
      duplicateCount: similarMatches.length,
      isDuplicate: similarMatches.length > 0
    }

    // If there are matches, provide detailed information
    if (duplicateAnalysis.isDuplicate) {
      const matchDetails = similarMatches.map(match => ({
        id: match.id,
        name: match.name,
        dateOfBirth: match.date_of_birth,
        relationship: match.relationship,
        createdAt: match.created_at,
        ageDifference: dateOfBirth ? calculateAgeDifference(match.date_of_birth, dateOfBirth) : null
      }))

      return NextResponse.json({
        success: true,
        isDuplicate: true,
        analysis: duplicateAnalysis,
        matches: matchDetails,
        message: `Found ${duplicateAnalysis.duplicateCount} existing family member(s) with similar name "${name}"`
      })
    }

    return NextResponse.json({
      success: true,
      isDuplicate: false,
      analysis: duplicateAnalysis,
      matches: [],
      message: `No duplicates found for "${name}"`
    })

  } catch (error) {
    console.error('❌ Error in duplicate check API:', error)
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}

// Helper function to check if two names are similar
function isSimilarName(name1: string, name2: string): boolean {
  // Remove common variations and normalize
  const normalize = (name: string) => name
    .toLowerCase()
    .trim()
    .replace(/[^a-z]/g, '') // Remove non-letters
  
  const n1 = normalize(name1)
  const n2 = normalize(name2)
  
  // Exact match after normalization
  if (n1 === n2) return true
  
  // Check for common name variations
  const variations = {
    'brendan': ['brenden', 'brendon', 'brendyn'],
    'chloe': ['chole', 'cloe', 'khloe'],
    'jimmy': ['jim', 'james', 'jamie'],
    'dean': ['deane', 'deen'],
    'bradley': ['brad', 'bradly']
  }
  
  for (const [canonical, variants] of Object.entries(variations)) {
    if ((n1 === canonical && variants.includes(n2)) || 
        (n2 === canonical && variants.includes(n1)) ||
        (variants.includes(n1) && variants.includes(n2))) {
      return true
    }
  }
  
  // Levenshtein distance for very close matches
  if (levenshteinDistance(n1, n2) <= 2 && Math.min(n1.length, n2.length) >= 3) {
    return true
  }
  
  return false
}

// Calculate age difference in years
function calculateAgeDifference(date1: string, date2: string): number {
  const d1 = new Date(date1)
  const d2 = new Date(date2)
  const diffTime = Math.abs(d2.getTime() - d1.getTime())
  return Math.floor(diffTime / (1000 * 60 * 60 * 24 * 365.25))
}

// Simple Levenshtein distance implementation
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = Array(str2.length + 1).fill(null).map(() => Array(str1.length + 1).fill(null))
  
  for (let i = 0; i <= str1.length; i++) {
    matrix[0][i] = i
  }
  
  for (let j = 0; j <= str2.length; j++) {
    matrix[j][0] = j
  }
  
  for (let j = 1; j <= str2.length; j++) {
    for (let i = 1; i <= str1.length; i++) {
      const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1
      matrix[j][i] = Math.min(
        matrix[j][i - 1] + 1, // deletion
        matrix[j - 1][i] + 1, // insertion
        matrix[j - 1][i - 1] + indicator // substitution
      )
    }
  }
  
  return matrix[str2.length][str1.length]
}