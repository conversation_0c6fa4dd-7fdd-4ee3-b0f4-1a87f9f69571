import { NextRequest, NextResponse } from 'next/server'
import { getOnboardingService } from '@/lib/supabase-client'

export async function POST(request: NextRequest) {
  try {
    const { sql } = await request.json()
    
    if (!sql) {
      return NextResponse.json({ error: 'SQL is required' }, { status: 400 })
    }
    
    console.log('Executing SQL:', sql.substring(0, 200) + '...')
    
    const client = getOnboardingService().getClient()
    
    // Execute the SQL using the supabase client
    const result = await client.rpc('exec_sql', { sql_query: sql })
    
    if (result.error) {
      console.error('SQL execution error:', result.error)
      return NextResponse.json({
        success: false,
        error: result.error.message || result.error,
        details: result.error
      }, { status: 500 })
    }
    
    return NextResponse.json({
      success: true,
      message: 'SQL executed successfully',
      result: result.data
    })
    
  } catch (error) {
    console.error('SQL execution failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}