import { NextRequest, NextResponse } from 'next/server'
import { getOnboardingService } from '@/lib/supabase-client'

export async function POST(request: NextRequest) {
  try {
    console.log('Attempting to fix users table by working around foreign key constraint...')
    
    const client = getOnboardingService().getClient()
    
    // Instead of recreating the table, let's try to work around the foreign key constraint
    // by first creating a valid user record that can be referenced
    
    // Step 1: Try creating a "system" user that other users can reference
    const systemUser = await client
      .from('users')
      .insert({
        id: '00000000-0000-0000-0000-000000000000',
        email: '<EMAIL>',
        name: 'System User',
        role: 'system',
        onboarding_completed: true,
        onboarding_step: 'complete',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (systemUser.error && !systemUser.error.message.includes('duplicate key')) {
      console.error('Could not create system user:', systemUser.error)
      
      // If system user creation fails, try to find what users exist
      const existingUsers = await client
        .from('users')
        .select('id, email')
        .limit(5)
      
      console.log('Existing users:', existingUsers.data)
      
      // If there are existing users, use the first one's ID as reference for the foreign key
      const referenceId = existingUsers.data && existingUsers.data.length > 0 
        ? existingUsers.data[0].id 
        : crypto.randomUUID()
      
      // Try to create a regular user now with manual UUID
      const testUser = await client
        .from('users')
        .insert({
          id: referenceId, // Use existing ID to satisfy foreign key constraint
          email: '<EMAIL>',
          name: 'Test Workaround User',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()
      
      if (testUser.error) {
        return NextResponse.json({
          success: false,
          error: 'User creation still failing: ' + testUser.error.message,
          details: testUser.error,
          existingUsers: existingUsers.data
        }, { status: 500 })
      }
      
      return NextResponse.json({
        success: true,
        message: 'User creation working without system user',
        testUser: testUser.data,
        existingUsers: existingUsers.data
      })
    }
    
    // Step 2: Now try creating a regular user with manual UUID
    const testUser = await client
      .from('users')
      .insert({
        id: crypto.randomUUID(),
        email: '<EMAIL>',
        name: 'Test With System User'
      })
      .select()
      .single()
    
    if (testUser.error) {
      return NextResponse.json({
        success: false,
        error: 'Test user creation failed: ' + testUser.error.message,
        details: testUser.error,
        systemUser: systemUser.data
      }, { status: 500 })
    }
    
    return NextResponse.json({
      success: true,
      message: 'Users table working with workaround',
      systemUser: systemUser.data,
      testUser: testUser.data
    })
    
  } catch (error) {
    console.error('Users table fix failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}