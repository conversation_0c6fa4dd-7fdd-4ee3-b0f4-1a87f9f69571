import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY
    
    if (!supabaseUrl || !supabaseKey) {
      return NextResponse.json({ 
        success: false, 
        error: 'Database configuration missing' 
      }, { status: 500 })
    }
    
    const supabase = createClient(supabaseUrl, supabaseKey)
    const { user_id, child, note, tags, content_type, weather } = await request.json()
    
    // Validate required fields
    if (!user_id || !child || !note) {
      return NextResponse.json({ 
        success: false, 
        error: 'Missing required fields: user_id, child, note' 
      }, { status: 400 })
    }

    console.log('📝 Mae saving care log entry:', { user_id, child, content_type })

    // Validate that user_id is a proper UUID
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(user_id)) {
      return NextResponse.json({ 
        success: false, 
        error: 'Invalid user_id format. Must be a valid UUID.' 
      }, { status: 400 })
    }

    // Verify the user exists
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, email, name')
      .eq('id', user_id)
      .single()

    if (userError || !userData) {
      console.error('❌ User not found:', user_id, userError)
      return NextResponse.json({ 
        success: false, 
        error: 'User not found in database' 
      }, { status: 404 })
    }

    // Insert care log entry
    const careLogData = {
      user_id,
      child,
      note,
      tags: tags || [],
      content_type: content_type || 'daily_log',
      weather: weather || null,
      created_at: new Date().toISOString()
    }

    const { data: newEntry, error: insertError } = await supabase
      .from('care_log_entries')
      .insert(careLogData)
      .select()
      .single()

    if (insertError) {
      console.error('❌ Error inserting care log entry:', insertError)
      return NextResponse.json({ 
        success: false, 
        error: 'Failed to save care log entry to database',
        details: insertError.message
      }, { status: 500 })
    }

    console.log('✅ Care log entry saved successfully:', newEntry.id)

    return NextResponse.json({
      success: true,
      message: `Successfully saved care log entry for ${child}`,
      entry_id: newEntry.id,
      entry: {
        id: newEntry.id,
        child: newEntry.child,
        note: newEntry.note,
        tags: newEntry.tags,
        content_type: newEntry.content_type,
        created_at: newEntry.created_at
      }
    })

  } catch (error) {
    console.error('❌ Error in Mae care log API:', error)
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET endpoint to retrieve care log entries
export async function GET(request: NextRequest) {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY
    
    if (!supabaseUrl || !supabaseKey) {
      return NextResponse.json({ 
        success: false, 
        error: 'Database configuration missing' 
      }, { status: 500 })
    }
    
    const supabase = createClient(supabaseUrl, supabaseKey)
    const { searchParams } = new URL(request.url)
    const user_id = searchParams.get('user_id')
    const child = searchParams.get('child')
    const limit = parseInt(searchParams.get('limit') || '20')
    
    if (!user_id) {
      return NextResponse.json({ 
        success: false, 
        error: 'user_id parameter is required' 
      }, { status: 400 })
    }

    // Build query
    let query = supabase
      .from('care_log_entries')
      .select('*')
      .eq('user_id', user_id)
      .order('created_at', { ascending: false })
      .limit(limit)

    // Filter by child if specified
    if (child) {
      query = query.eq('child', child)
    }

    const { data: careLogEntries, error: careLogError } = await query

    if (careLogError) {
      console.error('❌ Error fetching care log entries:', careLogError)
      return NextResponse.json({ 
        success: false, 
        error: 'Failed to fetch care log entries',
        details: careLogError.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      care_log_entries: careLogEntries || [],
      count: careLogEntries?.length || 0
    })

  } catch (error) {
    console.error('❌ Error in Mae care log GET API:', error)
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
