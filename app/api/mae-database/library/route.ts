import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

export async function POST(request: NextRequest) {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY
    
    if (!supabaseUrl || !supabaseKey) {
      return NextResponse.json({ 
        success: false, 
        error: 'Database configuration missing' 
      }, { status: 500 })
    }
    
    const supabase = createClient(supabaseUrl, supabaseKey)
    const { user_id, content_type, title, content, metadata } = await request.json()
    
    // Validate required fields
    if (!user_id || !content_type || !title || !content) {
      return NextResponse.json({ 
        success: false, 
        error: 'Missing required fields: user_id, content_type, title, content' 
      }, { status: 400 })
    }

    console.log('📚 Mae saving to library:', { user_id, content_type, title })

    // Validate that user_id is a proper UUID
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(user_id)) {
      return NextResponse.json({ 
        success: false, 
        error: 'Invalid user_id format. Must be a valid UUID.' 
      }, { status: 400 })
    }

    // Verify the user exists
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, email, name')
      .eq('id', user_id)
      .single()

    if (userError || !userData) {
      console.error('❌ User not found:', user_id, userError)
      return NextResponse.json({ 
        success: false, 
        error: 'User not found in database' 
      }, { status: 404 })
    }

    // Insert library item
    const libraryData = {
      user_id,
      content_type,
      title: title.trim(),
      content,
      metadata: metadata || {},
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    const { data: newLibraryItem, error: insertError } = await supabase
      .from('my_library')
      .insert(libraryData)
      .select()
      .single()

    if (insertError) {
      console.error('❌ Error inserting library item:', insertError)
      return NextResponse.json({ 
        success: false, 
        error: 'Failed to save library item to database',
        details: insertError.message
      }, { status: 500 })
    }

    console.log('✅ Library item saved successfully:', newLibraryItem.id)

    return NextResponse.json({
      success: true,
      message: `Successfully saved "${title}" to your library`,
      library_id: newLibraryItem.id,
      library_item: {
        id: newLibraryItem.id,
        content_type: newLibraryItem.content_type,
        title: newLibraryItem.title,
        content: newLibraryItem.content,
        metadata: newLibraryItem.metadata,
        created_at: newLibraryItem.created_at
      }
    })

  } catch (error) {
    console.error('❌ Error in Mae library API:', error)
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET endpoint to retrieve library items
export async function GET(request: NextRequest) {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY
    
    if (!supabaseUrl || !supabaseKey) {
      return NextResponse.json({ 
        success: false, 
        error: 'Database configuration missing' 
      }, { status: 500 })
    }
    
    const supabase = createClient(supabaseUrl, supabaseKey)
    const { searchParams } = new URL(request.url)
    const user_id = searchParams.get('user_id')
    const content_type = searchParams.get('content_type')
    const limit = parseInt(searchParams.get('limit') || '20')
    
    if (!user_id) {
      return NextResponse.json({ 
        success: false, 
        error: 'user_id parameter is required' 
      }, { status: 400 })
    }

    // Build query
    let query = supabase
      .from('my_library')
      .select('*')
      .eq('user_id', user_id)
      .order('created_at', { ascending: false })
      .limit(limit)

    // Filter by content type if specified
    if (content_type) {
      query = query.eq('content_type', content_type)
    }

    const { data: libraryItems, error: libraryError } = await query

    if (libraryError) {
      console.error('❌ Error fetching library items:', libraryError)
      return NextResponse.json({ 
        success: false, 
        error: 'Failed to fetch library items',
        details: libraryError.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      library_items: libraryItems || [],
      count: libraryItems?.length || 0
    })

  } catch (error) {
    console.error('❌ Error in Mae library GET API:', error)
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
