import { NextRequest, NextResponse } from 'next/server'
import { createNewsletterWelcomeEmail } from '@/lib/email-templates'
import nodemailer from 'nodemailer'
import path from 'path'

// Rate limiting store (in production, use Redis or a database)
const rateLimitStore = new Map<string, { count: number; lastRequest: number }>()

// Rate limiting configuration
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 5 // 5 signups per minute per IP

interface NewsletterSignupData {
  email: string
}

function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

function checkRateLimit(clientIP: string): boolean {
  const now = Date.now()
  const clientData = rateLimitStore.get(clientIP)

  if (!clientData) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  // Reset count if window has passed
  if (now - clientData.lastRequest > RATE_LIMIT_WINDOW) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  // Check if limit exceeded
  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  // Increment count
  clientData.count++
  clientData.lastRequest = now
  return true
}

export async function POST(request: NextRequest) {
  try {
    console.log('📧 Newsletter signup request received')
    
    // Get client IP for rate limiting
    const clientIP = request.headers.get('x-forwarded-for')?.split(',')[0] || 
      request.headers.get('x-real-ip') || 
      'unknown'

    // Check rate limit
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please wait before signing up again.' },
        { status: 429 }
      )
    }

    const body = await request.json()
    const { email }: NewsletterSignupData = body

    // Validation
    if (!email || typeof email !== 'string') {
      return NextResponse.json(
        { error: 'Email address is required' },
        { status: 400 }
      )
    }

    if (!validateEmail(email)) {
      return NextResponse.json(
        { error: 'Please provide a valid email address' },
        { status: 400 }
      )
    }

    console.log('📧 Newsletter signup request:', { email, clientIP })

    // Get Supabase project details from environment
    const supabaseProjectId = process.env.SUPABASE_PROJECT_ID || 'njhibesggyagmezbergr'
    const supabaseApiKey = process.env.SUPABASE_SERVICE_ROLE_KEY

    console.log('🔧 Environment check:', {
      hasSupabaseProjectId: !!supabaseProjectId,
      hasSupabaseApiKey: !!supabaseApiKey,
      projectId: supabaseProjectId
    })

    if (!supabaseApiKey) {
      console.error('❌ Supabase service role key not configured')
      return NextResponse.json(
        { error: 'Newsletter service not configured' },
        { status: 500 }
      )
    }

    // Insert into Supabase database
    const supabaseUrl = `https://${supabaseProjectId}.supabase.co/rest/v1/newsletter_signups`
    
    console.log('💾 Inserting newsletter signup into database...')
    
    const response = await fetch(supabaseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseApiKey}`,
        'apikey': supabaseApiKey,
        'Prefer': 'return=minimal'
      },
      body: JSON.stringify({
        email: email.toLowerCase().trim()
      })
    })

    if (!response.ok) {
      const errorData = await response.text()
      console.error('❌ Supabase insert failed:', {
        status: response.status,
        statusText: response.statusText,
        error: errorData
      })

      // Check if it's a duplicate email error
      if (response.status === 409 || errorData.includes('duplicate') || errorData.includes('unique')) {
        return NextResponse.json(
          { error: 'This email is already subscribed to our newsletter' },
          { status: 409 }
        )
      }

      return NextResponse.json(
        { error: 'Failed to subscribe to newsletter. Please try again.' },
        { status: 500 }
      )
    }

    console.log('✅ Newsletter signup successful:', email)

    // Send welcome email
    try {
      console.log('📧 Sending newsletter welcome email...')

      // Get SMTP credentials
      const smtpHost = process.env.SMTP_HOST
      const smtpPort = parseInt(process.env.SMTP_PORT || '465')
      const smtpUser = process.env.SMTP_USER
      const smtpPass = process.env.SMTP_PASS
      const smtpSecure = process.env.SMTP_SECURE === 'true'
      const fromEmail = process.env.FROM_EMAIL || smtpUser
      const fromName = process.env.FROM_NAME || 'Our Kidz'

      console.log('📧 SMTP Configuration check:', {
        hasSmtpUser: !!smtpUser,
        hasSmtpPass: !!smtpPass,
        hasSmtpHost: !!smtpHost,
        smtpHost,
        smtpPort,
        smtpSecure
      })

      if (smtpUser && smtpPass && smtpHost) {
        // Create transporter
        const transporter = nodemailer.createTransport({
          host: smtpHost,
          port: smtpPort,
          secure: smtpSecure,
          auth: {
            user: smtpUser,
            pass: smtpPass,
          },
        })

        // Generate welcome email content
        const emailContent = createNewsletterWelcomeEmail(email)

        // Send welcome email
        const mailOptions = {
          from: `"${fromName}" <${fromEmail}>`,
          to: email,
          subject: emailContent.subject,
          text: emailContent.text,
          html: emailContent.html,
          attachments: [
            {
              filename: 'ourkidz-logo.png',
              path: path.join(process.cwd(), 'public', 'OKdarkTsp.png'),
              cid: 'ourkidz-logo'
            }
          ]
        }

        const info = await transporter.sendMail(mailOptions)
        console.log('✅ Welcome email sent successfully:', info.messageId)
      } else {
        console.log('⚠️ SMTP not configured, skipping welcome email')
      }
    } catch (emailError) {
      console.error('❌ Failed to send welcome email:', emailError)
      // Don't fail the signup if email fails - user is still subscribed
    }

    return NextResponse.json({
      success: true,
      message: 'Successfully subscribed to newsletter! Check your email for a welcome message.',
      email: email
    })

  } catch (error) {
    console.error('❌ Newsletter signup error:', error)
    console.error('❌ Error details:', error instanceof Error ? error.message : String(error))
    console.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack trace')
    return NextResponse.json(
      { error: 'Internal server error. Please try again.' },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Newsletter Signup API Endpoint',
    description: 'POST to this endpoint to subscribe to the Our Kidz newsletter',
    usage: {
      method: 'POST',
      body: {
        email: '<EMAIL> (required)'
      }
    },
    features: [
      'Email validation',
      'Rate limiting (5 signups per minute)',
      'Duplicate email prevention',
      'Supabase database integration',
      'Automatic timestamp tracking',
      'Welcome email confirmation'
    ]
  })
}
