import { NextRequest, NextResponse } from 'next/server'
import { getOnboardingService } from '@/lib/supabase-client'

// Rate limiting store
const rateLimitStore = new Map<string, { count: number; lastRequest: number }>()
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 30 // 30 requests per minute per IP

function checkRateLimit(clientIP: string): boolean {
  const now = Date.now()
  const clientData = rateLimitStore.get(clientIP)

  if (!clientData) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  if (now - clientData.lastRequest > RATE_LIMIT_WINDOW) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  clientData.count++
  clientData.lastRequest = now
  return true
}

function getClientIP(request: NextRequest): string {
  return request.ip || 
    request.headers.get('x-forwarded-for')?.split(',')[0] || 
    request.headers.get('x-real-ip') || 
    'unknown'
}

// Validation functions
function validateUserData(data: any): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!data.email) {
    errors.push('Email is required')
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
    errors.push('Please provide a valid email address')
  }

  if (!data.name || data.name.trim().length === 0) {
    errors.push('Name is required')
  }

  if (data.phone && !/^\+?[\d\s\-\(\)]+$/.test(data.phone)) {
    errors.push('Please provide a valid phone number')
  }

  if (data.zip && !/^\d{5}(-\d{4})?$/.test(data.zip)) {
    errors.push('Please provide a valid ZIP code')
  }

  if (data.date_of_birth) {
    const birthDate = new Date(data.date_of_birth)
    const today = new Date()
    const age = today.getFullYear() - birthDate.getFullYear()
    
    if (birthDate > today) {
      errors.push('Date of birth cannot be in the future')
    } else if (age < 18) {
      errors.push('You must be at least 18 years old to register')
    } else if (age > 120) {
      errors.push('Please provide a valid date of birth')
    }
  }

  if (data.emergency_contact) {
    if (!data.emergency_contact.name) {
      errors.push('Emergency contact name is required')
    }
    if (!data.emergency_contact.phone) {
      errors.push('Emergency contact phone is required')
    }
    if (!data.emergency_contact.relationship) {
      errors.push('Emergency contact relationship is required')
    }
  }

  return { valid: errors.length === 0, errors }
}

function validateFamilyMemberData(data: any): { valid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!data.name || data.name.trim().length === 0) {
    errors.push('Name is required')
  }

  if (!data.date_of_birth) {
    errors.push('Date of birth is required')
  } else {
    const birthDate = new Date(data.date_of_birth)
    const today = new Date()
    
    if (birthDate > today) {
      errors.push('Date of birth cannot be in the future')
    } else if (birthDate < new Date('1900-01-01')) {
      errors.push('Please provide a valid date of birth')
    }
  }

  if (!data.relationship || data.relationship.trim().length === 0) {
    errors.push('Relationship is required')
  }

  if (data.medications && Array.isArray(data.medications)) {
    data.medications.forEach((med: any, index: number) => {
      if (!med.name) {
        errors.push(`Medication ${index + 1}: Name is required`)
      }
      if (!med.dosage) {
        errors.push(`Medication ${index + 1}: Dosage is required`)
      }
      if (!med.frequency) {
        errors.push(`Medication ${index + 1}: Frequency is required`)
      }
    })
  }

  return { valid: errors.length === 0, errors }
}

// POST: Validate data
export async function POST(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const requestData = await request.json()
    const { type, data, email } = requestData

    if (!type || !data) {
      return NextResponse.json(
        { error: 'Type and data are required' },
        { status: 400 }
      )
    }

    let validation: { valid: boolean; errors: string[] }

    switch (type) {
      case 'user':
      case 'user_info':
        validation = validateUserData(data)
        break
      
      case 'family_member':
        validation = validateFamilyMemberData(data)
        break
      
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        validation = {
          valid: emailRegex.test(data.email || data),
          errors: emailRegex.test(data.email || data) ? [] : ['Please provide a valid email address']
        }
        break
      
      case 'phone':
        const phoneRegex = /^\+?[\d\s\-\(\)]+$/
        validation = {
          valid: phoneRegex.test(data.phone || data),
          errors: phoneRegex.test(data.phone || data) ? [] : ['Please provide a valid phone number']
        }
        break
      
      case 'zip':
        const zipRegex = /^\d{5}(-\d{4})?$/
        validation = {
          valid: zipRegex.test(data.zip || data),
          errors: zipRegex.test(data.zip || data) ? [] : ['Please provide a valid ZIP code']
        }
        break
      
      case 'date_of_birth':
        const dateValue = data.date_of_birth || data
        const birthDate = new Date(dateValue)
        const today = new Date()
        const age = today.getFullYear() - birthDate.getFullYear()
        
        const dateErrors: string[] = []
        if (birthDate > today) {
          dateErrors.push('Date of birth cannot be in the future')
        } else if (age < 0) {
          dateErrors.push('Please provide a valid date of birth')
        } else if (age > 120) {
          dateErrors.push('Please provide a valid date of birth')
        }
        
        validation = {
          valid: dateErrors.length === 0,
          errors: dateErrors
        }
        break
      
      default:
        return NextResponse.json(
          { error: 'Invalid validation type' },
          { status: 400 }
        )
    }

    // Check for duplicate email if validating user data
    if (type === 'user' && validation.valid && data.email && email !== data.email) {
      try {
        const { data: existingUser } = await getOnboardingService().getUserByEmail(data.email)
        if (existingUser) {
          validation.valid = false
          validation.errors.push('An account with this email already exists')
        }
      } catch (error) {
        console.warn('Error checking for existing user:', error)
        // Don't fail validation for database check errors
      }
    }

    console.log('✅ Validation completed:', { type, valid: validation.valid, errorCount: validation.errors.length })
    
    return NextResponse.json({
      success: true,
      validation: validation,
      type: type
    })

  } catch (error) {
    console.error('Error in POST /api/onboarding/validate:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET: Validate using database functions
export async function GET(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const { searchParams } = new URL(request.url)
    const type = searchParams.get('type')
    const name = searchParams.get('name')
    const dateOfBirth = searchParams.get('date_of_birth')
    const relationship = searchParams.get('relationship')

    if (type === 'family_member') {
      if (!name || !dateOfBirth || !relationship) {
        return NextResponse.json(
          { error: 'name, date_of_birth, and relationship are required for family member validation' },
          { status: 400 }
        )
      }

      const { data: validation, error } = await getOnboardingService().validateFamilyMemberData(
        name,
        dateOfBirth,
        relationship
      )

      if (error) {
        console.error('Error validating family member data:', error)
        return NextResponse.json(
          { error: 'Failed to validate family member data' },
          { status: 500 }
        )
      }

      return NextResponse.json({
        success: true,
        validation: validation,
        type: type
      })
    }

    return NextResponse.json(
      { error: 'Unsupported validation type for GET request' },
      { status: 400 }
    )

  } catch (error) {
    console.error('Error in GET /api/onboarding/validate:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}