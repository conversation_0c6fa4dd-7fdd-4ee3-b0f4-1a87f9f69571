import { NextRequest, NextResponse } from 'next/server'
import { getOnboardingService } from '@/lib/supabase-client'

// Rate limiting store
const rateLimitStore = new Map<string, { count: number; lastRequest: number }>()
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 40 // 40 requests per minute per IP (increased for onboarding flow)

function checkRateLimit(clientIP: string): boolean {
  const now = Date.now()
  const clientData = rateLimitStore.get(clientIP)

  if (!clientData) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  if (now - clientData.lastRequest > RATE_LIMIT_WINDOW) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  clientData.count++
  clientData.lastRequest = now
  return true
}

function getClientIP(request: NextRequest): string {
  return request.ip || 
    request.headers.get('x-forwarded-for')?.split(',')[0] || 
    request.headers.get('x-real-ip') || 
    'unknown'
}

// GET: Get onboarding progress
export async function GET(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email')

    if (!email) {
      return NextResponse.json(
        { error: 'Email parameter is required' },
        { status: 400 }
      )
    }

    const { data: progress, error } = await getOnboardingService().getOnboardingProgress(email)

    if (error) {
      console.error('Error fetching onboarding progress:', error)
      return NextResponse.json(
        { error: 'Failed to fetch onboarding progress' },
        { status: 500 }
      )
    }

    // If no progress found, return default progress
    if (!progress) {
      return NextResponse.json({
        success: true,
        progress: {
          user_id: null,
          current_step: 'welcome',
          onboarding_completed: false,
          family_members_count: 0,
          session_data: {}
        }
      })
    }

    return NextResponse.json({
      success: true,
      progress: progress
    })

  } catch (error) {
    console.error('Error in GET /api/onboarding/progress:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST: Update onboarding progress
export async function POST(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const requestData = await request.json()
    const { email, step, session_data } = requestData

    if (!email || !step) {
      return NextResponse.json(
        { error: 'Email and step are required' },
        { status: 400 }
      )
    }

    // Validate step
    const validSteps = ['welcome', 'user_info', 'family_info', 'complete']
    if (!validSteps.includes(step)) {
      return NextResponse.json(
        { error: 'Invalid step. Must be one of: ' + validSteps.join(', ') },
        { status: 400 }
      )
    }

    // Get user first
    const { data: user, error: userError } = await getOnboardingService().getUserByEmail(email)
    
    if (userError) {
      console.error('Error fetching user:', userError)
      return NextResponse.json(
        { error: 'Failed to fetch user' },
        { status: 500 }
      )
    }

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Update user's onboarding step
    const { data: updatedUser, error: updateError } = await getOnboardingService().updateUser(user.id, {
      onboarding_step: step as any,
      onboarding_completed: step === 'complete'
    })

    if (updateError) {
      console.error('Error updating user onboarding step:', updateError)
      return NextResponse.json(
        { error: 'Failed to update onboarding step' },
        { status: 500 }
      )
    }

    // Create or update onboarding session if session_data provided
    if (session_data) {
      try {
        const sessionUpdate = {
          user_id: user.id,
          session_data: session_data,
          current_step: step,
          updated_at: new Date().toISOString()
        }

        // Try to update existing session first
        const { data: existingSession } = await getOnboardingService().getClient()
          .from('onboarding_sessions')
          .select('id')
          .eq('user_id', user.id)
          .is('completed_at', null)
          .single()

        if (existingSession) {
          await getOnboardingService().updateOnboardingSession(existingSession.id as string, sessionUpdate)
        } else {
          await getOnboardingService().createOnboardingSession(sessionUpdate)
        }
      } catch (sessionError) {
        console.warn('Error updating onboarding session:', sessionError)
        // Don't fail the entire request for session update issues
      }
    }

    console.log('✅ Onboarding progress updated successfully:', { email, step })
    
    return NextResponse.json({
      success: true,
      message: 'Onboarding progress updated successfully',
      user: updatedUser,
      step: step
    })

  } catch (error) {
    console.error('Error in POST /api/onboarding/progress:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT: Complete onboarding
export async function PUT(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const requestData = await request.json()
    const { email, session_id } = requestData

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      )
    }

    // Get user first
    const { data: user, error: userError } = await getOnboardingService().getUserByEmail(email)
    
    if (userError) {
      console.error('Error fetching user:', userError)
      return NextResponse.json(
        { error: 'Failed to fetch user' },
        { status: 500 }
      )
    }

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Complete onboarding
    const { success, error: completionError } = await getOnboardingService().completeOnboarding(
      user.id,
      session_id || 'direct-completion'
    )

    if (!success) {
      console.error('Error completing onboarding:', completionError)
      return NextResponse.json(
        { error: 'Failed to complete onboarding' },
        { status: 500 }
      )
    }

    console.log('✅ Onboarding completed successfully:', email)
    
    return NextResponse.json({
      success: true,
      message: 'Onboarding completed successfully',
      user_id: user.id,
      completed_at: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in PUT /api/onboarding/progress:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}