import { NextRequest, NextResponse } from 'next/server'
import { getOnboardingService, type FamilyMember } from '@/lib/supabase-client'
import { createClient } from '@supabase/supabase-js'

// Rate limiting store
const rateLimitStore = new Map<string, { count: number; lastRequest: number }>()
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 30 // 30 requests per minute per IP (increased for onboarding flow)

function checkRateLimit(clientIP: string): boolean {
  const now = Date.now()
  const clientData = rateLimitStore.get(clientIP)

  if (!clientData) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  if (now - clientData.lastRequest > RATE_LIMIT_WINDOW) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  clientData.count++
  clientData.lastRequest = now
  return true
}

function getClientIP(request: NextRequest): string {
  return request.ip || 
    request.headers.get('x-forwarded-for')?.split(',')[0] || 
    request.headers.get('x-real-ip') || 
    'unknown'
}

// Helper function to get authenticated user
async function getAuthenticatedUser(request: NextRequest) {
  const authHeader = request.headers.get('authorization')
  if (!authHeader) {
    return null
  }

  const token = authHeader.replace('Bearer ', '')
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
  
  const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey)
  
  const { data: { user }, error } = await supabaseAdmin.auth.getUser(token)
  
  if (error || !user) {
    return null
  }
  
  return user
}

// POST: Create a new family member
export async function POST(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const rawMemberData: Partial<FamilyMember> = await request.json()
    
    // Clean up the data - convert empty strings to null for date fields
    const memberData: Partial<FamilyMember> = {
      ...rawMemberData,
      date_of_birth: rawMemberData.date_of_birth === '' ? undefined : rawMemberData.date_of_birth
    }
    
    // Validate required fields
    if (!memberData.user_id || !memberData.name || !memberData.date_of_birth) {
      return NextResponse.json(
        { error: 'user_id, name, and date_of_birth are required' },
        { status: 400 }
      )
    }

    // Try to get authenticated user for validation, but allow creation for Mae integration
    const authUser = await getAuthenticatedUser(request)
    console.log('Auth user for family member creation:', authUser?.email || 'No auth user')

    // If we have auth user, verify they own the user_id being provided
    if (authUser) {
      const { data: user } = await getOnboardingService().getUserByEmail(authUser.email!)
      if (user && user.id !== memberData.user_id) {
        return NextResponse.json(
          { error: 'Unauthorized: You can only create family members for your own account' },
          { status: 403 }
        )
      }
    }

    // Check for duplicate family member (same name and date of birth)
    const { data: existingMembers } = await getOnboardingService().getFamilyMembers(memberData.user_id)
    const duplicate = (existingMembers || []).find(member =>
      member.name.toLowerCase() === memberData.name!.toLowerCase() &&
      member.date_of_birth === memberData.date_of_birth
    )

    if (duplicate) {
      return NextResponse.json(
        { error: 'A family member with this name and date of birth already exists' },
        { status: 409 }
      )
    }

    // Validate date of birth
    const birthDate = new Date(memberData.date_of_birth!)
    const today = new Date()
    
    if (birthDate > today) {
      return NextResponse.json(
        { error: 'Date of birth cannot be in the future' },
        { status: 400 }
      )
    }

    if (birthDate < new Date('1900-01-01')) {
      return NextResponse.json(
        { error: 'Please provide a valid date of birth' },
        { status: 400 }
      )
    }

    // Create new family member
    const { data: newMember, error } = await getOnboardingService().createFamilyMember(memberData)

    if (error) {
      console.error('Error creating family member:', error)
      console.error('Family member error details:', {
        message: error.message,
        code: error.code,
        details: error.details,
        hint: error.hint
      })
      console.error('Family member data that failed:', memberData)
      return NextResponse.json(
        { error: 'Failed to create family member', details: error.message },
        { status: 500 }
      )
    }

    console.log('✅ Family member created successfully:', newMember?.name)
    
    return NextResponse.json({
      success: true,
      message: 'Family member created successfully',
      family_member: newMember
    })

  } catch (error) {
    console.error('Error in POST /api/onboarding/family-members:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET: Get family members for a user
export async function GET(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('user_id')
    const userEmail = searchParams.get('user_email')

    if (!userId && !userEmail) {
      return NextResponse.json(
        { error: 'user_id or user_email parameter is required' },
        { status: 400 }
      )
    }

    let finalUserId = userId

    // If user_email provided, get user_id first
    if (!finalUserId && userEmail) {
      const { data: user, error } = await getOnboardingService().getUserByEmail(userEmail)
      if (error || !user) {
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        )
      }
      finalUserId = user.id
    }

    const { data: familyMembers, error } = await getOnboardingService().getFamilyMembers(finalUserId!)

    if (error) {
      console.error('Error fetching family members:', error)
      return NextResponse.json(
        { error: 'Failed to fetch family members' },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      family_members: familyMembers,
      count: familyMembers.length
    })

  } catch (error) {
    console.error('Error in GET /api/onboarding/family-members:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// PUT: Update family member
export async function PUT(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const updateData = await request.json()
    
    if (!updateData.id) {
      return NextResponse.json(
        { error: 'Family member ID is required' },
        { status: 400 }
      )
    }

    // Remove id from update data to avoid conflicts
    const { id, ...memberData } = updateData

    // Validate date of birth if provided
    if (memberData.date_of_birth) {
      const birthDate = new Date(memberData.date_of_birth)
      const today = new Date()
      
      if (birthDate > today) {
        return NextResponse.json(
          { error: 'Date of birth cannot be in the future' },
          { status: 400 }
        )
      }

      if (birthDate < new Date('1900-01-01')) {
        return NextResponse.json(
          { error: 'Please provide a valid date of birth' },
          { status: 400 }
        )
      }
    }

    const { data: updatedMember, error } = await getOnboardingService().updateFamilyMember(id, memberData)

    if (error) {
      console.error('Error updating family member:', error)
      return NextResponse.json(
        { error: 'Failed to update family member' },
        { status: 500 }
      )
    }

    console.log('✅ Family member updated successfully:', updatedMember?.name)
    
    return NextResponse.json({
      success: true,
      message: 'Family member updated successfully',
      family_member: updatedMember
    })

  } catch (error) {
    console.error('Error in PUT /api/onboarding/family-members:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// DELETE: Delete family member
export async function DELETE(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    const { searchParams } = new URL(request.url)
    const memberId = searchParams.get('id')

    if (!memberId) {
      return NextResponse.json(
        { error: 'Family member ID is required' },
        { status: 400 }
      )
    }

    // Delete family member using the service method
    const { success, error } = await getOnboardingService().deleteFamilyMember(memberId)

    if (!success) {
      console.error('Error deleting family member:', error)
      return NextResponse.json(
        { error: 'Failed to delete family member' },
        { status: 500 }
      )
    }

    console.log('✅ Family member deleted successfully:', memberId)

    return NextResponse.json({
      success: true,
      message: 'Family member deleted successfully'
    })

  } catch (error) {
    console.error('Error in DELETE /api/onboarding/family-members:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}