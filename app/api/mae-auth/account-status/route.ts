import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Rate limiting store
const rateLimitStore = new Map<string, { count: number; lastRequest: number }>()
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 15 // 15 account status checks per minute per IP

function checkRateLimit(clientIP: string): boolean {
  const now = Date.now()
  const clientData = rateLimitStore.get(clientIP)

  if (!clientData) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  if (now - clientData.lastRequest > RATE_LIMIT_WINDOW) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  clientData.count++
  clientData.lastRequest = now
  return true
}

function getClientIP(request: NextRequest): string {
  return request.ip || 
    request.headers.get('x-forwarded-for')?.split(',')[0] || 
    request.headers.get('x-real-ip') || 
    'unknown'
}

export async function GET(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Too many account status checks. Please wait a moment and try again.' },
        { status: 429 }
      )
    }

    const { searchParams } = new URL(request.url)
    const email = searchParams.get('email')

    if (!email) {
      return NextResponse.json(
        { error: 'Email parameter is required' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase configuration')
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    console.log('👤 Checking comprehensive account status for:', email)

    // Get user from auth.users table
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers()

    if (authError) {
      console.error('Error fetching auth users:', authError)
      return NextResponse.json(
        { error: 'Failed to check account status' },
        { status: 500 }
      )
    }

    const authUser = authUsers.users.find(user => user.email === email)

    if (!authUser) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Get user record from our users table
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('auth_user_id', authUser.id)
      .single()

    if (userError && userError.code !== 'PGRST116') {
      console.error('Error fetching user record:', userError)
    }

    // Get family members count
    let familyMembersCount = 0
    if (userData) {
      const { count, error: familyError } = await supabase
        .from('family_members')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', userData.id)

      if (!familyError) {
        familyMembersCount = count || 0
      }
    }

    const isVerified = !!authUser.email_confirmed_at
    const hasUserRecord = !!userData
    const onboardingCompleted = userData?.onboarding_completed || false
    
    console.log(`👤 User ${email} comprehensive status:`)
    console.log(`   - Verified: ${isVerified}`)
    console.log(`   - Has user record: ${hasUserRecord}`)
    console.log(`   - Onboarding completed: ${onboardingCompleted}`)
    console.log(`   - Family members: ${familyMembersCount}`)

    return NextResponse.json({
      success: true,
      email: email,
      verified: isVerified,
      has_user_record: hasUserRecord,
      onboarding_completed: onboardingCompleted,
      family_members_count: familyMembersCount,
      onboarding_step: userData?.onboarding_step || 'authentication',
      user_id: authUser.id,
      created_at: authUser.created_at,
      email_confirmed_at: authUser.email_confirmed_at,
      last_sign_in_at: authUser.last_sign_in_at,
      user_record: userData ? {
        id: userData.id,
        name: userData.name,
        role: userData.role,
        phone: userData.phone,
        zip: userData.zip
      } : null
    })

  } catch (error) {
    console.error('Error in GET /api/mae-auth/account-status:', error)
    return NextResponse.json(
      { error: 'Internal server error during account status check' },
      { status: 500 }
    )
  }
}