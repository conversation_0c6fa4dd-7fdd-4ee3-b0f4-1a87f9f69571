import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Rate limiting store
const rateLimitStore = new Map<string, { count: number; lastRequest: number }>()
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 10 // 10 sign-in attempts per minute per IP

function checkRateLimit(clientIP: string): boolean {
  const now = Date.now()
  const clientData = rateLimitStore.get(clientIP)

  if (!clientData) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  if (now - clientData.lastRequest > RATE_LIMIT_WINDOW) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  clientData.count++
  clientData.lastRequest = now
  return true
}

function getClientIP(request: NextRequest): string {
  return request.ip || 
    request.headers.get('x-forwarded-for')?.split(',')[0] || 
    request.headers.get('x-real-ip') || 
    'unknown'
}

export async function POST(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Too many sign-in attempts. Please wait a moment and try again.' },
        { status: 429 }
      )
    }

    const { email, password } = await request.json()
    
    // Validate required fields
    if (!email || !password) {
      return NextResponse.json(
        { 
          error: 'Email and password are required',
          field: 'required_fields'
        },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { 
          error: 'Please provide a valid email address',
          field: 'email'
        },
        { status: 400 }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase configuration')
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    console.log('🔑 Attempting to sign in user:', email)

    // Sign in user with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email,
      password
    })

    if (authError) {
      console.error('Auth sign-in error:', authError)
      
      if (authError.message.includes('Invalid login credentials')) {
        return NextResponse.json(
          { 
            error: 'Invalid email or password. Please check your credentials and try again.',
            field: 'credentials'
          },
          { status: 401 }
        )
      }

      if (authError.message.includes('Email not confirmed')) {
        return NextResponse.json(
          { 
            error: 'Please verify your email address before signing in. Check your inbox for a verification link.',
            field: 'email_verification'
          },
          { status: 401 }
        )
      }

      return NextResponse.json(
        { 
          error: authError.message || 'Sign in failed',
          field: 'auth'
        },
        { status: 400 }
      )
    }

    if (!authData.user || !authData.session) {
      return NextResponse.json(
        { error: 'Sign in failed - no user session created' },
        { status: 500 }
      )
    }

    console.log('✅ User signed in successfully:', authData.user.id)

    // Get user record from our users table
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('auth_user_id', authData.user.id)
      .single()

    if (userError && userError.code !== 'PGRST116') {
      console.error('Error fetching user record:', userError)
    }

    return NextResponse.json({
      success: true,
      message: `Welcome back! You're now signed in as ${email}.`,
      user: authData.user,
      session: authData.session,
      user_record: userData || null,
      signed_in_at: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in POST /api/mae-auth/sign-in:', error)
    return NextResponse.json(
      { error: 'Internal server error during sign in' },
      { status: 500 }
    )
  }
}