import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Rate limiting store
const rateLimitStore = new Map<string, { count: number; lastRequest: number }>()
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 5 // 5 registration attempts per minute per IP

function checkRateLimit(clientIP: string): boolean {
  const now = Date.now()
  const clientData = rateLimitStore.get(clientIP)

  if (!clientData) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  if (now - clientData.lastRequest > RATE_LIMIT_WINDOW) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  clientData.count++
  clientData.lastRequest = now
  return true
}

function getClientIP(request: NextRequest): string {
  return request.ip || 
    request.headers.get('x-forwarded-for')?.split(',')[0] || 
    request.headers.get('x-real-ip') || 
    'unknown'
}

export async function POST(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Too many registration attempts. Please wait a moment and try again.' },
        { status: 429 }
      )
    }

    const { email, password, full_name } = await request.json()
    
    // Validate required fields
    if (!email || !password || !full_name) {
      return NextResponse.json(
        { 
          error: 'Email, password, and full name are required',
          field: 'required_fields'
        },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { 
          error: 'Please provide a valid email address',
          field: 'email'
        },
        { status: 400 }
      )
    }

    // Validate password strength
    if (password.length < 6) {
      return NextResponse.json(
        { 
          error: 'Password must be at least 6 characters long',
          field: 'password'
        },
        { status: 400 }
      )
    }

    // Initialize Supabase client
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('Missing Supabase configuration')
      return NextResponse.json(
        { error: 'Server configuration error' },
        { status: 500 }
      )
    }

    const supabase = createClient(supabaseUrl, supabaseServiceKey)

    console.log('🔐 Attempting to register user:', email)

    // Register user with Supabase Auth
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${request.nextUrl.origin}/auth/callback`,
        data: {
          full_name: full_name
        }
      }
    })

    if (authError) {
      console.error('Auth registration error:', authError)
      
      if (authError.message.includes('already registered')) {
        return NextResponse.json(
          { 
            error: 'An account with this email already exists. Try signing in instead.',
            field: 'email'
          },
          { status: 409 }
        )
      }

      return NextResponse.json(
        { 
          error: authError.message || 'Registration failed',
          field: 'auth'
        },
        { status: 400 }
      )
    }

    if (!authData.user) {
      return NextResponse.json(
        { error: 'User registration failed - no user data returned' },
        { status: 500 }
      )
    }

    console.log('✅ User registered with Supabase Auth:', authData.user.id)

    // Create user record in our users table
    const { data: userData, error: userError } = await supabase
      .from('users')
      .insert({
        auth_user_id: authData.user.id,
        email: email,
        name: full_name,
        role: 'parent',
        onboarding_completed: false,
        onboarding_step: 'authentication'
      })
      .select()
      .single()

    if (userError) {
      console.error('Error creating user record:', userError)
      
      // If user record creation fails, we should clean up the auth user
      // But we'll let it slide for now as the auth state change handler will handle it
      console.warn('User auth created but database record failed - will be handled by auth state change')
    } else {
      console.log('✅ User database record created:', userData.id)
    }

    return NextResponse.json({
      success: true,
      message: 'Account created successfully! Please check your email for a verification link.',
      user_id: authData.user.id,
      email: email,
      verification_required: true,
      created_at: new Date().toISOString()
    })

  } catch (error) {
    console.error('Error in POST /api/mae-auth/register:', error)
    return NextResponse.json(
      { error: 'Internal server error during registration' },
      { status: 500 }
    )
  }
}