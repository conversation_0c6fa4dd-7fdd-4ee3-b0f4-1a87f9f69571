import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

const supabase = createClient(supabaseUrl, supabaseServiceKey)

export async function POST(request: NextRequest) {
  try {
    console.log('🔧 Starting user relationship fixes...')

    // 1. Update audio_chat_sessions to link with users table via user_id
    console.log('📊 Updating audio_chat_sessions user_id relationships...')
    
    const { data: sessionsToUpdate, error: fetchError } = await supabase
      .from('audio_chat_sessions')
      .select('id, user_email')
      .is('user_id', null)
      .not('user_email', 'is', null)

    if (fetchError) {
      console.error('❌ Error fetching sessions to update:', fetchError)
      return NextResponse.json({ 
        success: false, 
        error: 'Failed to fetch sessions' 
      }, { status: 500 })
    }

    console.log(`📊 Found ${sessionsToUpdate?.length || 0} sessions to update`)

    let updatedSessions = 0
    let failedUpdates = 0

    if (sessionsToUpdate && sessionsToUpdate.length > 0) {
      for (const session of sessionsToUpdate) {
        try {
          // Find the user by email
          const { data: user, error: userError } = await supabase
            .from('users')
            .select('id')
            .eq('email', session.user_email)
            .single()

          if (userError || !user) {
            console.log(`⚠️ No user found for email: ${session.user_email}`)
            failedUpdates++
            continue
          }

          // Update the session with the user_id
          const { error: updateError } = await supabase
            .from('audio_chat_sessions')
            .update({ user_id: user.id })
            .eq('id', session.id)

          if (updateError) {
            console.error(`❌ Failed to update session ${session.id}:`, updateError)
            failedUpdates++
          } else {
            updatedSessions++
          }
        } catch (error) {
          console.error(`❌ Error processing session ${session.id}:`, error)
          failedUpdates++
        }
      }
    }

    // 2. Check for users missing auth_user_id and provide guidance
    console.log('👤 Checking for users missing auth_user_id...')
    
    const { data: usersWithoutAuth, error: usersError } = await supabase
      .from('users')
      .select('id, email, name')
      .is('auth_user_id', null)

    if (usersError) {
      console.error('❌ Error fetching users without auth_user_id:', usersError)
    }

    console.log(`👤 Found ${usersWithoutAuth?.length || 0} users without auth_user_id`)

    // 3. Check family_members relationships
    console.log('👨‍👩‍👧‍👦 Checking family_members relationships...')
    
    const { data: orphanedFamilyMembers, error: familyError } = await supabase
      .from('family_members')
      .select('id, name, user_id')
      .not('user_id', 'in', `(SELECT id FROM users)`)

    if (familyError) {
      console.error('❌ Error checking family members:', familyError)
    }

    console.log(`👨‍👩‍👧‍👦 Found ${orphanedFamilyMembers?.length || 0} orphaned family members`)

    // 4. Generate summary report
    const report = {
      success: true,
      timestamp: new Date().toISOString(),
      audio_chat_sessions: {
        total_checked: sessionsToUpdate?.length || 0,
        updated: updatedSessions,
        failed: failedUpdates,
        status: updatedSessions > 0 ? 'Updated successfully' : 'No updates needed'
      },
      users: {
        missing_auth_user_id: usersWithoutAuth?.length || 0,
        users_without_auth: usersWithoutAuth?.map(u => ({
          id: u.id,
          email: u.email,
          name: u.name
        })) || [],
        status: (usersWithoutAuth?.length || 0) > 0 ? 'Manual auth linking required' : 'All users have auth links'
      },
      family_members: {
        orphaned_count: orphanedFamilyMembers?.length || 0,
        orphaned_members: orphanedFamilyMembers?.map(fm => ({
          id: fm.id,
          name: fm.name,
          invalid_user_id: fm.user_id
        })) || [],
        status: (orphanedFamilyMembers?.length || 0) > 0 ? 'Orphaned family members found' : 'All family members properly linked'
      },
      recommendations: []
    }

    // Add recommendations based on findings
    if (updatedSessions > 0) {
      report.recommendations.push(`✅ Successfully updated ${updatedSessions} audio chat sessions with proper user_id relationships`)
    }

    if (failedUpdates > 0) {
      report.recommendations.push(`⚠️ ${failedUpdates} sessions could not be updated - users may not exist in database`)
    }

    if ((usersWithoutAuth?.length || 0) > 0) {
      report.recommendations.push('⚠️ Some users are missing auth_user_id - they may need to re-authenticate or be manually linked')
    }

    if ((orphanedFamilyMembers?.length || 0) > 0) {
      report.recommendations.push('⚠️ Some family members have invalid user_id references - data cleanup required')
    }

    if (updatedSessions === 0 && failedUpdates === 0 && (usersWithoutAuth?.length || 0) === 0 && (orphanedFamilyMembers?.length || 0) === 0) {
      report.recommendations.push('✅ All user relationships are properly configured!')
    }

    console.log('✅ User relationship fix completed:', report)

    return NextResponse.json(report)

  } catch (error) {
    console.error('❌ Error in fix-user-relationships API:', error)
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET endpoint to just check status without making changes
export async function GET(request: NextRequest) {
  try {
    console.log('🔍 Checking user relationship status...')

    // Check audio_chat_sessions without user_id
    const { data: sessionsWithoutUserId, error: sessionsError } = await supabase
      .from('audio_chat_sessions')
      .select('id, user_email')
      .is('user_id', null)
      .not('user_email', 'is', null)

    // Check users without auth_user_id
    const { data: usersWithoutAuth, error: usersError } = await supabase
      .from('users')
      .select('id, email, name')
      .is('auth_user_id', null)

    // Check orphaned family members
    const { data: orphanedFamilyMembers, error: familyError } = await supabase
      .from('family_members')
      .select('id, name, user_id')
      .not('user_id', 'in', `(SELECT id FROM users)`)

    const status = {
      success: true,
      timestamp: new Date().toISOString(),
      issues_found: {
        sessions_missing_user_id: sessionsWithoutUserId?.length || 0,
        users_missing_auth_id: usersWithoutAuth?.length || 0,
        orphaned_family_members: orphanedFamilyMembers?.length || 0
      },
      total_issues: (sessionsWithoutUserId?.length || 0) + (usersWithoutAuth?.length || 0) + (orphanedFamilyMembers?.length || 0),
      needs_fixing: ((sessionsWithoutUserId?.length || 0) + (usersWithoutAuth?.length || 0) + (orphanedFamilyMembers?.length || 0)) > 0
    }

    return NextResponse.json(status)

  } catch (error) {
    console.error('❌ Error checking user relationships:', error)
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error' 
    }, { status: 500 })
  }
}
