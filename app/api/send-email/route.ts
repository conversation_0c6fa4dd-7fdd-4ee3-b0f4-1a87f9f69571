import { NextRequest, NextResponse } from 'next/server'
import nodemailer from 'nodemailer'
import axios from 'axios'

// Rate limiting store (in production, use Redis or a database)
const rateLimitStore = new Map<string, { count: number; lastRequest: number }>()

// Rate limiting configuration
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 10 // 10 emails per minute per IP

interface EmailData {
  recipient_email: string
  subject: string
  text_content: string
  html_content?: string
  sender_name?: string
  log_to_webhook?: boolean
  webhook_url?: string
  sources?: { title: string; url: string }[]
}

function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

function checkRateLimit(clientIP: string): boolean {
  const now = Date.now()
  const clientData = rateLimitStore.get(clientIP)

  if (!clientData) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  // Reset count if window has passed
  if (now - clientData.lastRequest > RATE_LIMIT_WINDOW) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  // Check if within rate limit
  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  // Increment count
  clientData.count++
  clientData.lastRequest = now
  return true
}

async function logEmailToWebhook(
  email: string,
  subject: string,
  success: boolean,
  messageId?: string,
  error?: string,
  webhookUrl?: string,
  sources?: { title: string; url: string }[]
) {
  try {
    const defaultWebhookUrl = process.env.N8N_WEBHOOK_URL
    const targetWebhookUrl = webhookUrl || defaultWebhookUrl
    
    if (!targetWebhookUrl) {
      console.log('⚠️ No webhook URL configured, skipping webhook logging')
      return
    }

    const payload = {
      email: email,
      subject: subject,
      date: new Date().toISOString(),
      success: success,
      messageId: messageId || null,
      error: error || null,
      sourcesCount: sources?.length || 0,
      sources: sources || [],
      platform: 'Our Kidz',
      emailType: 'mae_generated'
    }

    console.log('📡 Sending webhook to:', targetWebhookUrl)
    
    const response = await axios.post(targetWebhookUrl, payload, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10 second timeout
    })

    console.log('✅ Webhook sent successfully:', response.status)
  } catch (webhookError) {
    console.error('❌ Failed to send webhook:', webhookError instanceof Error ? webhookError.message : 'Unknown webhook error')
  }
}

export async function POST(request: NextRequest) {
  try {
    // Get client IP for rate limiting
    const clientIP = request.ip || 
      request.headers.get('x-forwarded-for')?.split(',')[0] || 
      request.headers.get('x-real-ip') || 
      'unknown'

    // Check rate limit
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please wait before sending another email.' },
        { status: 429 }
      )
    }

    const body: EmailData = await request.json()

    // Log incoming data for debugging
    console.log('📧 Received email request:', {
      recipient: body.recipient_email,
      subject: body.subject,
      hasTextContent: !!body.text_content,
      hasHtmlContent: !!body.html_content,
      logToWebhook: body.log_to_webhook,
      sourcesCount: body.sources?.length || 0,
      webhookUrl: process.env.N8N_WEBHOOK_URL
    })

    // Validate required fields
    if (!body.recipient_email || !body.subject || !body.text_content) {
      return NextResponse.json(
        { error: 'Missing required fields: recipient_email, subject, text_content' },
        { status: 400 }
      )
    }

    // Validate email format
    if (!validateEmail(body.recipient_email)) {
      return NextResponse.json(
        { error: 'Invalid email address format' },
        { status: 400 }
      )
    }

    // Get SMTP credentials from environment variables
    const smtpHost = process.env.SMTP_HOST
    const smtpPort = parseInt(process.env.SMTP_PORT || '465')
    const smtpUser = process.env.SMTP_USER
    const smtpPass = process.env.SMTP_PASS
    const smtpSecure = process.env.SMTP_SECURE === 'true'
    const fromEmail = process.env.FROM_EMAIL || smtpUser
    const fromName = body.sender_name || process.env.FROM_NAME || 'Our Kidz'

    console.log('📧 SMTP Configuration:', {
      host: smtpHost,
      port: smtpPort,
      user: smtpUser,
      hasPassword: !!smtpPass,
      fromEmail,
      fromName,
      secure: smtpSecure
    })

    if (!smtpUser || !smtpPass || !smtpHost) {
      console.error('❌ SMTP credentials not configured')
      return NextResponse.json(
        { error: 'Email service not configured' },
        { status: 500 }
      )
    }

    // Create transporter
    console.log('🔧 Creating SMTP transporter...')
    const transporter = nodemailer.createTransport({
      host: smtpHost,
      port: smtpPort,
      secure: smtpSecure,
      auth: {
        user: smtpUser,
        pass: smtpPass,
      },
    })

    // Test the connection
    try {
      console.log('🔌 Testing SMTP connection...')
      await transporter.verify()
      console.log('✅ SMTP connection verified successfully')
    } catch (verifyError) {
      console.error('❌ SMTP connection verification failed:', verifyError)
      return NextResponse.json(
        { 
          error: 'SMTP connection failed',
          details: verifyError instanceof Error ? verifyError.message : 'Unknown SMTP error'
        },
        { status: 500 }
      )
    }

    // Prepare email options
    const mailOptions = {
      from: `"${fromName}" <${fromEmail}>`,
      to: body.recipient_email,
      subject: body.subject,
      text: body.text_content,
      html: body.html_content || undefined
    }

    console.log('📮 Sending email with options:', {
      from: mailOptions.from,
      to: mailOptions.to,
      subject: mailOptions.subject,
      textLength: body.text_content.length,
      htmlLength: body.html_content?.length || 0
    })

    // Send email
    const info = await transporter.sendMail(mailOptions)
    console.log('✅ Email sent successfully:', {
      messageId: info.messageId,
      response: info.response
    })

    // Log to webhook if requested (defaults to true)
    if (body.log_to_webhook !== false) { // Default to true unless explicitly set to false
      console.log('📡 Logging email to N8N webhook...')
      await logEmailToWebhook(
        body.recipient_email,
        body.subject,
        true,
        info.messageId,
        undefined,
        body.webhook_url,
        body.sources
      )
    } else {
      console.log('⚠️ Webhook logging disabled for this email')
    }

    return NextResponse.json({
      success: true,
      message: 'Email sent successfully',
      messageId: info.messageId,
      recipient: body.recipient_email,
      subject: body.subject,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ Error sending email:', error)
    
    // Log error to webhook if logging was requested
    try {
      const body = await request.json().catch(() => ({}))
      if (body.log_to_webhook !== false && body.recipient_email && body.subject) {
        console.log('📡 Logging email error to N8N webhook...')
        await logEmailToWebhook(
          body.recipient_email,
          body.subject,
          false,
          undefined,
          error instanceof Error ? error.message : 'Unknown error',
          body.webhook_url,
          body.sources
        )
      }
    } catch (webhookError) {
      console.error('❌ Failed to log error to webhook:', webhookError)
    }
    
    return NextResponse.json(
      { 
        error: 'Failed to send email',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
