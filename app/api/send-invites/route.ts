import { NextRequest, NextResponse } from 'next/server'
import nodemailer from 'nodemailer'
import path from 'path'
import fs from 'fs'
import { emailTemplates } from '@/lib/email-templates'

// Rate limiting store (in production, use Redis or a database)
const rateLimitStore = new Map<string, { count: number; lastRequest: number }>()

// Rate limiting configuration
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 5 // 5 invite batches per minute

function checkRateLimit(clientIP: string): boolean {
  const now = Date.now()
  const clientData = rateLimitStore.get(clientIP)

  if (!clientData) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  // Reset count if window has passed
  if (now - clientData.lastRequest > RATE_LIMIT_WINDOW) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  // Check if under limit
  if (clientData.count < RATE_LIMIT_MAX_REQUESTS) {
    clientData.count++
    clientData.lastRequest = now
    return true
  }

  return false
}

function isValidEmail(email: string): boolean {
  return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
}

export async function POST(request: NextRequest) {
  try {
    // Get client IP for rate limiting
    const clientIP = request.headers.get('x-forwarded-for')?.split(',')[0] || 
      request.headers.get('x-real-ip') || 
      'unknown'

    // Check rate limit
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please wait before sending more invites.' },
        { status: 429 }
      )
    }

    const body = await request.json()
    const { emails, personalMessage = '' } = body

    // Validation
    if (!emails || !Array.isArray(emails) || emails.length === 0) {
      return NextResponse.json(
        { error: 'At least one email address is required' },
        { status: 400 }
      )
    }

    if (emails.length > 10) {
      return NextResponse.json(
        { error: 'Maximum 10 emails per batch' },
        { status: 400 }
      )
    }

    // Validate all emails
    const invalidEmails = emails.filter(email => !isValidEmail(email))
    if (invalidEmails.length > 0) {
      return NextResponse.json(
        { error: `Invalid email addresses: ${invalidEmails.join(', ')}` },
        { status: 400 }
      )
    }

    // Check for required environment variables
    const requiredEnvVars = ['SMTP_HOST', 'SMTP_PORT', 'SMTP_USER', 'SMTP_PASS']
    const missingEnvVars = requiredEnvVars.filter(varName => !process.env[varName])

    if (missingEnvVars.length > 0) {
      console.error('❌ Missing environment variables:', missingEnvVars)
      console.error('📋 Environment check:', {
        SMTP_HOST: process.env.SMTP_HOST ? 'SET' : 'MISSING',
        SMTP_PORT: process.env.SMTP_PORT ? 'SET' : 'MISSING',
        SMTP_USER: process.env.SMTP_USER ? 'SET' : 'MISSING',
        SMTP_PASS: process.env.SMTP_PASS ? 'SET' : 'MISSING',
        NODE_ENV: process.env.NODE_ENV
      })
      return NextResponse.json(
        {
          error: 'Email service configuration error',
          details: `Missing environment variables: ${missingEnvVars.join(', ')}`,
          environment: process.env.NODE_ENV
        },
        { status: 500 }
      )
    }

    // Create transporter
    const transporter = nodemailer.createTransport({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT || '587'),
      secure: process.env.SMTP_SECURE === 'true',
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    })

    // Read and attach logo
    const logoPath = path.join(process.cwd(), 'public', 'OKdarkTsp.png')
    let logoAttachment = null
    
    if (fs.existsSync(logoPath)) {
      logoAttachment = {
        filename: 'ourkidz-logo.png',
        path: logoPath,
        cid: 'ourkidz-logo'
      }
    }

    const results = []

    // Send invites
    for (const email of emails) {
      try {
        // Generate email using template system (includes Founders Huddle card)
        const emailContent = emailTemplates.invite(email, personalMessage)

        const mailOptions = {
          from: `"Our Kidz" <${process.env.SMTP_USER}>`,
          to: email,
          subject: emailContent.subject,
          html: emailContent.html,
          text: emailContent.text,
          attachments: logoAttachment ? [logoAttachment] : []
        }

        await transporter.sendMail(mailOptions)
        results.push({ email, status: 'sent' })

        // Log to n8n webhook if configured
        if (process.env.N8N_WEBHOOK_URL) {
          try {
            await fetch(process.env.N8N_WEBHOOK_URL, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                type: 'invite_sent',
                to: email,
                timestamp: new Date().toISOString(),
                personalMessage: personalMessage || null
              })
            })
          } catch (webhookError) {
            console.warn('⚠️ Failed to log to n8n webhook:', webhookError)
          }
        }

      } catch (emailError) {
        console.error(`❌ Failed to send invite to ${email}:`, emailError)
        results.push({ email, status: 'failed', error: emailError instanceof Error ? emailError.message : 'Unknown error' })
      }
    }

    const successCount = results.filter(r => r.status === 'sent').length
    const failureCount = results.filter(r => r.status === 'failed').length

    console.log(`✅ Invite batch completed: ${successCount} sent, ${failureCount} failed`)

    return NextResponse.json({
      success: true,
      message: `${successCount} invite${successCount !== 1 ? 's' : ''} sent successfully`,
      results,
      summary: {
        total: emails.length,
        sent: successCount,
        failed: failureCount
      }
    })

  } catch (error) {
    console.error('❌ Error in send-invites API:', error)
    return NextResponse.json(
      { 
        error: 'Failed to send invites',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}



export async function GET() {
  return NextResponse.json({
    message: 'Send Invites API Endpoint',
    description: 'POST to this endpoint to send Our Kidz platform invitations',
    usage: {
      method: 'POST',
      body: {
        emails: ['<EMAIL>', '<EMAIL>'],
        personalMessage: 'Optional personal message (string)'
      }
    },
    features: [
      'Send up to 10 invites per batch',
      'Rate limiting (5 batches per minute)',
      'Email validation',
      'Personal message support',
      'Professional HTML email templates',
      'N8N webhook integration for logging'
    ]
  })
}
