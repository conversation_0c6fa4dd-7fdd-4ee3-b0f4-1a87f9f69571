/**
 * Personalized Mae API Route
 * 
 * Provides a comprehensive API for managing personalized Mae sessions,
 * including session initialization, context management, and conversation tracking.
 */

import { NextRequest, NextResponse } from 'next/server'
import { userContextService, generatePersonalizedSystemInstruction } from '@/lib/user-context-service'
import { sessionManager, createSessionResumePrompt } from '@/lib/session-management'

export async function POST(request: NextRequest) {
  try {
    const { action, auth_user_id, session_data } = await request.json()

    console.log('🤖 Personalized Mae API called:', { action, auth_user_id })

    if (!auth_user_id) {
      return NextResponse.json(
        { error: 'auth_user_id is required' },
        { status: 400 }
      )
    }

    switch (action) {
      case 'initialize_session':
        return await initializePersonalizedSession(auth_user_id, session_data)
      
      case 'get_user_context':
        return await getUserContext(auth_user_id)
      
      case 'resume_session':
        return await resumeSession(auth_user_id, session_data)
      
      case 'save_conversation':
        return await saveConversation(auth_user_id, session_data)
      
      case 'get_conversation_history':
        return await getConversationHistory(auth_user_id, session_data?.limit || 10)
      
      case 'create_session_summary':
        return await createSessionSummary(auth_user_id, session_data)
      
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('❌ Error in Personalized Mae API:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * Initialize a new personalized Mae session
 */
async function initializePersonalizedSession(auth_user_id: string, session_data: any) {
  try {
    console.log('🚀 Initializing personalized Mae session for:', auth_user_id)

    // Load user context
    const userContext = await userContextService.getUserContext(auth_user_id)
    
    if (!userContext) {
      return NextResponse.json(
        { error: 'User context not found' },
        { status: 404 }
      )
    }

    // Generate personalized system instruction
    const personalizedInstruction = generatePersonalizedSystemInstruction(userContext)
    
    // Get conversation history for context
    const recentMessages = await userContextService.getConversationHistory(auth_user_id, 10)
    const conversationSummary = sessionManager.compressConversationHistory(recentMessages)
    
    // Generate contextual greeting
    const contextualGreeting = sessionManager.generateContextualGreeting(userContext, recentMessages)
    
    // Create session resume prompt if there's history
    const sessionResumePrompt = recentMessages.length > 0 
      ? createSessionResumePrompt(userContext, conversationSummary)
      : ''

    // Generate unique session ID
    const session_id = `mae_personalized_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    console.log('✅ Personalized session initialized:', {
      user: userContext.user.name,
      familyMembers: userContext.family_members.length,
      hasHistory: recentMessages.length > 0,
      session_id
    })

    return NextResponse.json({
      success: true,
      session_id,
      user_context: userContext,
      personalized_instruction: personalizedInstruction,
      conversation_summary: conversationSummary,
      contextual_greeting: contextualGreeting,
      session_resume_prompt: sessionResumePrompt,
      has_conversation_history: recentMessages.length > 0,
      message: `Personalized Mae session ready for ${userContext.user.name}`
    })

  } catch (error) {
    console.error('❌ Error initializing personalized session:', error)
    return NextResponse.json(
      { error: 'Failed to initialize personalized session' },
      { status: 500 }
    )
  }
}

/**
 * Get user context for Mae personalization
 */
async function getUserContext(auth_user_id: string) {
  try {
    const userContext = await userContextService.getUserContext(auth_user_id)
    
    if (!userContext) {
      return NextResponse.json(
        { error: 'User context not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      user_context: userContext
    })

  } catch (error) {
    console.error('❌ Error getting user context:', error)
    return NextResponse.json(
      { error: 'Failed to get user context' },
      { status: 500 }
    )
  }
}

/**
 * Resume an existing Mae session with context
 */
async function resumeSession(auth_user_id: string, session_data: any) {
  try {
    console.log('🔄 Resuming Mae session for:', auth_user_id)

    const resumeData = await sessionManager.resumeSession(
      auth_user_id, 
      session_data?.include_history !== false
    )

    if (!resumeData.userContext) {
      return NextResponse.json(
        { error: 'User not found or no context available' },
        { status: 404 }
      )
    }

    console.log('✅ Session resumed for:', resumeData.userContext.user.name)

    return NextResponse.json({
      success: true,
      ...resumeData,
      message: `Session resumed for ${resumeData.userContext.user.name}`
    })

  } catch (error) {
    console.error('❌ Error resuming session:', error)
    return NextResponse.json(
      { error: 'Failed to resume session' },
      { status: 500 }
    )
  }
}

/**
 * Save conversation data to the database
 */
async function saveConversation(auth_user_id: string, session_data: any) {
  try {
    const { session_id, messages, metadata } = session_data

    if (!session_id || !messages) {
      return NextResponse.json(
        { error: 'session_id and messages are required' },
        { status: 400 }
      )
    }

    const success = await userContextService.saveConversationSession(
      auth_user_id,
      session_id,
      messages,
      metadata
    )

    if (!success) {
      return NextResponse.json(
        { error: 'Failed to save conversation' },
        { status: 500 }
      )
    }

    console.log('✅ Conversation saved:', session_id)

    return NextResponse.json({
      success: true,
      message: 'Conversation saved successfully'
    })

  } catch (error) {
    console.error('❌ Error saving conversation:', error)
    return NextResponse.json(
      { error: 'Failed to save conversation' },
      { status: 500 }
    )
  }
}

/**
 * Get conversation history for a user
 */
async function getConversationHistory(auth_user_id: string, limit: number = 10) {
  try {
    const messages = await userContextService.getConversationHistory(auth_user_id, limit)

    return NextResponse.json({
      success: true,
      messages,
      count: messages.length
    })

  } catch (error) {
    console.error('❌ Error getting conversation history:', error)
    return NextResponse.json(
      { error: 'Failed to get conversation history' },
      { status: 500 }
    )
  }
}

/**
 * Create a session summary
 */
async function createSessionSummary(auth_user_id: string, session_data: any) {
  try {
    const { session_id, messages, start_time, end_time } = session_data

    if (!session_id || !messages || !start_time) {
      return NextResponse.json(
        { error: 'session_id, messages, and start_time are required' },
        { status: 400 }
      )
    }

    const summary = sessionManager.createSessionSummary(
      session_id,
      auth_user_id,
      messages,
      start_time,
      end_time
    )

    console.log('✅ Session summary created:', session_id)

    return NextResponse.json({
      success: true,
      summary
    })

  } catch (error) {
    console.error('❌ Error creating session summary:', error)
    return NextResponse.json(
      { error: 'Failed to create session summary' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const action = searchParams.get('action')
    const auth_user_id = searchParams.get('auth_user_id')

    console.log('🤖 Personalized Mae GET:', { action, auth_user_id })

    if (!auth_user_id) {
      return NextResponse.json(
        { error: 'auth_user_id is required' },
        { status: 400 }
      )
    }

    switch (action) {
      case 'get_user_context':
        return await getUserContext(auth_user_id)
      
      case 'get_conversation_history':
        const limit = parseInt(searchParams.get('limit') || '10')
        return await getConversationHistory(auth_user_id, limit)
      
      case 'check_personalization_status':
        return await checkPersonalizationStatus(auth_user_id)
      
      default:
        return NextResponse.json({
          message: 'Personalized Mae API',
          available_actions: [
            'initialize_session',
            'get_user_context', 
            'resume_session',
            'save_conversation',
            'get_conversation_history',
            'create_session_summary',
            'check_personalization_status'
          ]
        })
    }
  } catch (error) {
    console.error('❌ Error in Personalized Mae GET:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * Check if user has personalization data available
 */
async function checkPersonalizationStatus(auth_user_id: string) {
  try {
    const userContext = await userContextService.getUserContext(auth_user_id)
    const conversationHistory = await userContextService.getConversationHistory(auth_user_id, 1)

    const status = {
      has_user_context: !!userContext,
      has_family_members: userContext?.family_members.length > 0,
      has_conversation_history: conversationHistory.length > 0,
      personalization_ready: !!userContext && userContext.family_members.length > 0,
      user_name: userContext?.user.name,
      family_member_count: userContext?.family_members.length || 0,
      conversation_count: conversationHistory.length
    }

    return NextResponse.json({
      success: true,
      status
    })

  } catch (error) {
    console.error('❌ Error checking personalization status:', error)
    return NextResponse.json(
      { error: 'Failed to check personalization status' },
      { status: 500 }
    )
  }
}