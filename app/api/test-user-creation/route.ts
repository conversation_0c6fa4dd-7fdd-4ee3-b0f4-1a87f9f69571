import { NextRequest, NextResponse } from 'next/server'
import { getOnboardingService } from '@/lib/supabase-client'

export async function GET(request: NextRequest) {
  try {
    console.log('Testing user creation...')
    
    // Test with minimal user object
    const testUser = {
      email: '<EMAIL>',
      name: 'Test User'
    }
    
    // Try to create the user
    const { data, error } = await getOnboardingService().createUser(testUser)
    
    if (error) {
      console.error('Error creating test user:', error)
      return NextResponse.json({
        success: false,
        error: error.message || error,
        details: error
      }, { status: 500 })
    }
    
    console.log('✅ Test user created successfully:', data)
    
    return NextResponse.json({
      success: true,
      message: 'Test user created successfully',
      user: data
    })
    
  } catch (error) {
    console.error('Test user creation failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}