import { NextResponse } from 'next/server'
import nodemailer from 'nodemailer'

export async function GET() {
  try {
    
    const testResult = {
      nodemailerType: typeof nodemailer,
      hasCreateTransporter: typeof nodemailer.createTransport === 'function',
      methods: Object.keys(nodemailer),
      version: (nodemailer as any).version || 'unknown'
    }
    
    // Try to create a basic transporter to test the function
    try {
      const transporter = nodemailer.createTransport({
        host: 'smtp.hostinger.com',
        port: 465,
        secure: true,
        auth: {
          user: '<EMAIL>',
          pass: 'Three110409!!*'
        }
      })
      
      testResult.transporterCreated = true
      testResult.transporterType = typeof transporter
      
    } catch (transporterError) {
      testResult.transporterError = transporterError instanceof Error ? transporterError.message : 'Unknown error'
    }
    
    return NextResponse.json(testResult)
    
  } catch (error) {
    return NextResponse.json({
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}