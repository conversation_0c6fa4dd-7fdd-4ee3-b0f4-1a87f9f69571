/**
 * Mae Agent API Endpoint - AG-UI Protocol Implementation
 * 
 * This endpoint implements the AG-UI HTTP protocol for <PERSON>, the AI pediatric health coach.
 * It accepts POST requests with RunAgentInput and returns a stream of AG-UI events.
 */

import { NextRequest, NextResponse } from 'next/server'
import { MaeAgent, RunAgentInput, AgentEvent } from '@/lib/mae-agent'
import { 
  onboardingFunctionDeclarations,
  handleCollectUserInfo,
  handleAddFamilyMember,
  handleUpdateOnboardingStep,
  handleCompleteOnboarding
} from '@/lib/onboarding-function-tools'

// Rate limiting
const requestCounts = new Map<string, { count: number; resetTime: number }>()
const RATE_LIMIT = 10 // requests per minute
const RATE_WINDOW = 60 * 1000 // 1 minute

function getClientIP(request: NextRequest): string {
  return request.headers.get('x-forwarded-for')?.split(',')[0] || 
         request.headers.get('x-real-ip') || 
         'unknown'
}

function checkRateLimit(clientIP: string): boolean {
  const now = Date.now()
  const clientData = requestCounts.get(clientIP)
  
  if (!clientData || now > clientData.resetTime) {
    requestCounts.set(clientIP, { count: 1, resetTime: now + RATE_WINDOW })
    return true
  }
  
  if (clientData.count >= RATE_LIMIT) {
    return false
  }
  
  clientData.count++
  return true
}

// Event encoder for Server-Sent Events
function encodeEvent(event: AgentEvent): string {
  return `data: ${JSON.stringify(event)}\n\n`
}

// POST handler for AG-UI protocol
export async function POST(request: NextRequest) {
  try {
    const clientIP = getClientIP(request)
    
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded' },
        { status: 429 }
      )
    }

    // Parse the AG-UI RunAgentInput
    const input: RunAgentInput = await request.json()
    
    // Validate required fields
    if (!input.threadId || !input.runId) {
      return NextResponse.json(
        { error: 'threadId and runId are required' },
        { status: 400 }
      )
    }

    // Create Mae agent instance
    const agent = new MaeAgent(
      'mae-onboarding-agent',
      input.threadId
    )

    // Add onboarding tools to the input
    const toolsWithOnboarding = [
      ...input.tools,
      ...onboardingFunctionDeclarations
    ]

    // Create a readable stream for Server-Sent Events
    const stream = new ReadableStream({
      start(controller) {
        // Run the agent and stream events
        const subscription = agent.runAgent({
          runId: input.runId,
          tools: toolsWithOnboarding,
          context: input.context,
          forwardedProps: input.forwardedProps
        }).subscribe({
          next: async (event: AgentEvent) => {
            try {
              // Handle tool calls by executing the actual functions
              if (event.type === 'TOOL_CALL_END') {
                await handleToolExecution(event, input, controller)
              } else {
                // Encode and send the event
                const encodedEvent = encodeEvent(event)
                controller.enqueue(new TextEncoder().encode(encodedEvent))
              }
            } catch (error) {
              console.error('Error processing event:', error)
            }
          },
          error: (error) => {
            console.error('Agent error:', error)
            const errorEvent = encodeEvent({
              type: 'RUN_ERROR' as any,
              messageId: `msg_${Date.now()}`,
              toolCallId: '',
              content: error.message || 'Unknown error',
              timestamp: Date.now()
            })
            controller.enqueue(new TextEncoder().encode(errorEvent))
            controller.close()
          },
          complete: () => {
            controller.close()
          }
        })

        // Handle client disconnect
        request.signal.addEventListener('abort', () => {
          subscription.unsubscribe()
          controller.close()
        })
      }
    })

    // Return the stream as Server-Sent Events
    return new Response(stream, {
      headers: {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST',
        'Access-Control-Allow-Headers': 'Content-Type'
      }
    })

  } catch (error) {
    console.error('Error in Mae agent endpoint:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// Handle tool execution
async function handleToolExecution(
  toolCallEvent: any,
  input: RunAgentInput,
  controller: ReadableStreamDefaultController
) {
  try {
    // Find the tool call in the conversation
    const toolCall = findToolCallById(toolCallEvent.toolCallId, input.messages)
    if (!toolCall) {
      console.warn('Tool call not found:', toolCallEvent.toolCallId)
      return
    }

    const { name, arguments: argsString } = toolCall.function
    const args = JSON.parse(argsString)

    let result: any

    // Execute the appropriate tool function
    switch (name) {
      case 'collect_user_information':
        result = await handleCollectUserInfo(args)
        break
      case 'add_family_member':
        result = await handleAddFamilyMember(args)
        break
      case 'update_onboarding_step':
        result = await handleUpdateOnboardingStep(args)
        break
      case 'complete_onboarding':
        result = await handleCompleteOnboarding(args)
        break
      default:
        result = {
          success: false,
          error: `Unknown tool: ${name}`
        }
    }

    // Send tool result event
    const resultEvent = encodeEvent({
      type: 'TOOL_CALL_RESULT' as any,
      messageId: `msg_${Date.now()}`,
      toolCallId: toolCallEvent.toolCallId,
      content: JSON.stringify(result),
      role: 'tool',
      timestamp: Date.now()
    })

    controller.enqueue(new TextEncoder().encode(resultEvent))

  } catch (error) {
    console.error('Error executing tool:', error)
    
    // Send error result
    const errorResult = encodeEvent({
      type: 'TOOL_CALL_RESULT' as any,
      messageId: `msg_${Date.now()}`,
      toolCallId: toolCallEvent.toolCallId,
      content: JSON.stringify({
        success: false,
        error: error instanceof Error ? error.message : 'Tool execution failed'
      }),
      role: 'tool',
      timestamp: Date.now()
    })

    controller.enqueue(new TextEncoder().encode(errorResult))
  }
}

// Helper function to find tool call by ID
function findToolCallById(toolCallId: string, messages: any[]): any {
  for (const message of messages) {
    if (message.toolCalls) {
      for (const toolCall of message.toolCalls) {
        if (toolCall.id === toolCallId) {
          return toolCall
        }
      }
    }
  }
  return null
}

// OPTIONS handler for CORS
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type'
    }
  })
}
