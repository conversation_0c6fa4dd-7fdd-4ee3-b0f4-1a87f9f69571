import { NextRequest, NextResponse } from 'next/server'
import { getOnboardingService } from '@/lib/supabase-client'

export async function GET(request: NextRequest) {
  try {
    console.log('Testing raw SQL insert...')
    
    // Try raw SQL insert to bypass foreign key issues
    const result = await getOnboardingService().getClient()
      .rpc('insert_user_safe', {
        user_email: '<EMAIL>',
        user_name: 'Test User'
      })
    
    if (result.error) {
      console.error('Raw SQL insert failed:', result.error)
      
      // Try a basic direct insert with minimal data
      const basicResult = await getOnboardingService().getClient()
        .from('users')
        .insert({
          id: crypto.randomUUID(),
          email: '<EMAIL>',
          name: 'Test User',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()
      
      if (basicResult.error) {
        return NextResponse.json({
          success: false,
          error: basicResult.error.message || basicResult.error,
          details: basicResult.error
        }, { status: 500 })
      }
      
      return NextResponse.json({
        success: true,
        message: 'Basic insert successful',
        user: basicResult.data
      })
    }
    
    return NextResponse.json({
      success: true,
      message: 'Raw SQL insert successful',
      result: result.data
    })
    
  } catch (error) {
    console.error('Test raw insert failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}