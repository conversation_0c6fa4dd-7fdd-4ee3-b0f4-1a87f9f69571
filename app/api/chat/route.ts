import { NextRequest, NextResponse } from 'next/server'
import { GoogleGenAI } from '@google/genai'

// Rate limiting store (in production, use Redis or a database)
const rateLimitStore = new Map<string, { count: number; lastRequest: number }>()

// Rate limiting configuration
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 10 // 10 requests per minute

function checkRateLimit(clientIP: string): boolean {
  const now = Date.now()
  const clientData = rateLimitStore.get(clientIP)

  if (!clientData) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  // Reset count if window has passed
  if (now - clientData.lastRequest > RATE_LIMIT_WINDOW) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  // Check if under limit
  if (clientData.count < RATE_LIMIT_MAX_REQUESTS) {
    clientData.count++
    clientData.lastRequest = now
    return true
  }

  return false
}

export async function POST(request: NextRequest) {
  try {
    // Get client IP for rate limiting
    const clientIP = request.headers.get('x-forwarded-for')?.split(',')[0] || 
      request.headers.get('x-real-ip') || 
      'unknown'

    // Check rate limit
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please wait before making another request.' },
        { status: 429 }
      )
    }

    const body = await request.json()
    const { message, conversation_history = [] } = body

    if (!message || typeof message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required and must be a string' },
        { status: 400 }
      )
    }

    // Get Gemini API key
    const apiKey = process.env.GEMINI_API_KEY
    if (!apiKey) {
      console.error('❌ GEMINI_API_KEY not found in environment variables')
      return NextResponse.json(
        { error: 'AI service configuration error' },
        { status: 500 }
      )
    }

    // Initialize Gemini AI
    const ai = new GoogleGenAI({ apiKey })

    // Build conversation history for context
    const chatHistory = conversation_history.map((msg: any) => ({
      role: msg.role === 'assistant' ? 'model' : 'user',
      parts: [{ text: msg.content }]
    }))

    // Add current message
    chatHistory.push({
      role: 'user',
      parts: [{ text: message }]
    })

    console.log('🤖 Sending request to Gemini with Google Search enabled...')

    // Generate response with Google Search capability
    const result = await ai.models.generateContent({
      model: 'gemini-live-2.5-flash-preview',
      contents: chatHistory,
      config: {
        tools: [{ googleSearch: {} }],
        systemInstruction: `You are Mae, an AI pediatric assistant for Our Kidz platform. You are knowledgeable, caring, and provide evidence-based advice for parents.

Key guidelines:
- Always search Google for current, evidence-based information before responding
- Provide helpful, research-backed advice
- Include sources when possible
- Be empathetic and understanding
- If it's a medical emergency, advise seeking immediate medical attention
- Keep responses conversational but professional
- Always prioritize child safety and well-being

Remember: You must ALWAYS use Google Search to find current information before providing advice. This ensures your responses are based on the latest research and guidelines.`
      }
    })

    const aiResponse = result.text

    console.log('✅ Received response from Gemini')

    return NextResponse.json({
      success: true,
      response: aiResponse,
      timestamp: new Date().toISOString()
    })

  } catch (error) {
    console.error('❌ Error in chat API:', error)
    
    // Log more detailed error information
    if (error instanceof Error) {
      console.error('❌ Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack?.substring(0, 500)
      })
    }
    
    return NextResponse.json(
      { 
        error: 'Failed to generate AI response',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Chat API Endpoint',
    description: 'POST to this endpoint to chat with Mae, the AI pediatric assistant',
    usage: {
      method: 'POST',
      body: {
        message: 'Your question or message (required)',
        conversation_history: '[{role: "user|assistant", content: "message"}] (optional)'
      }
    },
    features: [
      'Real-time AI responses using Gemini 2.0 Flash',
      'Google Search integration for current information',
      'Conversation history support',
      'Rate limiting protection',
      'Evidence-based pediatric advice'
    ]
  })
}
