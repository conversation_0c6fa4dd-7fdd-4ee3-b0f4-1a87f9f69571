/**
 * Mae Sessions API Route
 * 
 * Manages user-specific Mae conversation sessions, including session creation,
 * retrieval, and conversation history management for personalized experiences.
 */

import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'

// Initialize Supabase client with service role for full access
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

interface SessionRequest {
  action: 'create' | 'get' | 'update' | 'list' | 'delete'
  auth_user_id?: string
  session_id?: string
  session_data?: any
  limit?: number
}

export async function POST(request: NextRequest) {
  try {
    const body: SessionRequest = await request.json()
    const { action, auth_user_id, session_id, session_data, limit = 10 } = body

    console.log('📱 Mae Sessions API called:', { action, auth_user_id, session_id })

    // Validate required parameters
    if (!action) {
      return NextResponse.json(
        { error: 'Action is required' },
        { status: 400 }
      )
    }

    switch (action) {
      case 'create':
        return await createSession(auth_user_id!, session_data)
      
      case 'get':
        return await getSession(session_id!)
      
      case 'update':
        return await updateSession(session_id!, session_data)
      
      case 'list':
        return await listUserSessions(auth_user_id!, limit)
      
      case 'delete':
        return await deleteSession(session_id!)
      
      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }
  } catch (error) {
    console.error('❌ Error in Mae Sessions API:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const auth_user_id = searchParams.get('auth_user_id')
    const session_id = searchParams.get('session_id')
    const limit = parseInt(searchParams.get('limit') || '10')

    console.log('📱 Mae Sessions GET:', { auth_user_id, session_id, limit })

    if (session_id) {
      return await getSession(session_id)
    } else if (auth_user_id) {
      return await listUserSessions(auth_user_id, limit)
    } else {
      return NextResponse.json(
        { error: 'auth_user_id or session_id is required' },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('❌ Error in Mae Sessions GET:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * Create a new Mae session for a user
 */
async function createSession(auth_user_id: string, session_data: any) {
  try {
    // Get user email for session linking
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('email, name')
      .eq('auth_user_id', auth_user_id)
      .single()

    if (userError || !user) {
      console.error('❌ User not found for session creation:', userError)
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Generate unique session ID
    const session_id = `mae_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // Create session record
    const { data: session, error: sessionError } = await supabase
      .from('audio_chat_sessions')
      .insert({
        session_id,
        user_email: user.email,
        history: session_data?.initial_messages || [],
        metadata: {
          ...session_data?.metadata,
          auth_user_id,
          session_type: 'mae_personalized',
          user_name: user.name,
          created_by: 'mae_sessions_api',
          created_at: new Date().toISOString()
        }
      })
      .select()
      .single()

    if (sessionError) {
      console.error('❌ Error creating Mae session:', sessionError)
      return NextResponse.json(
        { error: 'Failed to create session' },
        { status: 500 }
      )
    }

    console.log('✅ Mae session created:', session_id)

    return NextResponse.json({
      success: true,
      session_id,
      session,
      message: `Mae session created for ${user.name}`
    })

  } catch (error) {
    console.error('❌ Error in createSession:', error)
    return NextResponse.json(
      { error: 'Failed to create session' },
      { status: 500 }
    )
  }
}

/**
 * Get a specific Mae session
 */
async function getSession(session_id: string) {
  try {
    const { data: session, error } = await supabase
      .from('audio_chat_sessions')
      .select('*')
      .eq('session_id', session_id)
      .single()

    if (error || !session) {
      console.error('❌ Session not found:', error)
      return NextResponse.json(
        { error: 'Session not found' },
        { status: 404 }
      )
    }

    console.log('✅ Mae session retrieved:', session_id)

    return NextResponse.json({
      success: true,
      session
    })

  } catch (error) {
    console.error('❌ Error in getSession:', error)
    return NextResponse.json(
      { error: 'Failed to get session' },
      { status: 500 }
    )
  }
}

/**
 * Update an existing Mae session
 */
async function updateSession(session_id: string, session_data: any) {
  try {
    const { data: session, error } = await supabase
      .from('audio_chat_sessions')
      .update({
        history: session_data?.history,
        metadata: {
          ...session_data?.metadata,
          updated_at: new Date().toISOString()
        }
      })
      .eq('session_id', session_id)
      .select()
      .single()

    if (error) {
      console.error('❌ Error updating Mae session:', error)
      return NextResponse.json(
        { error: 'Failed to update session' },
        { status: 500 }
      )
    }

    console.log('✅ Mae session updated:', session_id)

    return NextResponse.json({
      success: true,
      session
    })

  } catch (error) {
    console.error('❌ Error in updateSession:', error)
    return NextResponse.json(
      { error: 'Failed to update session' },
      { status: 500 }
    )
  }
}

/**
 * List all Mae sessions for a user
 */
async function listUserSessions(auth_user_id: string, limit: number) {
  try {
    // Get user email
    const { data: user } = await supabase
      .from('users')
      .select('email')
      .eq('auth_user_id', auth_user_id)
      .single()

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }

    // Get user's Mae sessions
    const { data: sessions, error } = await supabase
      .from('audio_chat_sessions')
      .select('session_id, created_at, updated_at, metadata, history')
      .eq('user_email', user.email)
      .eq('metadata->>session_type', 'mae_personalized')
      .order('created_at', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('❌ Error listing Mae sessions:', error)
      return NextResponse.json(
        { error: 'Failed to list sessions' },
        { status: 500 }
      )
    }

    // Transform sessions for better readability
    const transformedSessions = sessions?.map(session => ({
      session_id: session.session_id,
      created_at: session.created_at,
      updated_at: session.updated_at,
      message_count: Array.isArray(session.history) ? session.history.length : 0,
      last_topic: extractTopicFromHistory(session.history),
      duration: calculateSessionDuration(session.created_at, session.updated_at),
      metadata: session.metadata
    })) || []

    console.log('✅ Mae sessions listed for user:', { count: transformedSessions.length })

    return NextResponse.json({
      success: true,
      sessions: transformedSessions,
      total: transformedSessions.length
    })

  } catch (error) {
    console.error('❌ Error in listUserSessions:', error)
    return NextResponse.json(
      { error: 'Failed to list sessions' },
      { status: 500 }
    )
  }
}

/**
 * Delete a Mae session
 */
async function deleteSession(session_id: string) {
  try {
    const { error } = await supabase
      .from('audio_chat_sessions')
      .delete()
      .eq('session_id', session_id)

    if (error) {
      console.error('❌ Error deleting Mae session:', error)
      return NextResponse.json(
        { error: 'Failed to delete session' },
        { status: 500 }
      )
    }

    console.log('✅ Mae session deleted:', session_id)

    return NextResponse.json({
      success: true,
      message: 'Session deleted successfully'
    })

  } catch (error) {
    console.error('❌ Error in deleteSession:', error)
    return NextResponse.json(
      { error: 'Failed to delete session' },
      { status: 500 }
    )
  }
}

// Helper functions
function extractTopicFromHistory(history: any[]): string {
  if (!history || !Array.isArray(history) || history.length === 0) {
    return 'New conversation'
  }

  // Get the first user message to determine topic
  const userMessage = history.find(msg => msg.role === 'user')
  if (userMessage?.content) {
    const content = userMessage.content.toLowerCase()
    if (content.includes('sleep')) return 'Sleep concerns'
    if (content.includes('eat') || content.includes('food')) return 'Nutrition'
    if (content.includes('sick') || content.includes('fever')) return 'Health issues'
    if (content.includes('behavior') || content.includes('tantrum')) return 'Behavior'
    if (content.includes('development') || content.includes('milestone')) return 'Development'
    if (content.includes('doctor') || content.includes('appointment')) return 'Medical consultation'
    if (content.includes('onboard') || content.includes('setup')) return 'Account setup'
    return 'Parenting guidance'
  }

  return 'General conversation'
}

function calculateSessionDuration(created_at: string, updated_at: string): string {
  const start = new Date(created_at)
  const end = new Date(updated_at)
  const diffMs = end.getTime() - start.getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))

  if (diffMins < 1) return 'Less than 1 minute'
  if (diffMins === 1) return '1 minute'
  if (diffMins < 60) return `${diffMins} minutes`
  
  const hours = Math.floor(diffMins / 60)
  const remainingMins = diffMins % 60
  
  if (hours === 1 && remainingMins === 0) return '1 hour'
  if (remainingMins === 0) return `${hours} hours`
  if (hours === 1) return `1 hour ${remainingMins} minutes`
  
  return `${hours} hours ${remainingMins} minutes`
}