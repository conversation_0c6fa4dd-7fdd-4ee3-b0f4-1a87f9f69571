import { NextRequest, NextResponse } from 'next/server'
import { debugEnvironmentVariables } from '@/lib/debug-env'

export async function GET(request: NextRequest) {
  try {
    // Debug environment variables
    debugEnvironmentVariables()
    
    // Test environment variables
    const requiredVars = [
      'NEXT_PUBLIC_SUPABASE_URL',
      'NEXT_PUBLIC_SUPABASE_ANON_KEY',
      'SUPABASE_SERVICE_ROLE_KEY'
    ]
    
    const missingVars = requiredVars.filter(varName => !process.env[varName])
    
    if (missingVars.length > 0) {
      return NextResponse.json({
        success: false,
        error: `Missing environment variables: ${missingVars.join(', ')}`,
        available_vars: Object.keys(process.env).filter(key => key.includes('SUPABASE'))
      }, { status: 500 })
    }
    
    // Test Supabase connection
    const { getOnboardingService } = await import('@/lib/supabase-client')
    
    // Try a simple operation
    const result = await getOnboardingService().getClient()
      .from('users')
      .select('count')
      .limit(1)
    
    return NextResponse.json({
      success: true,
      message: 'Supabase connection successful',
      environment: process.env.NODE_ENV,
      supabase_url: process.env.NEXT_PUBLIC_SUPABASE_URL,
      has_service_key: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
      test_query_result: result.error ? `Error: ${result.error.message}` : 'Success'
    })
    
  } catch (error) {
    console.error('Supabase test error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}