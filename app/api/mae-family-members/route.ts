import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { currentUser } from '@clerk/nextjs/server'

export async function POST(request: NextRequest) {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY
    
    if (!supabaseUrl || !supabaseKey) {
      return NextResponse.json({ 
        success: false, 
        error: 'Database configuration missing' 
      }, { status: 500 })
    }
    
    const supabase = createClient(supabaseUrl, supabaseKey)
    const { user_id, name, date_of_birth, gender, relationship, medical_conditions, allergies, medications, additional_notes, is_primary } = await request.json()
    
    // Validate required fields
    if (!user_id || !name || !date_of_birth || !relationship) {
      return NextResponse.json({ 
        success: false, 
        error: 'Missing required fields: user_id, name, date_of_birth, relationship' 
      }, { status: 400 })
    }

    console.log('👨‍👩‍👧‍👦 Mae adding family member:', { user_id, name, relationship })

    // Validate that user_id is a proper UUID
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(user_id)) {
      return NextResponse.json({ 
        success: false, 
        error: 'Invalid user_id format. Must be a valid UUID.' 
      }, { status: 400 })
    }

    // Verify the user exists
    const { data: userData, error: userError } = await supabase
      .from('users')
      .select('id, email, name')
      .eq('id', user_id)
      .single()

    if (userError || !userData) {
      console.error('❌ User not found:', user_id, userError)
      return NextResponse.json({ 
        success: false, 
        error: 'User not found in database' 
      }, { status: 404 })
    }

    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/
    if (!dateRegex.test(date_of_birth)) {
      return NextResponse.json({ 
        success: false, 
        error: 'Invalid date format. Use YYYY-MM-DD.' 
      }, { status: 400 })
    }

    // Insert family member
    const familyMemberData = {
      user_id,
      name: name.trim(),
      date_of_birth,
      gender: gender || null,
      relationship: relationship.toLowerCase(),
      medical_conditions: medical_conditions || [],
      allergies: allergies || [],
      medications: medications || [],
      additional_notes: additional_notes || null,
      is_primary: is_primary || false
    }

    const { data: newFamilyMember, error: insertError } = await supabase
      .from('family_members')
      .insert(familyMemberData)
      .select()
      .single()

    if (insertError) {
      console.error('❌ Error inserting family member:', insertError)
      return NextResponse.json({ 
        success: false, 
        error: 'Failed to add family member to database',
        details: insertError.message
      }, { status: 500 })
    }

    console.log('✅ Family member added successfully:', newFamilyMember.id)

    return NextResponse.json({
      success: true,
      message: `Successfully added ${name} as a family member`,
      family_member: {
        id: newFamilyMember.id,
        name: newFamilyMember.name,
        date_of_birth: newFamilyMember.date_of_birth,
        relationship: newFamilyMember.relationship,
        user_id: newFamilyMember.user_id
      }
    })

  } catch (error) {
    console.error('❌ Error in Mae family member API:', error)
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// GET endpoint to retrieve family members for a user
export async function GET(request: NextRequest) {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY
    
    if (!supabaseUrl || !supabaseKey) {
      return NextResponse.json({ 
        success: false, 
        error: 'Database configuration missing' 
      }, { status: 500 })
    }
    
    const supabase = createClient(supabaseUrl, supabaseKey)
    const { searchParams } = new URL(request.url)
    const user_id = searchParams.get('user_id')
    
    if (!user_id) {
      return NextResponse.json({ 
        success: false, 
        error: 'user_id parameter is required' 
      }, { status: 400 })
    }

    // Validate that user_id is a proper UUID
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(user_id)) {
      return NextResponse.json({ 
        success: false, 
        error: 'Invalid user_id format. Must be a valid UUID.' 
      }, { status: 400 })
    }

    // Get family members
    const { data: familyMembers, error: familyError } = await supabase
      .from('family_members')
      .select('*')
      .eq('user_id', user_id)
      .order('created_at', { ascending: false })

    if (familyError) {
      console.error('❌ Error fetching family members:', familyError)
      return NextResponse.json({ 
        success: false, 
        error: 'Failed to fetch family members',
        details: familyError.message
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      family_members: familyMembers || [],
      count: familyMembers?.length || 0
    })

  } catch (error) {
    console.error('❌ Error in Mae family member GET API:', error)
    return NextResponse.json({ 
      success: false, 
      error: 'Internal server error',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
