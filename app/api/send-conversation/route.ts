import { NextRequest, NextResponse } from 'next/server'
import * as fs from 'fs'
import * as path from 'path'
import nodemailer from 'nodemailer'

// Rate limiting store (in production, use Redis or a database)
const rateLimitStore = new Map<string, { count: number; lastRequest: number }>()

// Rate limiting configuration
const RATE_LIMIT_WINDOW = 60 * 1000 // 1 minute
const RATE_LIMIT_MAX_REQUESTS = 5 // 5 emails per minute per IP

interface ConversationData {
  recipient_email: string
  subject?: string
  user_question: string
  ai_response: string
  timestamp: string
  session_id?: string
  include_context?: boolean
  conversation_context?: string
  sources?: { title: string; url: string }[]
}

function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

function isValidUUID(uuid: string): boolean {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i
  return uuidRegex.test(uuid)
}

function checkRateLimit(clientIP: string): boolean {
  const now = Date.now()
  const clientData = rateLimitStore.get(clientIP)

  if (!clientData) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  // Reset count if window has passed
  if (now - clientData.lastRequest > RATE_LIMIT_WINDOW) {
    rateLimitStore.set(clientIP, { count: 1, lastRequest: now })
    return true
  }

  // Check if within rate limit
  if (clientData.count >= RATE_LIMIT_MAX_REQUESTS) {
    return false
  }

  // Increment count
  clientData.count++
  clientData.lastRequest = now
  return true
}

export async function POST(request: NextRequest) {
  try {
    // Get client IP for rate limiting
    const clientIP = request.ip || 
      request.headers.get('x-forwarded-for')?.split(',')[0] || 
      request.headers.get('x-real-ip') || 
      'unknown'

    // Check rate limit
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please wait before sending another email.' },
        { status: 429 }
      )
    }

    const body: ConversationData = await request.json()

    // Log incoming data for debugging
    console.log('📧 Received email request:', {
      recipient: body.recipient_email,
      hasQuestion: !!body.user_question,
      hasResponse: !!body.ai_response,
      hasSources: !!(body.sources && body.sources.length > 0),
      sourcesCount: body.sources?.length || 0,
      sources: body.sources
    })

    // Validate required fields
    if (!body.recipient_email || !body.user_question || !body.ai_response) {
      return NextResponse.json(
        { error: 'Missing required fields: recipient_email, user_question, ai_response' },
        { status: 400 }
      )
    }

    // Validate email format
    if (!validateEmail(body.recipient_email)) {
      return NextResponse.json(
        { error: 'Invalid email address format' },
        { status: 400 }
      )
    }

    // Get SMTP credentials from environment variables (use exact values provided)
    const smtpHost = process.env.SMTP_HOST || 'smtp.hostinger.com'
    const smtpPort = parseInt(process.env.SMTP_PORT || '465')
    const smtpUser = process.env.SMTP_USER || '<EMAIL>'
    const smtpPass = process.env.SMTP_PASS || 'Three110409!!*'
    const fromEmail = process.env.FROM_EMAIL || '<EMAIL>'
    const fromName = process.env.FROM_NAME || 'Our Kidz'

    console.log('📧 SMTP Configuration:', {
      host: smtpHost,
      port: smtpPort,
      user: smtpUser,
      hasPassword: !!smtpPass,
      fromEmail,
      fromName
    })

    if (!smtpUser || !smtpPass) {
      console.error('❌ SMTP credentials not configured')
      return NextResponse.json(
        { error: 'Email service not configured' },
        { status: 500 }
      )
    }

    // Create transporter
    console.log('🔧 Creating SMTP transporter...')
    console.log('📦 Nodemailer type:', typeof nodemailer)
    console.log('📦 Nodemailer methods:', Object.keys(nodemailer))
    
    const transporter = nodemailer.createTransport({
      host: smtpHost,
      port: smtpPort,
      secure: true, // true for 465, false for other ports
      auth: {
        user: smtpUser,
        pass: smtpPass,
      },
      debug: true, // Enable debug logging
      logger: true // Enable logger
    })

    // Test the connection
    try {
      console.log('🔌 Testing SMTP connection...')
      await transporter.verify()
      console.log('✅ SMTP connection verified successfully')
    } catch (verifyError) {
      console.error('❌ SMTP connection verification failed:', verifyError)
      return NextResponse.json(
        { 
          error: 'SMTP connection failed',
          details: verifyError instanceof Error ? verifyError.message : 'Unknown SMTP error'
        },
        { status: 500 }
      )
    }

    // Format email content
    const subject = body.subject || 'Your Our Kidz AI Conversation Summary'
    const formattedTimestamp = new Date(body.timestamp).toLocaleString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      timeZoneName: 'short'
    }).replace(' at ', ' at ').replace(',', ' at')

    let emailHTML = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Our Kidz Conversation Summary</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        .container {
            background-color: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #00bba7;
            padding-bottom: 20px;
            margin-bottom: 30px;
            position: relative;
        }
        .logo {
            color: #00bba7;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .tagline {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
        }
        .header-actions {
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            margin-top: 10px;
        }
        .new-button {
            background-color: #3b82f6;
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
        }
        .invite-link {
            background-color: #14b8a6;
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            text-decoration: none;
        }
        .logo-image {
            width: 48px;
            height: 48px;
            margin: 0 auto 10px auto;
            display: block;
        }
        .timestamp {
            color: #666;
            font-size: 14px;
            margin-bottom: 20px;
        }
        .conversation-section {
            margin-bottom: 25px;
            padding: 20px;
            border-radius: 8px;
        }
        .user-section {
            background-color: #f0f9ff;
            border-left: 4px solid #0ea5e9;
            border-radius: 8px;
        }
        .ai-section {
            background-color: #f0fdf4;
            border-left: 4px solid #00bba7;
            border-radius: 8px;
        }
        .section-title {
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 10px;
            font-size: 16px;
        }
        .content {
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .founders-huddle {
            background-color: #f0fdf4;
            border: 1px solid #14b8a6;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        .founders-huddle h3 {
            color: #1f2937;
            margin: 0 0 10px 0;
            font-size: 18px;
        }
        .founders-huddle p {
            color: #666;
            margin: 0 0 15px 0;
            line-height: 1.5;
        }
        .book-button {
            background-color: #14b8a6;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            display: inline-block;
        }
        .calendar-section {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .calendar-section p {
            margin: 0 0 8px 0;
            font-weight: 500;
        }
        .calendar-section a {
            color: #3b82f6;
            text-decoration: none;
            word-break: break-all;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #666;
            font-size: 14px;
        }
        .footer a {
            color: #00bba7;
            text-decoration: none;
        }

        /* Mobile responsiveness */
        @media only screen and (max-width: 600px) {
            .container {
                padding: 20px;
                margin: 10px;
            }
            .header-actions {
                flex-direction: column;
                gap: 10px;
            }
            .conversation-section {
                padding: 15px;
            }
            .founders-huddle {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="cid:ourkidz-logo" alt="Our Kidz Logo" class="logo-image" />
            <div class="logo">Our Kidz</div>
            <div class="tagline">The Smart, Agentic Companion Built for Today's Modern Families</div>
        </div>
        
        <div class="timestamp">
            <strong>Date:</strong> ${formattedTimestamp}
        </div>
        
        <div class="conversation-section user-section">
            <div class="section-title">Your Question:</div>
            <div class="content">${body.user_question}</div>
        </div>
        
        <div class="conversation-section ai-section">
            <div class="section-title">Mae's Response:</div>
            <div class="content">${body.ai_response}</div>
        </div>`

    // Add conversation context if included
    if (body.include_context && body.conversation_context) {
      emailHTML += `
        <div class="conversation-section" style="background-color: #fefce8; border-left: 4px solid #eab308;">
            <div class="section-title">Additional Context:</div>
            <div class="content">${body.conversation_context}</div>
        </div>`
    }

    // Add sources section if provided
    if (body.sources && body.sources.length > 0) {
      const sourcesHTML = body.sources
        .map(source => `<li><a href="${source.url}" target="_blank" rel="noopener noreferrer">${source.title || source.url}</a></li>`) 
        .join('')

      emailHTML += `
        <div class="conversation-section" style="background-color: #ecfdf5; border-left: 4px solid #14b8a6;">
            <div class="section-title">Sources:</div>
            <ul class="content">${sourcesHTML}</ul>
        </div>`
    }

    emailHTML += `
        <div class="founders-huddle">
            <h3>Founders 15-Minute Huddle</h3>
            <p>Secure a personal, 15-minute call with Our Kidz Founder. Share your chance to share feedback and influence what we build next.</p>
            <a href="https://cal.read.ai/our-kids/15-min" class="book-button" target="_blank" rel="noopener noreferrer">Book a time</a>
        </div>

        <div class="calendar-section">
            <p><strong>Calendar Link:</strong></p>
            <a href="https://cal.read.ai/our-kids/15-min" target="_blank" rel="noopener noreferrer">https://cal.read.ai/our-kids/15-min</a>
        </div>

        <div class="footer">
            <p>This conversation summary was generated by Our Kidz AI platform.</p>
            <p>Visit us at <a href="https://app.our-kidz.com">our kidz.com</a></p>
            <p>Your journey to health freedom starts here!</p>
        </div>
    </div>
</body>
</html>`

    // Text version for email clients that don't support HTML
    let emailText = `Our Kidz - AI Conversation Summary\n\n`
    emailText += `Conversation Date: ${formattedTimestamp}\n\n`
    emailText += `Your Question:\n${body.user_question}\n\n`
    emailText += `Mae's Response:\n${body.ai_response}\n\n`
    
    if (body.include_context && body.conversation_context) {
      emailText += `Additional Context:\n${body.conversation_context}\n\n`
    }
    
    if (body.sources && body.sources.length > 0) {
      emailText += `Sources:\n`
      body.sources.forEach(source => {
        emailText += `- ${source.title || source.url}: ${source.url}\n`
      })
      emailText += `\n`
    }

    emailText += `Founders 15-Minute Huddle\n`
    emailText += `Secure a personal, 15-minute call with Our Kidz Founder. Share your chance to share feedback and influence what we build next.\n`
    emailText += `Book a time: https://cal.read.ai/our-kids/15-min\n\n`

    emailText += `Calendar Link: https://cal.read.ai/our-kids/15-min\n\n`

    emailText += `This conversation summary was generated by Our Kidz AI platform.\n`
    emailText += `Visit us at https://our-kidz.com\n`
    emailText += `Your journey to health freedom starts here!`

    // Prepare image attachment
    let attachments = []
    try {
      const imagePath = path.join(process.cwd(), 'public', 'OKdarkTsp.png')
      if (fs.existsSync(imagePath)) {
        attachments.push({
          filename: 'OKdarkTsp.png',
          path: imagePath,
          cid: 'ourkidz-logo' // Content-ID for embedding in HTML
        })
        console.log('✅ Image attachment prepared:', imagePath)
      } else {
        console.warn('⚠️ Image file not found:', imagePath)
      }
    } catch (imageError) {
      console.error('❌ Error preparing image attachment:', imageError)
    }

    // Send email
    const mailOptions = {
      from: `"${fromName}" <${fromEmail}>`,
      to: body.recipient_email,
      subject: subject,
      text: emailText,
      html: emailHTML,
      attachments: attachments
    }

    console.log('📮 Sending email with options:', {
      from: mailOptions.from,
      to: mailOptions.to,
      subject: mailOptions.subject,
      textLength: emailText.length,
      htmlLength: emailHTML.length,
      attachmentsCount: attachments.length
    })

    const info = await transporter.sendMail(mailOptions)
    console.log('✅ Email sent successfully:', {
      messageId: info.messageId,
      response: info.response
    })

    // Save email interaction to database
    try {
      const supabaseProjectId = process.env.SUPABASE_PROJECT_ID
      const supabaseApiKey = process.env.SUPABASE_SERVICE_ROLE_KEY

      if (!supabaseProjectId) {
        console.warn('⚠️ SUPABASE_PROJECT_ID not configured, skipping database save')
      } else if (!supabaseApiKey) {
        console.warn('⚠️ SUPABASE_SERVICE_ROLE_KEY not configured, skipping database save')
      } else {
        console.log('💾 Saving email interaction to database...')

        // First, try to find the user by email to get their UUID
        let userId = null
        try {
          const userLookupUrl = `https://${supabaseProjectId}.supabase.co/rest/v1/users?email=eq.${encodeURIComponent(body.recipient_email)}&select=id`
          const userResponse = await fetch(userLookupUrl, {
            headers: {
              'Authorization': `Bearer ${supabaseApiKey}`,
              'apikey': supabaseApiKey
            }
          })

          if (userResponse.ok) {
            const users = await userResponse.json()
            if (users && users.length > 0) {
              userId = users[0].id
              console.log('✅ Found user ID for email:', { email: body.recipient_email, userId })
            } else {
              console.log('⚠️ No user found for email:', body.recipient_email)
            }
          }
        } catch (userLookupError) {
          console.error('❌ Error looking up user:', userLookupError)
        }

        // Generate a proper UUID for session_id or let database auto-generate
        const sessionData: any = {
          user_id: userId, // Include user_id if found
          user_email: body.recipient_email,
          history: [
            {
              role: 'user',
              content: body.user_question,
              timestamp: body.timestamp
            },
            {
              role: 'assistant',
              content: body.ai_response,
              timestamp: body.timestamp,
              sources: body.sources || []
            }
          ],
          metadata: {
            email_sent: true,
            email_message_id: info.messageId,
            email_subject: subject,
            email_timestamp: new Date().toISOString(),
            sources_count: body.sources?.length || 0,
            include_context: body.include_context || false,
            conversation_context: body.conversation_context || null,
            original_session_id: body.session_id || null, // Store original session ID in metadata
            user_lookup_attempted: true,
            user_found: userId !== null
          }
        }

        // Only include session_id if it's a valid UUID format, otherwise let DB generate one
        if (body.session_id && isValidUUID(body.session_id)) {
          sessionData.session_id = body.session_id
        }

        const supabaseUrl = `https://${supabaseProjectId}.supabase.co/rest/v1/audio_chat_sessions`

        const dbResponse = await fetch(supabaseUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${supabaseApiKey}`,
            'apikey': supabaseApiKey,
            'Prefer': 'return=minimal'
          },
          body: JSON.stringify(sessionData)
        })

        if (dbResponse.ok) {
          console.log('✅ Email interaction saved to database successfully')
        } else {
          const errorData = await dbResponse.text()
          console.error('❌ Failed to save email interaction to database:', {
            status: dbResponse.status,
            error: errorData
          })
        }
      }
    } catch (dbError) {
      console.error('❌ Error saving email interaction to database:', dbError)
      // Don't fail the email send if database save fails
    }

    // Trigger webhook (production-ready)
    try {
      const webhookUrl = process.env.N8N_WEBHOOK_URL || process.env.WEBHOOK_URL

      if (webhookUrl) {
        console.log('🚀 Triggering webhook...')
        const webhookResponse = await fetch(webhookUrl, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            event: 'email_sent',
            recipient: body.recipient_email,
            subject: subject,
            messageId: info.messageId,
            timestamp: body.timestamp,
            sessionId: body.session_id,
            sources: body.sources
          })
        })

        if (webhookResponse.ok) {
          console.log('✅ Webhook triggered successfully')
        } else {
          console.warn('⚠️ Webhook response not OK:', webhookResponse.status)
        }
      } else {
        console.log('ℹ️ No webhook URL configured, skipping webhook trigger')
      }
    } catch (webhookError) {
      console.error('❌ Error triggering webhook:', webhookError)
      // Don't fail the email send if webhook fails
    }

    return NextResponse.json({
      success: true,
      message: 'Conversation summary sent successfully',
      messageId: info.messageId,
      timestamp: body.timestamp
    })

  } catch (error) {
    console.error('❌ Error sending email:', error)
    
    // Log more detailed error information
    if (error instanceof Error) {
      console.error('❌ Error details:', {
        name: error.name,
        message: error.message,
        stack: error.stack?.substring(0, 500)
      })
    }
    
    return NextResponse.json(
      { 
        error: 'Failed to send email',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}