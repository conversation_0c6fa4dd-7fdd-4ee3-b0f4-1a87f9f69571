import { auth, currentUser } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'
import { syncUserWithSupabase } from '@/lib/user-sync-helper'

export async function GET() {
  try {
    // Get the current Clerk user
    let clerkUser
    let userId
    
    try {
      const authData = auth()
      userId = authData.userId
      clerkUser = await currentUser()
    } catch (authError) {
      console.error('Auth error:', authError)
      return NextResponse.json({ 
        error: 'Authentication failed', 
        details: 'Unable to verify Clerk session. Please sign out and sign back in.' 
      }, { status: 401 })
    }
    
    if (!userId || !clerkUser) {
      return NextResponse.json({ 
        error: 'Not authenticated', 
        details: 'No valid Clerk session found. Please sign in.' 
      }, { status: 401 })
    }

    console.log('🔄 Syncing user:', clerkUser.id, clerkUser.emailAddresses[0]?.emailAddress)

    // Get primary email
    const primaryEmail = clerkUser.emailAddresses.find(
      (email) => email.id === clerkUser.primaryEmailAddressId
    )?.emailAddress
    
    if (!primaryEmail) {
      return NextResponse.json({ error: 'No primary email found' }, { status: 400 })
    }

    // Get primary phone if exists
    const primaryPhone = clerkUser.phoneNumbers && clerkUser.phoneNumbers.length > 0 
      ? clerkUser.phoneNumbers.find((phone) => phone.id === clerkUser.primaryPhoneNumberId)?.phoneNumber || clerkUser.phoneNumbers[0]?.phoneNumber
      : null

    // Use the new sync helper
    const result = await syncUserWithSupabase({
      clerkId: clerkUser.id,
      email: primaryEmail,
      firstName: clerkUser.firstName,
      lastName: clerkUser.lastName,
      fullName: clerkUser.fullName || `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim() || 'User',
      phone: primaryPhone,
      imageUrl: clerkUser.imageUrl
    })

    return NextResponse.json(result)

  } catch (error) {
    console.error('❌ Sync error:', error)
    return NextResponse.json({ 
      error: 'Internal server error', 
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}