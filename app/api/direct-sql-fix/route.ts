import { NextRequest, NextResponse } from 'next/server'
import { getOnboardingService } from '@/lib/supabase-client'

export async function POST(request: NextRequest) {
  try {
    console.log('Direct SQL fix for users table...')
    
    const client = getOnboardingService().getClient()
    
    // Step 1: Try to alter the existing table to add UUID default
    console.log('Step 1: Adding UUID default to existing table...')
    
    const alterResult = await client.rpc('sql', {
      query: `
        DO $$
        BEGIN
          -- Enable uuid-ossp extension if not exists
          CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
          
          -- Add default UUID to id column
          ALTER TABLE users ALTER COLUMN id SET DEFAULT uuid_generate_v4();
          
        EXCEPTION WHEN OTHERS THEN
          -- If alter fails, we'll catch and continue
          NULL;
        END
        $$;
      `
    })
    
    console.log('Alter result:', alterResult)
    
    // Step 2: Test insert without ID to see if default works
    console.log('Step 2: Testing insert with DB-generated ID...')
    
    const testInsert = await client
      .from('users')
      .insert({
        email: '<EMAIL>',
        name: 'Direct Fix Test User'
      })
      .select()
      .single()
    
    if (testInsert.error) {
      console.log('DB default failed, trying manual UUID generation...')
      
      // Step 3: If default doesn't work, try manual UUID
      const manualInsert = await client
        .from('users')
        .insert({
          id: crypto.randomUUID(),
          email: '<EMAIL>',
          name: 'Manual UUID Test User',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single()
      
      if (manualInsert.error) {
        return NextResponse.json({
          success: false,
          error: 'Both DB default and manual UUID failed: ' + manualInsert.error.message,
          details: manualInsert.error
        }, { status: 500 })
      }
      
      return NextResponse.json({
        success: true,
        message: 'Manual UUID generation working',
        method: 'manual_uuid',
        testUser: manualInsert.data,
        alterResult: alterResult.data
      })
    }
    
    return NextResponse.json({
      success: true,
      message: 'Database UUID default working',
      method: 'db_default',
      testUser: testInsert.data,
      alterResult: alterResult.data
    })
    
  } catch (error) {
    console.error('Direct SQL fix failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}