import { auth, currentUser } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'
import { syncUserWithSupabase } from '@/lib/user-sync-helper'

// This endpoint is called by middleware or client-side to ensure user is synced
export async function POST() {
  try {
    console.log('🔄 Auth sync endpoint called')
    
    const { userId } = auth()
    
    if (!userId) {
      console.log('⚠️ No userId from auth()')
      return NextResponse.json({ error: 'Not authenticated' }, { status: 401 })
    }

    console.log('✅ Auth userId found:', userId)
    
    const clerkUser = await currentUser()
    
    if (!clerkUser) {
      console.log('❌ currentUser() returned null for userId:', userId)
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    console.log('🔄 Auth sync triggered for:', clerkUser.id)

    // Get primary email
    const primaryEmail = clerkUser.emailAddresses.find(
      (email) => email.id === clerkUser.primaryEmailAddressId
    )?.emailAddress

    if (!primaryEmail) {
      return NextResponse.json({ error: 'No primary email found' }, { status: 400 })
    }

    // Get primary phone
    const primaryPhone = clerkUser.phoneNumbers && clerkUser.phoneNumbers.length > 0 
      ? clerkUser.phoneNumbers.find((phone) => phone.id === clerkUser.primaryPhoneNumberId)?.phoneNumber || clerkUser.phoneNumbers[0]?.phoneNumber
      : null

    // Sync user with Supabase
    const result = await syncUserWithSupabase({
      clerkId: clerkUser.id,
      email: primaryEmail,
      firstName: clerkUser.firstName,
      lastName: clerkUser.lastName,
      fullName: clerkUser.fullName || `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim() || 'User',
      phone: primaryPhone,
      imageUrl: clerkUser.imageUrl
    })

    return NextResponse.json({
      success: true,
      sync: result,
      user: {
        clerkId: clerkUser.id,
        email: primaryEmail,
        name: result.user.name
      }
    })

  } catch (error) {
    console.error('❌ Auth sync error:', error)
    return NextResponse.json({ 
      error: 'Sync failed', 
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}