import { NextResponse } from 'next/server'
import nodemailer from 'nodemailer'

export async function GET() {
  const config = {
    SMTP_HOST: process.env.SMTP_HOST || 'NOT_SET',
    SMTP_PORT: process.env.SMTP_PORT || 'NOT_SET',
    SMTP_USER: process.env.SMTP_USER || 'NOT_SET',
    SMTP_PASS: process.env.SMTP_PASS ? 'SET' : 'NOT_SET',
    FROM_EMAIL: process.env.FROM_EMAIL || 'NOT_SET',
    FROM_NAME: process.env.FROM_NAME || 'NOT_SET'
  }

  return NextResponse.json(config)
}

export async function POST() {
  try {
    // Get SMTP credentials from environment variables
    const smtpHost = process.env.SMTP_HOST
    const smtpPort = parseInt(process.env.SMTP_PORT || '465')
    const smtpUser = process.env.SMTP_USER
    const smtpPass = process.env.SMTP_PASS
    const smtpSecure = process.env.SMTP_SECURE === 'true'

    console.log('🔧 Testing SMTP connection with config:', {
      host: smtpHost,
      port: smtpPort,
      user: smtpUser,
      secure: smtpSecure,
      hasPassword: !!smtpPass
    })

    if (!smtpUser || !smtpPass || !smtpHost) {
      return NextResponse.json(
        {
          success: false,
          error: 'SMTP credentials not configured',
          config: {
            host: smtpHost || 'NOT_SET',
            port: smtpPort,
            user: smtpUser || 'NOT_SET',
            hasPassword: !!smtpPass
          }
        },
        { status: 400 }
      )
    }

    // Create transporter
    const transporter = nodemailer.createTransport({
      host: smtpHost,
      port: smtpPort,
      secure: smtpSecure,
      auth: {
        user: smtpUser,
        pass: smtpPass,
      },
    })

    // Test the connection
    console.log('🔌 Testing SMTP connection...')
    await transporter.verify()
    console.log('✅ SMTP connection verified successfully')

    return NextResponse.json({
      success: true,
      message: 'SMTP connection verified successfully',
      config: {
        host: smtpHost,
        port: smtpPort,
        user: smtpUser,
        secure: smtpSecure
      }
    })

  } catch (error) {
    console.error('❌ SMTP connection test failed:', error)

    return NextResponse.json(
      {
        success: false,
        error: 'SMTP connection failed',
        details: error instanceof Error ? error.message : 'Unknown SMTP error'
      },
      { status: 500 }
    )
  }
}