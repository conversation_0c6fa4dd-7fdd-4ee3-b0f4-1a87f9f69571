import { NextRequest, NextResponse } from 'next/server'
import { getOnboardingService } from '@/lib/supabase-client'

export async function POST(request: NextRequest) {
  try {
    console.log('Fixing database schema...')
    
    const client = getOnboardingService().getClient()
    
    // 1. First, let's see what the current schema looks like
    const schemaInfo = await client.rpc('get_table_info', { table_name: 'users' })
    
    if (schemaInfo.error) {
      // Try to get constraint info directly
      const constraintQuery = await client
        .from('information_schema.table_constraints')
        .select('*')
        .eq('table_name', 'users')
        .eq('constraint_type', 'FOREIGN KEY')
      
      console.log('Constraint info:', constraintQuery)
    }
    
    // 2. Try to drop the problematic foreign key constraint
    const dropConstraint = await client.rpc('execute_sql', {
      sql: `ALTER TABLE users DROP CONSTRAINT IF EXISTS users_id_fkey;`
    })
    
    console.log('Drop constraint result:', dropConstraint)
    
    // 3. Ensure the ID column has a default UUID
    const addDefault = await client.rpc('execute_sql', {
      sql: `ALTER TABLE users ALTER COLUMN id SET DEFAULT gen_random_uuid();`
    })
    
    console.log('Add default result:', addDefault)
    
    // 4. Test a simple insert
    const testInsert = await client
      .from('users')
      .insert({
        email: '<EMAIL>',
        name: 'Schema Test User',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()
    
    if (testInsert.error) {
      return NextResponse.json({
        success: false,
        error: 'Test insert failed: ' + testInsert.error.message,
        details: testInsert.error
      }, { status: 500 })
    }
    
    return NextResponse.json({
      success: true,
      message: 'Database schema fixed successfully',
      testUser: testInsert.data
    })
    
  } catch (error) {
    console.error('Schema fix failed:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    }, { status: 500 })
  }
}