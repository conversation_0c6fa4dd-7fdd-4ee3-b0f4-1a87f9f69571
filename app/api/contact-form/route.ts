import { NextRequest, NextResponse } from 'next/server'
import { createContactConfirmationEmail, createAdminNotificationEmail } from '@/lib/email-templates'
import nodemailer from 'nodemailer'
import path from 'path'

interface ContactFormData {
  contactData: {
    first_name: string
    last_name: string
    email: string
    zip_code: string
    phone?: string | null
    country?: string | null
    children_ages?: string | null
    subject: string
    message: string
  }
}

// Rate limiting - simple in-memory store (use Redis in production)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>()

function checkRateLimit(clientIP: string): boolean {
  const now = Date.now()
  const windowMs = 60 * 1000 // 1 minute
  const maxRequests = 5 // 5 submissions per minute

  const clientData = rateLimitMap.get(clientIP)
  
  if (!clientData || now > clientData.resetTime) {
    rateLimitMap.set(clientIP, { count: 1, resetTime: now + windowMs })
    return true
  }
  
  if (clientData.count >= maxRequests) {
    return false
  }
  
  clientData.count++
  return true
}

function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

function validateSubject(subject: string): boolean {
  const validSubjects = [
    'ai-assistant',
    'demo-request', 
    'early-access',
    'healthcare-provider',
    'technical-support',
    'privacy-security',
    'billing',
    'media',
    'careers',
    'other'
  ]
  return validSubjects.includes(subject)
}

export async function POST(request: NextRequest) {
  try {
    // Get client IP for rate limiting
    const clientIP = request.headers.get('x-forwarded-for')?.split(',')[0] || 
      request.headers.get('x-real-ip') || 
      'unknown'

    // Check rate limit
    if (!checkRateLimit(clientIP)) {
      return NextResponse.json(
        { error: 'Rate limit exceeded. Please wait before submitting again.' },
        { status: 429 }
      )
    }

    const body = await request.json()
    const {contactData }: ContactFormData = body

    // Validation
    if (!contactData.first_name || typeof contactData.first_name !== 'string') {
      return NextResponse.json(
        { error: 'First name is required' },
        { status: 400 }
      )
    }

    if (!contactData.last_name || typeof contactData.last_name !== 'string') {
      return NextResponse.json(
        { error: 'Last name is required' },
        { status: 400 }
      )
    }

    if (!contactData.email || typeof contactData.email !== 'string') {
      return NextResponse.json(
        { error: 'Email address is required' },
        { status: 400 }
      )
    }

    if (!validateEmail(contactData.email)) {
      return NextResponse.json(
        { error: 'Please provide a valid email address' },
        { status: 400 }
      )
    }

    if (!contactData.zip_code || typeof contactData.zip_code !== 'string') {
      return NextResponse.json(
        { error: 'Zip code is required' },
        { status: 400 }
      )
    }

    if (!contactData.subject || typeof contactData.subject !== 'string') {
      return NextResponse.json(
        { error: 'Subject is required' },
        { status: 400 }
      )
    }

    if (!validateSubject(contactData.subject)) {
      return NextResponse.json(
        { error: 'Invalid subject selection' },
        { status: 400 }
      )
    }

    if (!contactData.message || typeof contactData.message !== 'string') {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      )
    }

    console.log('📝 Contact form submission:', { 
      email: contactData.email, 
      subject: contactData.subject,
      clientIP 
    })

    // Get Supabase project details from environment
    const supabaseProjectId = process.env.SUPABASE_PROJECT_ID || 'njhibesggyagmezbergr'
    const supabaseApiKey = process.env.SUPABASE_SERVICE_ROLE_KEY

    if (!supabaseApiKey) {
      console.error('❌ Supabase service role key not configured')
      return NextResponse.json(
        { error: 'Contact form service not configured' },
        { status: 500 }
      )
    }

    // Insert into Supabase database
    const supabaseUrl = `https://${supabaseProjectId}.supabase.co/rest/v1/contact_submissions`
    
    console.log('💾 Inserting contact submission into database...')
    
    const response = await fetch(supabaseUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${supabaseApiKey}`,
        'apikey': supabaseApiKey,
        'Prefer': 'return=minimal'
      },
      body: JSON.stringify({
        first_name: contactData.first_name.trim(),
        last_name: contactData.last_name.trim(),
        email: contactData.email.toLowerCase().trim(),
        zip_code: contactData.zip_code.trim(),
        phone: contactData.phone?.trim() || null,
        country: contactData.country?.trim() || null,
        children_ages: contactData.children_ages?.trim() || null,
        subject: contactData.subject,
        message: contactData.message.trim()
      })
    })

    if (!response.ok) {
      const errorData = await response.text()
      console.error('❌ Supabase insert failed:', {
        status: response.status,
        statusText: response.statusText,
        error: errorData
      })

      return NextResponse.json(
        { error: 'Failed to submit contact form. Please try again.' },
        { status: 500 }
      )
    }

    console.log('✅ Contact form submission successful:', contactData.email)

    // Send confirmation and notification emails
    try {
      console.log('📧 Sending contact form emails...')

      // Get SMTP credentials
      const smtpHost = process.env.SMTP_HOST
      const smtpPort = parseInt(process.env.SMTP_PORT || '465')
      const smtpUser = process.env.SMTP_USER
      const smtpPass = process.env.SMTP_PASS
      const smtpSecure = process.env.SMTP_SECURE === 'true'
      const fromEmail = process.env.FROM_EMAIL || smtpUser
      const fromName = process.env.FROM_NAME || 'Our Kidz'

      if (smtpUser && smtpPass && smtpHost) {
        // Create transporter
        const transporter = nodemailer.createTransport({
          host: smtpHost,
          port: smtpPort,
          secure: smtpSecure,
          auth: {
            user: smtpUser,
            pass: smtpPass,
          },
        })

        // Prepare logo attachment
        const logoAttachment = {
          filename: 'ourkidz-logo.png',
          path: path.join(process.cwd(), 'public', 'OKdarkTsp.png'),
          cid: 'ourkidz-logo'
        }

        // Send confirmation email to user
        const confirmationEmail = createContactConfirmationEmail(
          `${contactData.first_name} ${contactData.last_name}`,
          contactData.message
        )

        await transporter.sendMail({
          from: `"${fromName}" <${fromEmail}>`,
          to: contactData.email,
          subject: confirmationEmail.subject,
          text: confirmationEmail.text,
          html: confirmationEmail.html,
          attachments: [logoAttachment]
        })

        console.log('✅ Confirmation email sent to:', contactData.email)

        // Send notification email to admin
        const adminEmail = process.env.ADMIN_EMAIL || fromEmail
        const adminNotificationEmail = createAdminNotificationEmail({
          name: `${contactData.first_name} ${contactData.last_name}`,
          email: contactData.email,
          phone: contactData.phone || '',
          message: contactData.message
        })

        await transporter.sendMail({
          from: `"${fromName}" <${fromEmail}>`,
          to: adminEmail,
          subject: adminNotificationEmail.subject,
          text: adminNotificationEmail.text,
          html: adminNotificationEmail.html,
          attachments: [logoAttachment]
        })

        console.log('✅ Admin notification email sent to:', adminEmail)
      } else {
        console.log('⚠️ SMTP not configured, skipping email sending')
      }
    } catch (emailError) {
      console.error('❌ Failed to send contact form emails:', emailError)
      // Don't fail the contact form submission if email fails
    }

    return NextResponse.json({
      success: true,
      message: 'Contact form submitted successfully! We\'ll get back to you within 24 hours.'
    })

  } catch (error) {
    console.error('❌ Contact form submission error:', error)
    console.error('❌ Error details:', error instanceof Error ? error.message : String(error))
    console.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack trace')
    return NextResponse.json(
      { 
        error: 'Internal server error', 
        details: error instanceof Error ? error.message : String(error) 
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Contact Form API Endpoint',
    description: 'POST to this endpoint to submit contact form data',
    usage: {
      method: 'POST',
      body: {
        first_name: 'John (required)',
        last_name: 'Doe (required)',
        email: '<EMAIL> (required)',
        zip_code: '12345 (required)',
        phone: '************ (optional)',
        country: 'US (optional)',
        children_ages: '3, 7, 12 (optional)',
        subject: 'ai-assistant (required - see valid options)',
        message: 'Your message here (required)'
      }
    },
    validSubjects: [
      'ai-assistant',
      'demo-request', 
      'early-access',
      'healthcare-provider',
      'technical-support',
      'privacy-security',
      'billing',
      'media',
      'careers',
      'other'
    ],
    features: [
      'Form validation',
      'Rate limiting (5 submissions per minute)',
      'Supabase database integration',
      'Automatic timestamp tracking',
      'Email validation'
    ]
  })
}
