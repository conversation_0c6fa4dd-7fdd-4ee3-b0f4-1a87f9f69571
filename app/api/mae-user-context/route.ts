import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@supabase/supabase-js'
import { currentUser } from '@clerk/nextjs/server'

// Helper functions for processing session history
function extractTopicFromHistory(history: any[]): string {
  if (!history || history.length === 0) return 'No conversation yet'

  // Look for the first user message that's not a greeting
  const userMessages = history.filter(msg =>
    msg.role === 'user' &&
    msg.content &&
    !msg.content.toLowerCase().includes('hello') &&
    !msg.content.toLowerCase().includes('hi') &&
    msg.content.length > 10
  )

  if (userMessages.length > 0) {
    const firstMessage = userMessages[0].content
    return firstMessage.length > 50 ? firstMessage.substring(0, 50) + '...' : firstMessage
  }

  return 'General conversation'
}

function extractKeyPoints(history: any[]): string[] {
  if (!history || history.length === 0) return []

  const keyPoints: string[] = []

  // Look for <PERSON>'s responses that contain medical advice or key information
  const maeResponses = history.filter(msg =>
    msg.role === 'assistant' &&
    msg.content &&
    msg.content.length > 20
  )

  maeResponses.forEach(response => {
    const content = response.content.toLowerCase()

    // Extract key medical topics
    if (content.includes('fever')) keyPoints.push('Fever management')
    if (content.includes('cough')) keyPoints.push('Cough treatment')
    if (content.includes('allergy') || content.includes('allergies')) keyPoints.push('Allergy information')
    if (content.includes('medication') || content.includes('medicine')) keyPoints.push('Medication guidance')
    if (content.includes('doctor') || content.includes('pediatrician')) keyPoints.push('Healthcare provider referral')
    if (content.includes('emergency') || content.includes('urgent')) keyPoints.push('Emergency guidance')
  })

  return [...new Set(keyPoints)].slice(0, 3) // Remove duplicates and limit to 3
}

export async function POST(request: NextRequest) {
  try {
    console.log('🔍 Mae user context API called')
    
    // Get authenticated user from Clerk
    const user = await currentUser()
    
    if (!user) {
      console.log('❌ No authenticated user found via Clerk')
      return NextResponse.json({ 
        success: true, 
        user_authenticated: false,
        user_in_database: false,
        message: 'User not authenticated. Please sign in first.'
      })
    }
    
    // Check if Supabase is configured
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY
    
    if (!supabaseUrl || !supabaseKey) {
      console.error('❌ Supabase configuration missing:', { 
        hasUrl: !!supabaseUrl, 
        hasKey: !!supabaseKey 
      })
      return NextResponse.json({ 
        success: false, 
        error: 'Database configuration missing',
        user_authenticated: true,
        user_in_database: false
      }, { status: 500 })
    }
    
    const supabase = createClient(supabaseUrl, supabaseKey)

    console.log('🔍 Mae requesting user context for Clerk user:', { 
      clerkId: user.id, 
      email: user.emailAddresses[0]?.emailAddress 
    })

    let userData = null

    // Try multiple lookup strategies to find the user
    console.log('🔍 Attempting user lookup with multiple strategies...')

    // Strategy 1: Look up by clerk_id field
    const { data: clerkUserData, error: clerkError } = await supabase
      .from('users')
      .select('*')
      .eq('clerk_id', user.id)
      .single()

    if (!clerkError && clerkUserData) {
      userData = clerkUserData
      console.log('✅ Found user by clerk_id:', userData.id)
    } else {
      console.log('🔍 No user found by clerk_id, trying auth_user_id...')

      // Strategy 2: Look up by auth_user_id field (legacy)
      const { data: authUserData, error: authError } = await supabase
        .from('users')
        .select('*')
        .eq('auth_user_id', user.id)
        .single()

      if (!authError && authUserData) {
        userData = authUserData
        console.log('✅ Found user by auth_user_id:', userData.id)

        // Migrate to clerk_id field for consistency
        const { data: migratedUser, error: migrateError } = await supabase
          .from('users')
          .update({
            clerk_id: user.id,
            updated_at: new Date().toISOString()
          })
          .eq('id', authUserData.id)
          .select('*')
          .single()

        if (!migrateError && migratedUser) {
          userData = migratedUser
          console.log('✅ Migrated user to use clerk_id field')
        }
      } else {
        console.log('🔍 No user found by auth_user_id, trying email lookup...')

        // Strategy 3: Look up by email and link to Clerk ID
        const userEmail = user.emailAddresses[0]?.emailAddress
        if (userEmail) {
          const { data: emailUserData, error: emailError } = await supabase
            .from('users')
            .select('*')
            .ilike('email', userEmail.toLowerCase())
            .single()

          if (!emailError && emailUserData) {
            console.log('✅ Found user by email, linking to Clerk ID:', user.id)

            // Update existing user record with Clerk ID
            const { data: updatedUser, error: updateError } = await supabase
              .from('users')
              .update({
                clerk_id: user.id,
                updated_at: new Date().toISOString()
              })
              .eq('id', emailUserData.id)
              .select('*')
              .single()

            if (!updateError && updatedUser) {
              userData = updatedUser
              console.log('✅ Successfully linked Clerk ID to existing user:', userData.id)
            } else {
              console.error('❌ Failed to update user with clerk_id:', updateError)
            }
          }
        }
      }
    }

    if (!userData) {
      console.log('👤 No user found in database for Clerk ID:', user.id)
      console.log('🔧 Auto-creating user record for new user...')

      // Auto-create user record for new users
      const userEmail = user.emailAddresses[0]?.emailAddress
      const userName = user.firstName && user.lastName
        ? `${user.firstName} ${user.lastName}`
        : user.firstName || user.lastName || userEmail?.split('@')[0] || 'User'

      if (userEmail) {
        const { data: newUser, error: createError } = await supabase
          .from('users')
          .insert({
            clerk_id: user.id,
            email: userEmail,
            name: userName,
            role: 'parent',
            onboarding_completed: false
          })
          .select('*')
          .single()

        if (!createError && newUser) {
          console.log('✅ Auto-created user record:', newUser.id)
          userData = newUser
        } else {
          console.error('❌ Failed to auto-create user:', createError)
          return NextResponse.json({
            success: true,
            user_authenticated: true,
            user_in_database: false,
            clerk_id: user.id,
            email: userEmail,
            message: 'User authenticated with Clerk but could not be created in database.',
            action_required: 'manual_user_creation',
            error: createError?.message
          })
        }
      } else {
        return NextResponse.json({
          success: true,
          user_authenticated: true,
          user_in_database: false,
          clerk_id: user.id,
          message: 'User authenticated with Clerk but no email found.',
          action_required: 'email_required'
        })
      }
    }

    // Get family members for this user
    const { data: familyMembers, error: familyError } = await supabase
      .from('family_members')
      .select('*')
      .eq('user_id', userData.id)

    if (familyError) {
      console.error('❌ Error fetching family members:', familyError)
    }

    // Get recent audio chat sessions (enhanced with more details)
    const { data: recentSessions, error: sessionsError } = await supabase
      .from('audio_chat_sessions')
      .select('session_id, history, created_at, updated_at, metadata')
      .eq('user_id', userData.id)
      .order('created_at', { ascending: false })
      .limit(10)

    if (sessionsError) {
      console.error('❌ Error fetching recent sessions:', sessionsError)
    }

    // Get care log entries for comprehensive health tracking
    const { data: careLogEntries, error: careLogError } = await supabase
      .from('care_log_entries')
      .select('id, child, note, image_url, weather, created_at, tags, content_type')
      .eq('user_id', userData.id)
      .order('created_at', { ascending: false })
      .limit(20)

    if (careLogError) {
      console.error('❌ Error fetching care log entries:', careLogError)
    }

    // Get user's library content for personalization insights
    const { data: libraryContent, error: libraryError } = await supabase
      .from('my_library')
      .select('id, content_type, title, content, image_url, metadata, created_at, updated_at')
      .eq('user_id', userData.id)
      .order('created_at', { ascending: false })
      .limit(15)

    if (libraryError) {
      console.error('❌ Error fetching library content:', libraryError)
    }

    // Get pre-visit data for medical context
    const { data: preVisitData, error: preVisitError } = await supabase
      .from('pre_visit_data')
      .select('*')
      .eq('user_id', userData.id)
      .order('created_at', { ascending: false })
      .limit(5)

    if (preVisitError) {
      console.error('❌ Error fetching pre-visit data:', preVisitError)
    }

    // Get user session data for usage patterns
    const { data: userSessions, error: userSessionsError } = await supabase
      .from('user_sessions')
      .select('id, session_id, started_at, ended_at, duration_seconds, device_type, browser, os, country, is_mobile')
      .eq('user_id', userData.id)
      .order('started_at', { ascending: false })
      .limit(10)

    if (userSessionsError) {
      console.error('❌ Error fetching user sessions:', userSessionsError)
    }

    // Get user profile if exists
    const { data: userProfile, error: profileError } = await supabase
      .from('user_profiles')
      .select('*')
      .eq('user_id', userData.id)
      .single()

    if (profileError && profileError.code !== 'PGRST116') {
      console.error('❌ Error fetching user profile:', profileError)
    }

    // Build comprehensive user context with enhanced data
    const userContext = {
      success: true,
      user_authenticated: true,
      user_in_database: true,
      user: {
        id: userData.id,
        auth_user_id: userData.auth_user_id,
        email: userData.email,
        name: userData.name,
        role: userData.role || 'parent',
        phone: userData.phone,
        zip: userData.zip,
        onboarding_completed: userData.onboarding_completed,
        created_at: userData.created_at
      },
      family_members: familyMembers || [],
      family_summary: {
        total_children: familyMembers?.filter(m => m.relationship === 'child').length || 0,
        age_ranges: familyMembers?.map(m => {
          const birthDate = new Date(m.date_of_birth)
          const age = Math.floor((Date.now() - birthDate.getTime()) / (365.25 * 24 * 60 * 60 * 1000))
          return `${m.name}: ${age} years old`
        }) || [],
        primary_concerns: familyMembers?.flatMap(m => m.medical_conditions || []) || [],
        recent_conversations: recentSessions?.map(s => ({
          session_id: s.session_id,
          last_topic: extractTopicFromHistory(s.history),
          date: s.created_at,
          key_points: extractKeyPoints(s.history)
        })) || []
      },
      // Enhanced context data
      audio_chat_sessions: recentSessions || [],
      care_log_entries: careLogEntries || [],
      my_library: libraryContent || [],
      pre_visit_data: preVisitData || [],
      user_sessions: userSessions || [],
      // Enhanced analytics
      context_analytics: {
        total_audio_sessions: recentSessions?.length || 0,
        total_care_log_entries: careLogEntries?.length || 0,
        total_library_items: libraryContent?.length || 0,
        total_pre_visit_records: preVisitData?.length || 0,
        recent_session_count: userSessions?.length || 0,
        last_session_date: userSessions?.[0]?.started_at || null,
        most_used_device: getMostUsedDevice(userSessions || []),
        care_log_children: [...new Set(careLogEntries?.map(entry => entry.child) || [])],
        library_content_types: [...new Set(libraryContent?.map(item => item.content_type) || [])],
        recent_care_tags: getRecentCareTags(careLogEntries || [])
      },
      user_profile: userProfile || null,
      recent_sessions_count: recentSessions?.length || 0
    }

    console.log('✅ Mae user context retrieved:', {
      userId: userData.id,
      email: userData.email,
      familyMembersCount: familyMembers?.length || 0,
      recentSessionsCount: recentSessions?.length || 0,
      careLogEntriesCount: careLogEntries?.length || 0,
      libraryItemsCount: libraryContent?.length || 0,
      preVisitRecordsCount: preVisitData?.length || 0,
      userSessionsCount: userSessions?.length || 0
    })

    return NextResponse.json(userContext)

  } catch (error) {
    console.error('❌ Error in mae-user-context API:', error)
    return NextResponse.json({
      success: false,
      error: 'Internal server error'
    }, { status: 500 })
  }
}

// Helper function to get most used device type
function getMostUsedDevice(sessions: any[]): string {
  if (!sessions || sessions.length === 0) return 'unknown'

  const deviceCounts = sessions.reduce((acc, session) => {
    const device = session.device_type || 'unknown'
    acc[device] = (acc[device] || 0) + 1
    return acc
  }, {})

  return Object.entries(deviceCounts).reduce((a, b) =>
    deviceCounts[a[0]] > deviceCounts[b[0]] ? a : b
  )[0] as string
}

// Helper function to get recent care tags
function getRecentCareTags(careEntries: any[]): string[] {
  if (!careEntries || careEntries.length === 0) return []

  const allTags = careEntries
    .filter(entry => entry.tags && Array.isArray(entry.tags))
    .flatMap(entry => entry.tags)
    .filter(tag => tag && typeof tag === 'string')

  // Get unique tags and return most recent/frequent ones
  const uniqueTags = [...new Set(allTags)]
  return uniqueTags.slice(0, 10) // Return top 10 most recent tags
}