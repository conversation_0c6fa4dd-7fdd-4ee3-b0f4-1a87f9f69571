import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Get query parameters
    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '50')
    const offset = parseInt(searchParams.get('offset') || '0')
    const email = searchParams.get('email')
    const startDate = searchParams.get('start_date')
    const endDate = searchParams.get('end_date')

    // Get Supabase project details from environment
    const supabaseProjectId = process.env.SUPABASE_PROJECT_ID || 'njhibesggyagmezbergr'
    const supabaseApiKey = process.env.SUPABASE_SERVICE_ROLE_KEY

    if (!supabaseApiKey) {
      console.error('❌ Supabase service role key not configured')
      return NextResponse.json(
        { error: 'Database service not configured' },
        { status: 500 }
      )
    }

    console.log('📊 Querying email interactions:', {
      email,
      startDate,
      endDate,
      limit,
      offset
    })

    // Build Supabase REST API URL with filters
    let supabaseUrl = `https://${supabaseProjectId}.supabase.co/rest/v1/audio_chat_sessions`
    const urlParams = new URLSearchParams()

    // Filter for email interactions only
    urlParams.append('metadata->>email_sent', 'eq.true')

    if (email) {
      urlParams.append('user_email', `ilike.%${email}%`)
    }

    if (startDate) {
      urlParams.append('created_at', `gte.${startDate}`)
    }

    if (endDate) {
      urlParams.append('created_at', `lte.${endDate}`)
    }

    // Add ordering and pagination
    urlParams.append('order', 'created_at.desc')
    urlParams.append('limit', limit.toString())
    urlParams.append('offset', offset.toString())

    // Select specific columns
    urlParams.append('select', 'id,session_id,user_email,history,metadata,created_at,updated_at')

    supabaseUrl += '?' + urlParams.toString()

    // Execute query
    const response = await fetch(supabaseUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${supabaseApiKey}`,
        'apikey': supabaseApiKey
      }
    })

    if (!response.ok) {
      const errorData = await response.text()
      console.error('❌ Supabase query failed:', {
        status: response.status,
        error: errorData
      })
      return NextResponse.json(
        { error: 'Failed to fetch email interactions' },
        { status: 500 }
      )
    }

    const data = await response.json()

    // Transform the data for better readability
    const interactions = data.map((row: any) => ({
      id: row.id,
      session_id: row.session_id,
      user_email: row.user_email,
      question: row.history?.[0]?.content || 'No question recorded',
      response: row.history?.[1]?.content || 'No response recorded',
      sources: row.history?.[1]?.sources || [],
      email_details: {
        message_id: row.metadata?.email_message_id,
        subject: row.metadata?.email_subject,
        sent_at: row.metadata?.email_timestamp,
        sources_count: row.metadata?.sources_count || 0
      },
      created_at: row.created_at,
      updated_at: row.updated_at
    }))

    // Get total count for pagination using HEAD request
    let countUrl = `https://${supabaseProjectId}.supabase.co/rest/v1/audio_chat_sessions`
    const countParams = new URLSearchParams()

    // Same filters as main query
    countParams.append('metadata->>email_sent', 'eq.true')

    if (email) {
      countParams.append('user_email', `ilike.%${email}%`)
    }

    if (startDate) {
      countParams.append('created_at', `gte.${startDate}`)
    }

    if (endDate) {
      countParams.append('created_at', `lte.${endDate}`)
    }

    countUrl += '?' + countParams.toString()

    const countResponse = await fetch(countUrl, {
      method: 'HEAD',
      headers: {
        'Authorization': `Bearer ${supabaseApiKey}`,
        'apikey': supabaseApiKey,
        'Prefer': 'count=exact'
      }
    })

    let totalCount = 0
    if (countResponse.ok) {
      const contentRange = countResponse.headers.get('content-range')
      if (contentRange) {
        const match = contentRange.match(/\/(\d+)$/)
        totalCount = match ? parseInt(match[1]) : 0
      }
    }

    return NextResponse.json({
      success: true,
      data: interactions,
      pagination: {
        total: totalCount,
        limit,
        offset,
        has_more: offset + limit < totalCount
      },
      filters: {
        email,
        start_date: startDate,
        end_date: endDate
      }
    })

  } catch (error) {
    console.error('❌ Error fetching email interactions:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST() {
  return NextResponse.json({
    message: 'Email Interactions API Endpoint',
    description: 'GET to retrieve Mae\'s email interaction data',
    usage: {
      method: 'GET',
      query_parameters: {
        limit: 'Number of records to return (default: 50, max: 100)',
        offset: 'Number of records to skip for pagination (default: 0)',
        email: 'Filter by user email (partial match)',
        start_date: 'Filter by start date (ISO format: 2024-01-01)',
        end_date: 'Filter by end date (ISO format: 2024-12-31)'
      }
    },
    examples: [
      'GET /api/email-interactions',
      'GET /api/email-interactions?limit=10&offset=0',
      'GET /api/email-interactions?email=<EMAIL>',
      'GET /api/email-interactions?start_date=2024-01-01&end_date=2024-12-31'
    ],
    data_structure: {
      id: 'Database record ID',
      session_id: 'Unique session identifier',
      user_email: 'Email address where summary was sent',
      question: 'User\'s original question to Mae',
      response: 'Mae\'s AI response',
      sources: 'Array of source links used in response',
      email_details: {
        message_id: 'Email message ID from SMTP',
        subject: 'Email subject line',
        sent_at: 'Timestamp when email was sent',
        sources_count: 'Number of sources included'
      },
      created_at: 'When the interaction was recorded',
      updated_at: 'When the record was last updated'
    }
  })
}
