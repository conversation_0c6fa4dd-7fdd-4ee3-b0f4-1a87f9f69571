import { NextResponse } from 'next/server'

export async function GET() {
  // This endpoint helps debug environment variable issues in production
  // Only show boolean values for security
  
  const envCheck = {
    SUPABASE_PROJECT_ID: !!process.env.SUPABASE_PROJECT_ID,
    SUPABASE_SERVICE_ROLE_KEY: !!process.env.SUPABASE_SERVICE_ROLE_KEY,
    SMTP_HOST: !!process.env.SMTP_HOST,
    SMTP_PORT: !!process.env.SMTP_PORT,
    SMTP_USER: !!process.env.SMTP_USER,
    SMTP_PASS: !!process.env.SMTP_PASS,
    SMTP_SECURE: !!process.env.SMTP_SECURE,
    FROM_EMAIL: !!process.env.FROM_EMAIL,
    FROM_NAME: !!process.env.FROM_NAME,
    ADMIN_EMAIL: !!process.env.ADMIN_EMAIL,
    GEMINI_API_KEY: !!process.env.GEMINI_API_KEY,
    GOOGLE_MAPS_API_KEY: !!process.env.GOOGLE_MAPS_API_KEY,
    WEBHOOK_URL: !!process.env.WEBHOOK_URL,
    NODE_ENV: process.env.NODE_ENV
  }

  return NextResponse.json({
    message: 'Environment Variables Check',
    description: 'Boolean values indicate whether environment variables are set',
    env: envCheck,
    timestamp: new Date().toISOString()
  })
}