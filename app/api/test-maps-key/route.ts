import { NextRequest } from 'next/server';

export async function GET(req: NextRequest) {
  try {
    const apiKey = process.env.GOOGLE_MAPS_API_KEY;
    
    if (!apiKey) {
      return Response.json({ 
        error: 'No API key found',
        suggestion: 'Check that GOOGLE_MAPS_API_KEY is set in .env.local'
      }, { status: 500 });
    }

    console.log('🔍 Testing Google Maps API key...');
    console.log('   - Key length:', apiKey.length);
    console.log('   - Key prefix:', apiKey.substring(0, 15) + '...');
    
    // Test simple geocoding request
    const testLocation = 'New York, NY';
    const geocodeUrl = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodeURIComponent(testLocation)}&key=${apiKey}`;
    
    console.log('🌍 Testing geocoding API...');
    const geocodeResponse = await fetch(geocodeUrl);
    const geocodeData = await geocodeResponse.json();
    
    console.log('📍 Geocoding response:', geocodeData);
    
    // Test Places API
    if (geocodeData.status === 'OK' && geocodeData.results.length > 0) {
      const { lat, lng } = geocodeData.results[0].geometry.location;
      const placesUrl = `https://maps.googleapis.com/maps/api/place/nearbysearch/json?location=${lat},${lng}&radius=1000&keyword=hospital&key=${apiKey}`;
      
      console.log('🏥 Testing Places API...');
      const placesResponse = await fetch(placesUrl);
      const placesData = await placesResponse.json();
      
      console.log('🏥 Places response:', placesData);
      
      return Response.json({
        success: true,
        tests: {
          geocoding: {
            status: geocodeData.status,
            resultsCount: geocodeData.results?.length || 0
          },
          places: {
            status: placesData.status,
            resultsCount: placesData.results?.length || 0
          }
        },
        keyInfo: {
          length: apiKey.length,
          prefix: apiKey.substring(0, 15) + '...'
        }
      });
    } else {
      return Response.json({
        success: false,
        geocodingError: {
          status: geocodeData.status,
          error_message: geocodeData.error_message
        },
        keyInfo: {
          length: apiKey.length,
          prefix: apiKey.substring(0, 15) + '...'
        },
        suggestions: [
          'Enable Geocoding API in Google Cloud Console',
          'Enable Places API in Google Cloud Console', 
          'Check API key restrictions',
          'Ensure billing is enabled on the Google Cloud project'
        ]
      }, { status: 400 });
    }
    
  } catch (error) {
    console.error('API key test error:', error);
    return Response.json({
      error: 'Failed to test API key',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}