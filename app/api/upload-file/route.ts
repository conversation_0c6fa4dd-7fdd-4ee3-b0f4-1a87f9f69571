import { NextRequest, NextResponse } from 'next/server'
import { GoogleAIFileManager } from '@google/generative-ai/server'

export async function POST(request: NextRequest) {
  try {
    const apiKey = process.env.GEMINI_API_KEY
    if (!apiKey) {
      return NextResponse.json({ error: 'Gemini API key not configured' }, { status: 500 })
    }

    const fileManager = new GoogleAIFileManager(apiKey)
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 })
    }

    // Convert File to Buffer
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    // Upload file to Gemini File API
    const uploadResponse = await fileManager.uploadFile(buffer, {
      mimeType: file.type,
      displayName: file.name,
    })

    console.log(`📁 File uploaded successfully: ${uploadResponse.file.displayName}`)
    console.log(`🔗 File URI: ${uploadResponse.file.uri}`)

    return NextResponse.json({
      success: true,
      file: {
        uri: uploadResponse.file.uri,
        name: uploadResponse.file.displayName,
        mimeType: uploadResponse.file.mimeType,
        sizeBytes: uploadResponse.file.sizeBytes,
        state: uploadResponse.file.state
      }
    })

  } catch (error) {
    console.error('❌ File upload error:', error)
    return NextResponse.json({ 
      error: 'Failed to upload file',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}