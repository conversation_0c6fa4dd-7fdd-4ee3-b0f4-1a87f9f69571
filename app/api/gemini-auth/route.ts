import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    // Get the API key from server-side environment variables
    const apiKey = process.env.GEMINI_API_KEY
    
    if (!apiKey) {
      return NextResponse.json(
        { error: 'Gemini API key not configured' },
        { status: 500 }
      )
    }

    // Return a secure token or session identifier instead of the raw API key
    // For now, we'll create a simple endpoint that validates the key exists
    return NextResponse.json({
      success: true,
      hasApiKey: true,
      // Do NOT return the actual API key
    })

  } catch (error) {
    console.error('Error in gemini-auth:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}