import { auth, currentUser } from '@clerk/nextjs/server'
import { NextResponse } from 'next/server'

export async function GET() {
  try {
    const { userId } = auth()
    const user = await currentUser()

    return NextResponse.json({
      auth_userId: userId,
      currentUser_id: user?.id || null,
      currentUser_email: user?.emailAddresses[0]?.emailAddress || null,
      currentUser_name: user?.fullName || null,
      success: true
    })
  } catch (error) {
    return NextResponse.json({
      error: 'Auth check failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      success: false
    }, { status: 500 })
  }
}