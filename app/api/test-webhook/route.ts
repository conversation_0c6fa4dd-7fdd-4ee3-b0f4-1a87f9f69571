import { NextRequest, NextResponse } from 'next/server'
import axios from 'axios'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    console.log('🧪 Testing webhook with payload:', body)
    
    // Get webhook URL from environment
    const webhookUrl = process.env.N8N_WEBHOOK_URL
    
    if (!webhookUrl) {
      return NextResponse.json(
        { 
          success: false,
          error: 'N8N_WEBHOOK_URL not configured in environment variables' 
        },
        { status: 500 }
      )
    }

    // Create test payload
    const testPayload = {
      email: body.email || '<EMAIL>',
      subject: body.subject || 'Webhook Test from Our Kidz API',
      date: new Date().toISOString(),
      success: true,
      messageId: `test-${Date.now()}`,
      error: null,
      sourcesCount: body.sources?.length || 2,
      sources: body.sources || [
        {
          title: 'Test Source 1',
          url: 'https://example.com/test-1'
        },
        {
          title: 'Test Source 2', 
          url: 'https://example.com/test-2'
        }
      ],
      platform: 'Our Kidz',
      emailType: 'api_test',
      testMode: true
    }

    console.log('📡 Sending test webhook to:', webhookUrl)
    console.log('📦 Payload:', JSON.stringify(testPayload, null, 2))

    // Send webhook
    const response = await axios.post(webhookUrl, testPayload, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10 second timeout
    })

    console.log('✅ Webhook test successful:', response.status)

    return NextResponse.json({
      success: true,
      message: 'Webhook test completed successfully',
      webhookUrl: webhookUrl,
      responseStatus: response.status,
      responseData: response.data,
      testPayload: testPayload
    })

  } catch (error) {
    console.error('❌ Webhook test failed:', error)
    
    let errorDetails = 'Unknown error'
    let responseStatus = null
    let responseData = null
    
    if (error instanceof Error && 'response' in error) {
      errorDetails = (error as any).response.data || error.message
      responseStatus = (error as any).response.status
      responseData = (error as any).response.data
    } else if (error instanceof Error && 'request' in error) {
      errorDetails = 'No response received from webhook URL'
    } else {
      errorDetails = error instanceof Error ? error.message : 'Unknown error'
    }
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Webhook test failed',
        details: errorDetails,
        webhookUrl: process.env.N8N_WEBHOOK_URL,
        responseStatus: responseStatus,
        responseData: responseData
      },
      { status: 500 }
    )
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Webhook Test Endpoint',
    description: 'POST to this endpoint to test the N8N webhook integration',
    webhookUrl: process.env.N8N_WEBHOOK_URL || 'NOT_CONFIGURED',
    usage: {
      method: 'POST',
      body: {
        email: '<EMAIL> (optional)',
        subject: 'Test Subject (optional)',
        sources: '[{title: "Source", url: "URL"}] (optional)'
      }
    },
    example: `
curl -X POST ${process.env.NEXT_PUBLIC_VERCEL_URL || 'http://localhost:3000'}/api/test-webhook \\
  -H "Content-Type: application/json" \\
  -d '{
    "email": "<EMAIL>",
    "subject": "Test Webhook",
    "sources": [
      {"title": "Test Source", "url": "https://example.com"}
    ]
  }'
    `
  })
}
