"use client"

import React, { useEffect } from "react"
import { useR<PERSON><PERSON>, useSearchParams } from "next/navigation"
import { SignIn, SignUp, useAuth } from "@clerk/nextjs"
import Image from "next/image"
import { motion } from "framer-motion"
import { Heart, Shield, Users, MessageCircle, Sparkles, ChevronRight, Star } from "lucide-react"
import { Card } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { ThemeToggle } from "@/components/theme-toggle"
import Link from "next/link"

interface AuthPageProps {
  params: { signIn?: string[] }
}

export default function AuthPage({ params }: AuthPageProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { isSignedIn } = useAuth()
  
  // Start with a consistent default to avoid hydration mismatch
  const [activeTab, setActiveTab] = React.useState('sign-in')

  // Determine which tab to show based on the route and URL parameters
  const getActiveTab = () => {
    // Debug logging to understand the URL structure
    const redirectUrl = searchParams.get('redirect_url')
    const searchParamsStr = searchParams.toString()

    console.log('🔍 Auth Tab Detection:', {
      params: params.signIn,
      redirectUrl,
      searchParamsStr,
      pathname: typeof window !== 'undefined' ? window.location.pathname : 'server'
    })

    // Check URL path segments first
    if (params.signIn && params.signIn[0] === 'sign-up') {
      console.log('✅ Sign-up detected from params.signIn')
      return 'sign-up'
    }

    // Check if redirect_url contains 'sign-up' - this is what Clerk adds
    if (redirectUrl && (redirectUrl.includes('/auth/sign-up') || redirectUrl.includes('sign-up'))) {
      console.log('✅ Sign-up detected from redirect_url')
      return 'sign-up'
    }

    // Check search params directly for any sign-up related parameter
    if (searchParamsStr.includes('sign-up') || searchParamsStr.includes('sign_up')) {
      console.log('✅ Sign-up detected from search params')
      return 'sign-up'
    }

    // Check if the current URL path includes 'sign-up' (client-side only)
    if (typeof window !== 'undefined' && window.location.pathname.includes('sign-up')) {
      console.log('✅ Sign-up detected from pathname')
      return 'sign-up'
    }

    console.log('🔐 Defaulting to sign-in')
    return 'sign-in'
  }
  useEffect(() => {
    // Set the correct tab after hydration to avoid mismatch
    const newTab = getActiveTab()
    setActiveTab(newTab)
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [params?.signIn?.[0], searchParams.toString()])

  useEffect(() => {
    if (isSignedIn) {
      router.push("/")
    }
  }, [isSignedIn, router])

  const features = [
    {
      icon: <Heart className="w-5 h-5" />,
      title: "AI Health Assistant",
      description: "24/7 pediatric guidance with Mae"
    },
    {
      icon: <Shield className="w-5 h-5" />,
      title: "Secure & Private",
      description: "Your family's data is protected"
    },
    {
      icon: <Users className="w-5 h-5" />,
      title: "Family Management",
      description: "Track health for all your children"
    },
    {
      icon: <MessageCircle className="w-5 h-5" />,
      title: "Voice-Guided Setup",
      description: "Easy onboarding with Mae's help"
    }
  ]

  const testimonials = [
    {
      name: "Sarah M.",
      role: "Mother of 2",
      content: "Mae has been a lifesaver for managing my kids' health concerns. It's like having a pediatric nurse on call 24/7!",
      image: "https://images.unsplash.com/photo-**********-94ddf0286df2?q=80&fit=crop&crop=faces&w=64&h=64"
    },
    {
      name: "James D.",
      role: "Father of 3",
      content: "The voice-guided setup made it so easy to get started. Mae knows all about my family now and provides personalized advice.",
      image: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&fit=crop&crop=faces&w=64&h=64"
    }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-primary/5">
      {/* Header */}
      <header className="absolute top-0 left-0 right-0 z-50 p-6">
        <div className="container mx-auto flex justify-between items-center">
          <Link href="/" className="flex items-center space-x-2 group">
            <Image
              src="/OKdarkTsp.png"
              alt="Our Kidz"
              width={40}
              height={40}
              className="transition-all duration-300 group-hover:rotate-[15deg] " // group-hover:drop-shadow-[0_0_10px_rgba(20,184,166,0.5)]
            />
            <span className="font-inter text-lg font-light">Our Kidz</span>
          </Link>
          <div className="flex items-center gap-4">
            <ThemeToggle />
            <Link href="/">
              <Button variant="ghost" size="sm">
                Back to Home
              </Button>
            </Link>
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-20 flex items-center justify-center min-h-screen">
        <div className="grid lg:grid-cols-2 gap-12 max-w-7xl w-full">
          
          {/* Left Side - Benefits & Features */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            className="flex flex-col justify-center space-y-8"
          >
            <div>
              <Badge className="mb-4 bg-primary/10 text-primary border-primary/20">
                <Sparkles className="w-3 h-3 mr-1" />
                AI-Powered Parenting
              </Badge>
              <h1 className="text-4xl lg:text-5xl font-bold mb-4 bg-gradient-to-r from-blue-600 via-blue-500 to-primary bg-clip-text text-transparent dark:from-blue-400 dark:via-blue-300 dark:to-primary">
                Welcome to Our Kidz
              </h1>
              <p className="text-xl text-muted-foreground">
                Your personal AI pediatric health assistant, available 24/7 to support your family's wellness journey.
              </p>
            </div>

            {/* Features Grid */}
            <div className="grid grid-cols-2 gap-4">
              {features.map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                  className="flex items-start space-x-3"
                >
                  <div className="w-10 h-10 rounded-lg bg-primary/10 flex items-center justify-center flex-shrink-0">
                    <div className="text-primary">{feature.icon}</div>
                  </div>
                  <div>
                    <h3 className="font-semibold text-sm">{feature.title}</h3>
                    <p className="text-xs text-muted-foreground">{feature.description}</p>
                  </div>
                </motion.div>
              ))}
            </div>

            {/* Testimonials */}
            <div className="space-y-4">
              <h3 className="text-sm font-semibold text-muted-foreground">Trusted by Parents</h3>
              {testimonials.map((testimonial, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.4, delay: 0.6 + index * 0.1 }}
                >
                  <Card className="p-4 border border-border/50 bg-card/50 backdrop-blur transition-all duration-300 hover:scale-[1.01] hover:border-primary/40 hover:shadow-[0_0_25px_rgba(20,184,166,0.30)]">
                    <div className="flex items-start space-x-1 mb-2">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="w-3 h-3 fill-primary text-primary" />
                      ))}
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">"{testimonial.content}"</p>
                    <div className="flex items-center space-x-2">
                         <Image
                           src={testimonial.image}
                           alt={`${testimonial.name} avatar`}
                           width={32}
                           height={32}
                           className="w-8 h-8 rounded-full object-cover"
                         />
                      <div>
                        <p className="text-sm font-semibold">{testimonial.name}</p>
                        <p className="text-xs text-muted-foreground">{testimonial.role}</p>
                      </div>
                    </div>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>

          {/* Right Side - Auth Forms */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
            className="flex items-center justify-center"
          >
            <Card className="max-w-xl p-7 border border-border/50 bg-card/95 backdrop-blur shadow-2xl transition-all duration-300 hover:scale-[1.02] hover:border-primary/40 hover:shadow-[0_0_40px_rgba(20,184,166,0.35)]">
              <div className="text-center mb-8">
                <h2 className="text-2xl font-bold mb-2">Get Started</h2>
                <p className="text-muted-foreground">
                  Create your account or sign in to continue
                </p>
              </div>

              <Tabs value={activeTab} onValueChange={(tab) => {
                setActiveTab(tab)
                // Update URL when tab changes - use simple paths without Clerk parameters
                const newPath = tab === 'sign-up' ? '/auth/sign-up' : '/auth'
                router.push(newPath, { scroll: false })
              }} className="w-full">
                <TabsList className="grid w-full grid-cols-2 mb-6">
                  <TabsTrigger value="sign-in">Sign In</TabsTrigger>
                  <TabsTrigger value="sign-up">Sign Up</TabsTrigger>
                </TabsList>

                <TabsContent value="sign-in">
                  <div className="clerk-wrapper mx-auto w-full max-w-md">
                    <SignIn
                      routing="path"
                      path="/auth"
                      appearance={{
                        variables: {
                          colorPrimary: "#14b8a6",
                          colorDanger: "#ef4444",
                          colorSuccess: "#10b981",
                          colorWarning: "#f59e0b",
                          colorBackground: "transparent",
                        },
                        elements: {
                          rootBox: "mx-auto",
                          card: "bg-card/90 backdrop-blur border border-border shadow-lg",
                          cardBox: "shadow-none",
                        }
                      }}
                      fallbackRedirectUrl="/"
                      signUpUrl="/auth/sign-up"
                    />
                  </div>
                </TabsContent>

                <TabsContent value="sign-up">
                  <div className="clerk-wrapper mx-auto w-full max-w-md">
                    <SignUp
                      routing="path"
                      path="/auth/sign-up"
                      appearance={{
                        variables: {
                          colorPrimary: "#14b8a6",
                          colorDanger: "#ef4444",
                          colorSuccess: "#10b981",
                          colorWarning: "#f59e0b",
                          colorBackground: "transparent",
                        },
                        elements: {
                          rootBox: "mx-auto",
                          card: "bg-card/90 backdrop-blur border border-border shadow-lg",
                          cardBox: "shadow-none",
                        }
                      }}
                      fallbackRedirectUrl="/"
                      signInUrl="/auth"
                    />
                  </div>
                </TabsContent>
              </Tabs>

              <div className="mt-6 text-center">
                <p className="text-xs text-muted-foreground">
                  By continuing, you agree to our{" "}
                  <Link href="/terms" className="text-primary hover:underline">
                    Terms of Service
                  </Link>{" "}
                  and{" "}
                  <Link href="/privacy" className="text-primary hover:underline">
                    Privacy Policy
                  </Link>
                </p>
              </div>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  )
}