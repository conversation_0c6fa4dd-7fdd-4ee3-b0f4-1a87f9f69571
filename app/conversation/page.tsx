"use client"

import React, { useState } from 'react'
import { ConversationInterface } from '@/components/conversation-interface'
import { FoundersHuddle } from '@/components/founders-huddle'

interface ConversationMessage {
  id: string
  type: 'user' | 'ai'
  content: string
  timestamp: Date
}

export default function ConversationPage() {
  const [conversationHistory, setConversationHistory] = useState<ConversationMessage[]>([
    {
      id: 'demo-user-1',
      type: 'user',
      content: "My son <PERSON>, who's 12 years old fell off his bike and hit his knee and uh it's starting uh to swell a little bit. Well, now he's um, the scrape that he had on his knee is bleeding.",
      timestamp: new Date('2025-06-18T15:34:00')
    },
    {
      id: 'demo-ai-1',
      type: 'ai',
      content: `Okay, so just to make sure I understand, your 12-year-old son <PERSON> fell off his bike and hit his knee and uh it's starting uh to swell a little bit. Well, now he's um, the scrape that he had on his knee is bleeding.

Third, keep his leg elevated to also help minimize swelling.

Fourth, over-the-counter pain relievers like ibuprofen or acetaminophen can help manage the pain, but always follow the dosage instructions on the label.

If the swelling doesn't improve after a couple of days, or if <PERSON> is in severe pain, can't put weight on his leg, or has any numbness or discoloration, it's important to see a doctor.

I can send you a summary of these tips and more information about managing knee injuries directly to your inbox so you don't have to remember it all. Would you like me to email you more tips? If so, what's your email address?`,
      timestamp: new Date('2025-06-18T15:34:30')
    }
  ])

  const handleNewMessage = (message: ConversationMessage) => {
    setConversationHistory(prev => [...prev, message])
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 h-[calc(100vh-6rem)]">
          {/* Main Conversation Area */}
          <div className="lg:col-span-2 bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <ConversationInterface 
              initialMessages={conversationHistory}
              onNewMessage={handleNewMessage}
            />
          </div>
          
          {/* Sidebar */}
          <div className="space-y-6">
            {/* Founders Huddle */}
            <FoundersHuddle className="shadow-sm" />
            
            {/* Calendar Link Card */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <h3 className="font-medium text-gray-900 mb-2">Calendar Link:</h3>
              <a 
                href="https://cal.com/our-kidz/30-min" 
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-600 hover:text-blue-700 underline text-sm break-all"
              >
                https://cal.com/our-kidz/30-min
              </a>
            </div>
            
            {/* Quick Actions */}
            <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
              <h3 className="font-medium text-gray-900 mb-3">Quick Actions</h3>
              <div className="space-y-2">
                <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors">
                  📋 View Conversation History
                </button>
                <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors">
                  📧 Email This Conversation
                </button>
                <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors">
                  🔄 Start New Conversation
                </button>
                <button className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors">
                  ⚙️ Settings
                </button>
              </div>
            </div>
            
            {/* Tips */}
            <div className="bg-blue-50 rounded-lg border border-blue-200 p-4">
              <h3 className="font-medium text-blue-900 mb-2">💡 Tips</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Be specific about symptoms and age</li>
                <li>• Include relevant medical history</li>
                <li>• Ask follow-up questions for clarity</li>
                <li>• Share conversations with your pediatrician</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
