/**
 * Personalized Mae Test Page
 * 
 * This page demonstrates the personalized Mae session management system,
 * showing how <PERSON> can provide contextual, family-specific conversations
 * for authenticated users.
 */

"use client"

import React from 'react'
import { PersonalizedMaeInterface } from '@/components/personalized-mae-interface'
import { AuthProvider } from '@/lib/auth-provider'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Sparkles, Users, MessageSquare, Shield, Brain, Heart } from 'lucide-react'

export default function PersonalizedMaePage() {
  return (
    <AuthProvider>
      <div className="min-h-screen bg-gradient-to-br from-teal-50 to-blue-50">
        <div className="container mx-auto px-4 py-8">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center gap-2 mb-4">
              <Sparkles className="w-8 h-8 text-teal-500" />
              <h1 className="text-4xl font-bold text-gray-900">Personalized Mae</h1>
            </div>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Experience Mae&apos;s personalized AI assistance tailored to your family&apos;s unique needs and history.
            </p>
            <div className="flex items-center justify-center gap-2 mt-4">
              <Badge variant="secondary" className="bg-green-100 text-green-700">
                Beta Feature
              </Badge>
              <Badge variant="outline">
                Requires Authentication
              </Badge>
            </div>
          </div>

          {/* Features Overview */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Users className="w-5 h-5 text-blue-500" />
                  Family Context
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">
                  Mae remembers your children&apos;s names, ages, medical conditions, and family preferences for personalized guidance.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <MessageSquare className="w-5 h-5 text-green-500" />
                  Conversation History
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">
                  All conversations are saved and can be referenced in future sessions for continuity of care.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Brain className="w-5 h-5 text-purple-500" />
                  Smart Recommendations
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-gray-600">
                  Receive advice tailored to your specific children&apos;s ages, developmental stages, and health concerns.
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Main Interface */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Mae Interface */}
            <div className="lg:col-span-2">
              <PersonalizedMaeInterface 
                showUserContext={true}
                autoStart={false}
                className="h-full"
              />
            </div>

            {/* Info Panel */}
            <div className="space-y-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Shield className="w-5 h-5 text-teal-500" />
                    How It Works
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-teal-100 text-teal-600 rounded-full flex items-center justify-center text-sm font-medium">
                      1
                    </div>
                    <div>
                      <p className="font-medium text-sm">Sign In</p>
                      <p className="text-xs text-gray-600">
                        Authenticate with your Our Kidz account to access personalized features.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-teal-100 text-teal-600 rounded-full flex items-center justify-center text-sm font-medium">
                      2
                    </div>
                    <div>
                      <p className="font-medium text-sm">Context Loading</p>
                      <p className="text-xs text-gray-600">
                        Mae loads your family profile, children&apos;s information, and conversation history.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-teal-100 text-teal-600 rounded-full flex items-center justify-center text-sm font-medium">
                      3
                    </div>
                    <div>
                      <p className="font-medium text-sm">Personalized Chat</p>
                      <p className="text-xs text-gray-600">
                        Start conversations with Mae who knows your family&apos;s unique context and needs.
                      </p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-teal-100 text-teal-600 rounded-full flex items-center justify-center text-sm font-medium">
                      4
                    </div>
                    <div>
                      <p className="font-medium text-sm">Session Persistence</p>
                      <p className="text-xs text-gray-600">
                        All conversations are automatically saved for future reference and continuity.
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Heart className="w-5 h-5 text-red-500" />
                    Privacy & Security
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3 text-sm">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>All data encrypted in transit and at rest</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>Family data is only accessible to you</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>Conversations saved securely in your account</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span>No data shared with third parties</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Sample Questions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <div className="p-2 bg-gray-50 rounded">
                      &quot;How is Emma&apos;s sleep schedule for a 3-year-old?&quot;
                    </div>
                    <div className="p-2 bg-gray-50 rounded">
                      &quot;What should I do about Jake&apos;s food allergies?&quot;
                    </div>
                    <div className="p-2 bg-gray-50 rounded">
                      &quot;When should I schedule Sarah&apos;s next checkup?&quot;
                    </div>
                    <div className="p-2 bg-gray-50 rounded">
                      &quot;Can you email me our conversation summary?&quot;
                    </div>
                  </div>
                  <p className="text-xs text-gray-500 mt-3">
                    Mae will personalize responses based on your children&apos;s names, ages, and medical information.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Technical Implementation Notes */}
          <Card className="mt-8">
            <CardHeader>
              <CardTitle>Technical Implementation</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
                <div>
                  <h4 className="font-medium mb-2">User Context Service</h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>• Fetches user and family data from Supabase</li>
                    <li>• Generates personalized system instructions</li>
                    <li>• Manages conversation history and context</li>
                    <li>• Provides session resumption capabilities</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Enhanced Gemini Live Hook</h4>
                  <ul className="space-y-1 text-gray-600">
                    <li>• Accepts user authentication context</li>
                    <li>• Loads family data automatically</li>
                    <li>• Saves all conversations to database</li>
                    <li>• Provides personalized greetings and responses</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AuthProvider>
  )
}