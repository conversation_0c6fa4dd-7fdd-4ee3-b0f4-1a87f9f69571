'use client'

import { useUser } from '@clerk/nextjs'
import { useState, useEffect } from 'react'
import { PersonalizedMaeInterface } from '@/components/personalized-mae-interface'

export default function TestMaeFamilyPage() {
  const { user, isLoaded } = useUser()
  const [familyData, setFamilyData] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function fetchFamilyData() {
      if (!user) return
      
      try {
        const response = await fetch('/api/mae/family/list')
        const data = await response.json()
        setFamilyData(data)
      } catch (error) {
        console.error('Error fetching family data:', error)
      } finally {
        setLoading(false)
      }
    }

    if (isLoaded && user) {
      fetchFamilyData()
    } else if (isLoaded) {
      setLoading(false)
    }
  }, [user, isLoaded])

  if (!isLoaded || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading Mae family interface...</p>
        </div>
      </div>
    )
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Authentication Required</h1>
          <p className="text-gray-600 mb-4">Please sign in to test Mae's family management features</p>
          <a href="/sign-in" className="bg-teal-600 text-white px-6 py-2 rounded-lg hover:bg-teal-700">
            Sign In
          </a>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-6xl mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-4">Mae Family Management Test</h1>
          <p className="text-gray-600 mb-6">
            Test Mae's ability to access and manage your family information. Mae can now:
          </p>
          
          <div className="bg-white rounded-lg shadow p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Family Management Features</h2>
            <div className="grid md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium mb-2">📋 Information Access</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• View your complete family list</li>
                  <li>• Get detailed information about each family member</li>
                  <li>• Search for family members by name</li>
                  <li>• Access medical conditions and allergies</li>
                </ul>
              </div>
              <div>
                <h3 className="font-medium mb-2">✏️ Family Management</h3>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Add new family members</li>
                  <li>• Update medical conditions and allergies</li>
                  <li>• Modify contact information</li>
                  <li>• Set primary care designations</li>
                </ul>
              </div>
            </div>
          </div>

          {/* Current Family Status */}
          {familyData && (
            <div className="bg-white rounded-lg shadow p-6 mb-6">
              <h2 className="text-xl font-semibold mb-4">Current Family Information</h2>
              {familyData.success ? (
                <div>
                  <p className="text-gray-600 mb-4">
                    <strong>User:</strong> {familyData.data.user_name} | 
                    <strong> Family Members:</strong> {familyData.data.family_count}
                  </p>
                  
                  {familyData.data.family_members && familyData.data.family_members.length > 0 ? (
                    <div className="grid gap-4">
                      {familyData.data.family_members.map((member: any) => (
                        <div key={member.id} className="border border-gray-200 rounded-lg p-4">
                          <div className="flex items-center justify-between">
                            <h3 className="font-medium">
                              {member.name} 
                              {member.is_primary && <span className="ml-2 text-xs bg-teal-100 text-teal-800 px-2 py-1 rounded">Primary</span>}
                            </h3>
                            <span className="text-sm text-gray-600">{member.age_description}</span>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">
                            <strong>Relationship:</strong> {member.relationship}
                            {member.gender && ` | ${member.gender}`}
                          </p>
                          {member.medical_conditions && member.medical_conditions.length > 0 && (
                            <p className="text-sm text-red-600 mt-1">
                              <strong>Medical:</strong> {member.medical_conditions.join(', ')}
                            </p>
                          )}
                          {member.allergies && member.allergies.length > 0 && (
                            <p className="text-sm text-orange-600 mt-1">
                              <strong>Allergies:</strong> {member.allergies.join(', ')}
                            </p>
                          )}
                          {member.additional_notes && (
                            <p className="text-sm text-gray-600 mt-1">
                              <strong>Notes:</strong> {member.additional_notes}
                            </p>
                          )}
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-gray-500 italic">No family members added yet</p>
                  )}
                </div>
              ) : (
                <div className="text-red-600">
                  <p>Error loading family data: {familyData.error}</p>
                </div>
              )}
            </div>
          )}

          <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
            <h2 className="text-lg font-semibold text-blue-800 mb-3">Try These Voice Commands</h2>
            <div className="grid md:grid-cols-2 gap-4 text-sm">
              <div>
                <h3 className="font-medium text-blue-700 mb-2">Information Queries:</h3>
                <ul className="space-y-1 text-blue-600">
                  <li>• "Tell me about my family"</li>
                  <li>• "What are Emma's allergies?"</li>
                  <li>• "How old is my daughter?"</li>
                  <li>• "Show me my children's medical conditions"</li>
                </ul>
              </div>
              <div>
                <h3 className="font-medium text-blue-700 mb-2">Family Management:</h3>
                <ul className="space-y-1 text-blue-600">
                  <li>• "Add my son Jake to the family"</li>
                  <li>• "Update Emma's allergies"</li>
                  <li>• "My daughter has a peanut allergy"</li>
                  <li>• "Change my emergency contact"</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Personalized Mae Interface */}
        <div className="bg-white rounded-lg shadow">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold">Mae - Personalized Family Assistant</h2>
            <p className="text-gray-600 mt-1">
              Mae now has access to your family information and can help manage it through voice commands.
            </p>
          </div>
          
          <PersonalizedMaeInterface 
            authUserId={user.id}
            userEmail={user.emailAddresses[0]?.emailAddress}
            autoLoadContext={true}
            saveConversations={true}
          />
        </div>
      </div>
    </div>
  )
}