// EXAMPLE: Using Clerk's hosted pages instead of embedded components
// This is NOT your current setup - just showing the alternative

import { useClerk } from "@clerk/nextjs"
import { But<PERSON> } from "@/components/ui/button"

export function HostedAuthExample() {
  const { openSignIn, openSignUp } = useClerk()

  return (
    <div>
      {/* This would redirect to https://accounts.our-kidz.com/sign-in */}
      <Button onClick={() => openSignIn()}>
        Sign In (Hosted)
      </Button>
      
      {/* This would redirect to https://accounts.our-kidz.com/sign-up */}
      <Button onClick={() => openSignUp()}>
        Sign Up (Hosted)
      </Button>
    </div>
  )
}

// Your CURRENT setup uses embedded components instead:
// <SignIn /> and <SignUp /> render at https://our-kidz.com/auth
// They make API calls to clerk.our-kidz.com behind the scenes