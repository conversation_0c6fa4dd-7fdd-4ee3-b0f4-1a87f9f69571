'use client';

import React from 'react';
import { Header } from '@/components/header';
import Footer from '@/components/footer';
import { HealthcareMap } from '@/components/HealthcareMap';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MapPin, Search, Phone, Calendar } from 'lucide-react';
import Link from 'next/link';

export default function HealthcarePage() {
  return (
    <main className="min-h-screen flex flex-col">
      <Header />
      
      <div className="flex-1 bg-gradient-to-b from-background to-muted/50 py-12">
        <div className="container mx-auto px-4">
          {/* Page Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl font-bold text-foreground mb-4">
              Find Healthcare Providers Near You
            </h1>
            <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
              Let <PERSON> help you find trusted pediatricians, hospitals, urgent care centers, and pharmacies in your area.
              Get verified information, ratings, and directions all in one place.
            </p>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
            <Card className="healthcare-card hover:shadow-lg transition-shadow cursor-pointer border-primary/20 bg-card">
              <CardHeader className="text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-2">
                  <Search className="h-6 w-6 text-primary" />
                </div>
                <CardTitle className="text-lg text-card-foreground">Find Pediatricians</CardTitle>
                <CardDescription>
                  Locate trusted pediatricians and specialists near you
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="healthcare-card hover:shadow-lg transition-shadow cursor-pointer border-blue-500/20 bg-card">
              <CardHeader className="text-center">
                <div className="w-12 h-12 bg-blue-500/10 rounded-full flex items-center justify-center mx-auto mb-2">
                  <Phone className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                </div>
                <CardTitle className="text-lg text-card-foreground">Emergency Care</CardTitle>
                <CardDescription>
                  Find hospitals with pediatric emergency services
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="healthcare-card hover:shadow-lg transition-shadow cursor-pointer border-orange-500/20 bg-card">
              <CardHeader className="text-center">
                <div className="w-12 h-12 bg-orange-500/10 rounded-full flex items-center justify-center mx-auto mb-2">
                  <Calendar className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                </div>
                <CardTitle className="text-lg text-card-foreground">Urgent Care</CardTitle>
                <CardDescription>
                  Quick access to urgent care centers for children
                </CardDescription>
              </CardHeader>
            </Card>

            <Card className="healthcare-card hover:shadow-lg transition-shadow cursor-pointer border-purple-500/20 bg-card">
              <CardHeader className="text-center">
                <div className="w-12 h-12 bg-purple-500/10 rounded-full flex items-center justify-center mx-auto mb-2">
                  <MapPin className="h-6 w-6 text-purple-600 dark:text-purple-400" />
                </div>
                <CardTitle className="text-lg text-card-foreground">Pharmacies</CardTitle>
                <CardDescription>
                  Find pharmacies with pediatric medications
                </CardDescription>
              </CardHeader>
            </Card>
          </div>

          {/* Instructions */}
          <Card className="mb-8 border-primary/20 bg-card">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-primary">
                <MapPin className="h-5 w-5" />
                How to Use Healthcare Finder
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center mx-auto mb-3 font-bold">
                    1
                  </div>
                  <h3 className="font-semibold mb-2 text-card-foreground">Talk to Mae</h3>
                  <p className="text-sm text-muted-foreground">
                    Ask Mae to find healthcare providers: &quot;Find pediatricians near [your location]&quot;
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center mx-auto mb-3 font-bold">
                    2
                  </div>
                  <h3 className="font-semibold mb-2 text-card-foreground">View Results</h3>
                  <p className="text-sm text-muted-foreground">
                    See providers on the map and in the detailed list with ratings and contact info
                  </p>
                </div>
                <div className="text-center">
                  <div className="w-8 h-8 bg-primary text-primary-foreground rounded-full flex items-center justify-center mx-auto mb-3 font-bold">
                    3
                  </div>
                  <h3 className="font-semibold mb-2 text-card-foreground">Get Details</h3>
                  <p className="text-sm text-muted-foreground">
                    Click on providers for full details, directions, and email summaries
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Healthcare Map Component */}
          <HealthcareMap />

          {/* Voice Assistant Section */}
          <Card className="mt-12 bg-gradient-to-r from-primary to-primary/80 text-primary-foreground">
            <CardHeader>
              <CardTitle className="text-2xl">Need Help? Ask Mae!</CardTitle>
              <CardDescription className="text-primary-foreground/80">
                Mae can help you find the perfect healthcare provider for your child&apos;s needs
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
                <div className="flex-1">
                  <p className="mb-4 text-primary-foreground">
                    Try saying: &quot;Find a pediatrician near downtown Seattle&quot; or &quot;I need urgent care that accepts children in my area&quot;
                  </p>
                  <ul className="text-sm space-y-1 text-primary-foreground/80">
                    <li>• Mae searches current information and verified sources</li>
                    <li>• Get provider details, ratings, and insurance info</li>
                    <li>• Receive email summaries with contact information</li>
                    <li>• Get directions and save favorites</li>
                  </ul>
                </div>
                <div className="flex flex-col gap-2">
                  <Button
                    variant="secondary"
                    className="bg-background text-primary hover:bg-muted"
                    asChild
                  >
                    <Link href="/">
                      Talk to Mae
                    </Link>
                  </Button>
                  <Button
                    variant="outline"
                    className="border-primary-foreground text-primary-foreground hover:bg-primary-foreground hover:text-primary"
                    onClick={async () => {
                      if (!navigator.geolocation) {
                        alert('Geolocation is not supported by this browser.');
                        return;
                      }

                      try {
                        // Check if we're on HTTPS or localhost
                        if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
                          alert('Location access requires a secure connection (HTTPS).');
                          return;
                        }

                        // Request permission first
                        const permission = await navigator.permissions.query({ name: 'geolocation' });

                        if (permission.state === 'denied') {
                          alert('Location access is blocked. Please enable location permissions in your browser settings and refresh the page.');
                          return;
                        }

                        navigator.geolocation.getCurrentPosition(
                          (position) => {
                            console.log('✅ User location obtained:', position.coords);
                            alert(`Location found! Latitude: ${position.coords.latitude.toFixed(4)}, Longitude: ${position.coords.longitude.toFixed(4)}\n\nYou can now ask Mae to find healthcare providers near you.`);
                          },
                          (error) => {
                            console.error('❌ Location error:', error);
                            console.error('Error details:', {
                              code: error?.code,
                              message: error?.message,
                              type: typeof error,
                              keys: Object.keys(error || {})
                            });

                            let errorMessage = 'Could not get your location. ';

                            if (error && typeof error.code === 'number') {
                              switch (error.code) {
                                case 1: // PERMISSION_DENIED
                                  errorMessage += 'Please enable location permissions in your browser settings.';
                                  break;
                                case 2: // POSITION_UNAVAILABLE
                                  if (error.message?.includes('429') || error.message?.includes('network service')) {
                                    errorMessage += 'Google location service is temporarily unavailable (rate limit exceeded). Please try again later or enter your location manually when talking to Mae.';
                                  } else {
                                    errorMessage += 'Location information is unavailable. You can still enter your location manually when talking to Mae.';
                                  }
                                  break;
                                case 3: // TIMEOUT
                                  errorMessage += 'Location request timed out. Please try again or enter your location manually.';
                                  break;
                                default:
                                  errorMessage += `Error code ${error.code}: ${error.message || 'Unknown error'}.`;
                                  break;
                              }
                            } else {
                              errorMessage += 'Browser location service is not working properly. You can still enter your location manually when talking to Mae.';
                            }

                            alert(errorMessage);
                          },
                          {
                            enableHighAccuracy: false, // Disable high accuracy to avoid network location
                            timeout: 15000,
                            maximumAge: 300000 // Use cached location for 5 minutes
                          }
                        );
                      } catch (error) {
                        console.error('Permission check error:', error);
                        // Fallback to direct geolocation call
                        navigator.geolocation.getCurrentPosition(
                          (position) => {
                            console.log('✅ User location obtained:', position.coords);
                            alert(`Location found! You can now ask Mae to find healthcare providers near you.`);
                          },
                          (error) => {
                            alert('Could not get your location. Please check permissions and try again.');
                          }
                        );
                      }
                    }}
                  >
                    <MapPin className="h-4 w-4 mr-2" />
                    Use My Location
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </main>
  );
}