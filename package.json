{"name": "my-v0-project", "version": "0.1.0", "private": false, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@anthropic-ai/sdk": "^0.56.0", "@clerk/backend": "^2.7.0", "@clerk/mcp-tools": "^0.3.1", "@clerk/nextjs": "^6.30.0", "@copilotkit/react-core": "^1.9.1", "@copilotkit/react-ui": "^1.9.1", "@copilotkit/runtime": "^1.9.1", "@google/genai": "^1.4.0", "@google/generative-ai": "^0.24.1", "@googlemaps/js-api-loader": "^1.16.10", "@googlemaps/react-wrapper": "^1.2.0", "@handit.ai/ai-wrapper": "^1.0.0", "@handit.ai/node": "^1.2.0", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@supabase/supabase-js": "^2.51.0", "@types/canvas-confetti": "^1.9.0", "@types/nodemailer": "^6.4.17", "@vercel/mcp-adapter": "^1.0.0", "@vis.gl/react-google-maps": "^1.5.4", "audio-decode": "^2.2.3", "autoprefixer": "^10.4.20", "axios": "^1.10.0", "Blob": "^0.10.0", "canvas-confetti": "^1.9.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^3.6.0", "embla-carousel-react": "8.5.1", "framer-motion": "^12.23.12", "fs": "^0.0.1-security", "input-otp": "1.4.1", "lit": "^3.3.0", "lucide-react": "^0.454.0", "mermaid": "^11.9.0", "microphone-stream": "^6.0.1", "mime": "^4.0.7", "next": "^15.2.4", "next-themes": "^0.4.6", "node": "^22.16.0", "node-fetch": "^3.3.2", "node-fs": "^0.1.7", "nodemailer": "^7.0.3", "openai": "^4.104.0", "react": "^18.2.0", "react-day-picker": "^8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.54.1", "react-markdown": "^10.1.0", "react-resizable-panels": "^2.1.7", "react-theme-provider": "^0.1.3", "recharts": "2.15.0", "sonner": "^1.7.1", "svix": "^1.71.0", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "wavefile": "^11.0.0", "writefile": "^0.2.8", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "eslint": "9.28.0", "eslint-config-next": "15.3.3", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}