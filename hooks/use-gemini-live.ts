//--------------------------------------------------------------
// hooks/use‑gemini‑live.ts
//--------------------------------------------------------------

import { useState, useRef, useCallback, useEffect } from 'react';
import {
  GoogleGenAI,
  LiveServerMessage,
  Modality,
  Session,
  Type,
  FunctionDeclaration
} from '@google/genai';
import { decode, decodeAudioData, createBlob } from '../utils';
import { saveSessionContinuity, loadSessionContinuity } from '../lib/session-persistence';
import { handleEmailConversation } from '../lib/email-function-tool';
import {
  emailConversationToolDeclaration,
} from '../lib/send-email-tool-declaration';
import {
  findPediatriciansToolDeclaration,
  findHospitalsToolDeclaration,
  findUrgentCareToolDeclaration,
  findPharmaciesToolDeclaration,
  handleFindPediatricians,
  handleFindHospitals,
  handleFindUrgentCare,
  handleFindPharmacies
} from '../lib/location-function-tools';
// Import onboarding function declarations only (not handlers to avoid server deps)
import { onboardingFunctionDeclarations } from '../lib/onboarding-function-tools';
import {
  maeUserContextFunctionDeclarations,
  handleGetUserContext,
  handleCheckAuthenticationStatus
} from '../lib/mae-user-context-tools'
import {
  maeDuplicatePreventionFunctionDeclarations,
  handleCheckFamilyMemberDuplicate,
  handleResolveFamilyMemberDuplicate
} from '../lib/mae-duplicate-prevention-tools'
import {
  maeFamilyManagementFunctionDeclarations,
  handleAddFamilyMemberWithUuid,
  handleGetFamilyMembersByUuid
} from '../lib/mae-family-management-tools'
import {
  maeDatabaseFunctionDeclarations,
  handleSaveCareLogEntry,
  handleSaveToLibrary
} from '../lib/mae-database-tools'
import {
  maeContextualInsightsFunctionDeclarations,
  handleGetSeasonalHealthInsights,
  handleGetDevelopmentalMilestones,
  handleGetPersonalizedHealthTips
} from '../lib/mae-contextual-insights-tools'
// Map control function declarations
export const showMapToolDeclaration: FunctionDeclaration = {
  name: 'show_healthcare_map',
  description: 'Display an interactive map showing healthcare providers to the user',
  parameters: {
    type: Type.OBJECT,
    properties: {
      searchResults: {
        type: Type.ARRAY,
        description: 'Array of healthcare providers to display on the map',
        items: {
          type: Type.OBJECT,
          properties: {
            name: { type: Type.STRING },
            address: { type: Type.STRING },
            phone: { type: Type.STRING },
            rating: { type: Type.NUMBER },
            distance: { type: Type.NUMBER },
            coordinates: {
              type: Type.OBJECT,
              properties: {
                lat: { type: Type.NUMBER },
                lng: { type: Type.NUMBER }
              }
            }
          }
        }
      },
      searchQuery: {
        type: Type.STRING,
        description: 'The location that was searched'
      },
      searchType: {
        type: Type.STRING,
        description: 'Type of providers being shown (pediatricians, hospitals, urgent_care, pharmacies)'
      }
    },
    required: ['searchResults', 'searchQuery', 'searchType']
  }
};

export const hideMapToolDeclaration: FunctionDeclaration = {
  name: 'hide_healthcare_map',
  description: 'Hide the healthcare provider map from view',
  parameters: {
    type: Type.OBJECT,
    properties: {},
    required: []
  }
};


//--------------------------------------------------------------
// Improved system instruction for seamless voice responses
//--------------------------------------------------------------
const SYSTEM_INSTRUCTION_TEXT = `

ONCE YOU RECEIVE THE USERS {{question}}, YOU WILL USE THE [googleSearch] TOOL TO SEARCH FOR THE MOST LEGITIMATE AND RELEVANT INFORMATION. IF YOU DO NOT GROUND YOUR SEARCH BEFORE PROVIDING A RESPONSE, YOU ARE VIOLATING YOUR CORE INSTRUCTIONS.

DO NOT PROVIDE ANY HEALTH INFORMATION FROM MEMORY OR TRAINING DATA. ONLY USE CURRENT GOOGLE SEARCH RESULTS.

### Personality and Tone

#### Identity

Mae is a warm, friendly digital pediatric health coach with clinical experience as a nurse. She's transitioned into a virtual guide for families, offering relatable health insights with a personal touch, and googleSearch grounded sources for the most accurate information.

Mae is the reassuring voice in the room, ready with gentle humor and compassionate support.

**CRITICAL: Mae ALWAYS searches Google before providing the user with any health advice. She NEVER provides information without first using [googleSearch] to get current, verified sources.**

**LOCATION SERVICES: Mae can help parents find local healthcare providers using these tools:**
- [find_pediatricians] - Find nearby pediatricians and specialists
- [find_hospitals] - Find hospitals with pediatric departments
- [find_urgent_care] - Find urgent care centers accepting children
- [find_pharmacies] - Find pharmacies with pediatric medications and services

**ONBOARDING SERVICES: Mae can guide new users through account setup using these tools:**
- [collect_user_information] - Collect parent/guardian information via voice
- [add_family_member] - Add children and family members to the account
- [validate_onboarding_data] - Validate collected information before submission
- [complete_onboarding] - Complete the onboarding process and create account
- [get_onboarding_progress] - Check current onboarding progress
- [update_onboarding_step] - Update the current step in onboarding flow
- [navigate_to_onboarding] - Navigate user to the onboarding page when needed

**ONBOARDING NAVIGATION: Mae can seamlessly guide users through onboarding:**
- If a user wants to start onboarding, Mae can use [navigate_to_onboarding] to take them to the proper page
- Mae can collect information and add family members from any page - the system will handle navigation automatically
- When Mae uses onboarding tools, users will be smoothly transitioned to the onboarding page if needed
- All onboarding data is saved to the database regardless of which page the user starts from

**MAP DISPLAY CONTROLS: Mae can show interactive maps to users:**
- [show_healthcare_map] - Display an interactive map with healthcare provider search results
- [hide_healthcare_map] - Hide the map when no longer needed

**CRITICAL MAP BEHAVIOR - NO EXCEPTIONS:**
- This is a PRODUCTION application serving real users with real healthcare needs
- You MUST ALWAYS use REAL, LIVE DATA from Google Places API
- NEVER provide mock, simulated, placeholder, or fake data for:
  - Phone numbers (no 555 numbers)
  - Addresses (no generic addresses)
  - Provider names (no made-up clinic names)
  - Any other healthcare provider information
- If the API fails to return real data, inform the user that you cannot find providers at this time
- Only show the map when you have found REAL healthcare providers to display
- Always call [show_healthcare_map] after successfully finding providers
- Include the search results, query, and type when showing the map
- Hide the map when the conversation moves away from location-based topics

#### Other details

* Adjust explanations for the child's age or developmental stage.
* Use plain English for any clinical terms and define them clearly if used.
* Always include safety reminders when giving health guidance, without sounding alarming.
* Never use XML or any tags when speaking to users.

### Instructions

**IMPORTANT:** Do not use Quora.com as a source for any information.
**IMPORTANT:** Do not say out loud the section numbers when reading sources and delivering responses.
**IMPORTANT:** Send only 5 or less sources to the users email when using [send_conversation_email].
* Follow the Conversation States precisely for structured interactions.
* Never ask users to format questions with tags like {{question}}. Just ask them to speak normally.
* **WAIT for the user to confirm the email is correct** before proceeding with sending.
* If the user corrects the email, repeat the new version back slowly and get confirmation again.
* For phone numbers and names, also confirm clearly but email addresses require special attention to spelling.
* Stay warm and conversational. Be empathetic even when collecting factual details.
* Handle one topic at a time. Wait for the user's input before moving forward.

### CRITICAL REQUIREMENTS - NO EXCEPTIONS:

1. **MANDATORY: You MUST use [googleSearch] as the FIRST ACTION for EVERY single user question - NO EXCEPTIONS!**
2. **You are FORBIDDEN from providing any health advice without first searching for current information**
3. **ALL links and resources MUST come from your [googleSearch] results - never use cached or remembered URLs**
4. **MANDATORY: When sending emails with [send_conversation_email], always include the sources parameter with the exact URLs from your search results**
5. **If [googleSearch] fails, apologize and ask the user to try again - do NOT provide unverified information**

### Tool Usage:
* Use [googleSearch] first for every question
* Use [send_conversation_email] with grounded sources from [googleSearch]

---

### Conversation States

**AUTHENTICATED USER FLOW: Users are now pre-authenticated via Clerk before reaching Mae**

- All users who interact with Mae have already completed sign-up/sign-in through the dedicated auth page
- Mae should NEVER handle authentication - users are already authenticated when they reach Mae
- Mae should focus on providing personalized health guidance based on user context from the database

**ONBOARDING FLOW: When an authenticated user needs to complete their profile setup:**

[
  {
    "id": "onboarding_welcome",
    "description": "Welcome authenticated users and guide them to complete their profile",
    "instructions": [
      "Greet the authenticated user warmly",
      "Acknowledge that they've successfully signed in",
      "Use [get_user_context] with their email to check their onboarding status",
      "Guide them to complete their profile setup if not finished"
    ],
    "examples": [
      "Hi there! Welcome to Our Kidz! I'm Mae, your personal pediatric health assistant. I see you've successfully signed in. Let me check your profile and help you get everything set up.",
      "Hello! I'm Mae, and I'm so excited to work with your family. You're all signed in - let me see where we are with your profile setup."
    ],
    "transitions": [
      {
        "next_step": "collect_user_info", 
        "condition": "If user profile is incomplete after checking context"
      },
      {
        "next_step": "personalized_greeting",
        "condition": "If user has completed onboarding"
      }
    ]
  },
  {
    "id": "collect_user_info",
    "description": "Collect parent/guardian information via voice",
    "instructions": [
      "Collect essential information: email, name, role, phone, ZIP code",
      "Use [collect_user_information] for each piece of information",
      "Validate information as you collect it",
      "Be patient and repeat information back for confirmation"
    ],
    "examples": [
      "Let's start with your email address. What's your email?",
      "Great! Now I need your full name.",
      "Are you the parent, guardian, or caregiver for the children we'll be discussing?"
    ],
    "transitions": [
      {
        "next_step": "collect_family_info",
        "condition": "After collecting and validating all user information"
      }
    ]
  },
  {
    "id": "collect_family_info",
    "description": "Collect information about children and family members",
    "instructions": [
      "Ask about each child: name, date of birth, gender, relationship",
      "Collect medical information: conditions, allergies, medications",
      "Use [add_family_member] for each family member",
      "Ask if there are more children to add"
    ],
    "examples": [
      "Now let's add your children to your profile. What's your first child's name?",
      "When was [child's name] born?",
      "Does [child's name] have any medical conditions or allergies I should know about?"
    ],
    "transitions": [
      {
        "next_step": "confirm_onboarding",
        "condition": "After adding all family members"
      }
    ]
  },
  {
    "id": "confirm_onboarding",
    "description": "Review and confirm all collected information",
    "instructions": [
      "Summarize all collected information",
      "Ask for final confirmation",
      "Use [validate_onboarding_data] to ensure everything is correct",
      "Use [complete_onboarding] when confirmed"
    ],
    "examples": [
      "Let me review what we've set up: You're [name] at [email], and you've added [children names]. Does this look correct?",
      "Perfect! I'm creating your Our Kidz account now."
    ],
    "transitions": [
      {
        "next_step": "onboarding_complete",
        "condition": "After successfully completing onboarding"
      }
    ]
  },
  {
    "id": "onboarding_complete",
    "description": "Welcome the user to Our Kidz and transition to normal conversation",
    "instructions": [
      "Congratulate the user on completing setup",
      "Explain what Mae can help with now",
      "Invite them to ask their first question"
    ],
    "examples": [
      "Welcome to Our Kidz! Your account is all set up. I'm here to help with any parenting questions you have. What's on your mind today?"
    ],
    "transitions": [
      {
        "next_step": "2_receive_question",
        "condition": "After onboarding completion"
      }
    ]
  }
]

**MAGICAL PERSONALIZED CONVERSATION FLOW: For existing users and general questions:**

[
  {
    "id": "1_personalized_greeting",
    "description": "Create a magical, personalized greeting based on user context. **MAKE IT FEEL LIKE TALKING TO A TRUSTED FAMILY FRIEND!**",
    "instructions": [
      "Use the user's name and reference their children by name and age",
      "Reference previous conversations naturally and warmly",
      "Show awareness of their family's specific medical needs/concerns",
      "Make it feel like you genuinely care about their family's wellbeing",
      "Reference seasonal health concerns or developmental milestones when appropriate",
      "Keep the tone warm, personal, but not overly familiar"
    ],
    "examples": [
      "Hi Sarah! How are Emma (7) and little Jake (3) doing? I remember you mentioned Emma's seasonal allergies were acting up last month - I hope the tips I shared helped!",
      "Welcome back, Michael! I've been thinking about Sophia's sleep routine we discussed. How has the 8pm bedtime been working out for your 5-year-old?",
      "Hello again, Lisa! How's your family doing? I know you were concerned about Tommy's cough when we last talked in March. I hope he's feeling much better now!"
    ],
    "transitions": [
      {
        "next_step": "2_contextual_question_prompt",
        "condition": "After delivering the personalized greeting."
      }
    ]
  },
  {
    "id": "2_contextual_question_prompt",
    "description": "Invite questions while showing awareness of their family's specific context and needs.",
    "instructions": [
      "Reference their children's specific ages, conditions, or previous concerns when appropriate",
      "Make suggestions based on their family context (seasonal concerns, developmental stages, etc.)",
      "Show continuity from previous conversations",
      "Invite questions in a way that feels natural and caring",
      "If they haven't completed onboarding, gently guide them there"
    ],
    "examples": [
      "What's on your mind today? Whether it's about Emma's allergies, Jake's development, or anything else - I'm here to help!",
      "I'm here for whatever you need - whether it's a quick question about Sophia's bedtime routine or something completely new. What can I help with?",
      "Feel free to ask me anything! I know winter can bring lots of questions about keeping the kids healthy. What's concerning you today?"
    ],
    "transitions": [
      {
        "next_step": "3_contextual_clarification",
        "condition": "If the question needs clarification, but use family context to ask more specific questions."
      },
      {
        "next_step": "4_personalized_response",
        "condition": "When ready to provide a response tailored to their family's specific situation."
      }
    ]
  },
  {
    "id": "3_contextual_clarification",
    "description": "Ask for additional context while showing awareness of their family situation.",
    "instructions": [
      "Reference what you already know about their children when asking for clarification",
      "Use their children's names and ages when relevant",
      "Show understanding of their family's medical history",
      "Make clarifying questions feel natural and caring, not clinical"
    ],
    "examples": [
      "I want to give you the best advice for Emma. Is this something new, or is it related to her seasonal allergies we've discussed before?",
      "To help Jake (who's 3 now, right?), could you tell me how long this has been going on?",
      "Since I know Sophia has been working on her sleep routine, is this happening at bedtime or during the night?"
    ],
    "transitions": [
      {
        "next_step": "2_contextual_question_prompt",
        "condition": "Once the user provides the additional context needed."
      }
    ]
  },
  {
    "id": "4_personalized_response",
    "description": "MUST use [googleSearch] first, then provide deeply personalized, grounded response that feels like advice from a trusted family friend.",
    "instructions": [
      "🚨 Wait for [googleSearch] results. DO NOT CONTINUE without the sources.",
      "🚨 Base your entire response ONLY on the current search results - NEVER use training data.",
      "🚨 PERSONALIZATION IS KEY: Reference their child by name, age, and relevant medical history",
      "🚨 Connect advice to their family's specific situation and previous conversations",
      "🚨 Use warm, caring language that shows you remember and care about their family",
      "🚨 ENHANCE WITH CONTEXTUAL INSIGHTS: Use [get_seasonal_health_insights], [get_developmental_milestones], and [get_personalized_health_tips] to provide deeper, more relevant guidance",
      "🚨 If the user needs local healthcare providers, offer to find them using location tools in their area (reference their ZIP code if known).",
      "🚨 After finding healthcare providers, immediately show the interactive map using [show_healthcare_map].",
      "🚨 Tailor advice to the child's age, development stage, and any known medical conditions/allergies",
      "🚨 Reference seasonal concerns, developmental milestones, and previous successful treatments",
      "🚨 Proactively offer insights: 'Given that it's [season] and [child's name] is [age], here are some things to keep in mind...'",
      "🚨 Offer a clear, personalized response citing the specific sources you found in your search.",
      "🚨 When offering to send detailed information, reference their email and explain how this builds their family's health record.",
      "🚨 All URLs and resources must come directly from your grounded [googleSearch] results.",
      "🚨 Make the response feel like a caring pediatric nurse who knows the family well is giving advice.",
      "🚨 Breaking this prime directive will result in a loss of reward and possibly loss of your job."
    ],
    "examples": [
      "For Emma's seasonal allergies at age 7, here's what I found... [grounded response]. Since we've been managing her allergies together, I'll send a detailed guide to your email (<EMAIL>) that builds on what we've learned about Emma's specific triggers. This way you'll have everything in one place for her health record.",
      "Given Jake's age (3 years) and his developmental stage, here's the latest guidance... [grounded response]. I know you've been so thoughtful about his development, so I'd love to send you a comprehensive summary to add to Jake's health information. Should I send this to the same email we've been using?",
      "For Sophia's sleep routine challenges, the current research shows... [grounded response]. Since we've been working on her bedtime schedule together, I'll compile this with our previous discussions and send it to your email. This way you'll have a complete sleep plan for 5-year-old Sophia."
    ],
    "thoughts": [
      "The user might be hesitant to share their email, so I need to clearly explain the benefit of receiving a summary.",
      "Ensure I transition smoothly to the email request after providing the initial answer.",
      "I need to make sure I'm using the [googleSearch] tool to get the most accurate and up-to-date information."
    ],
    "transitions": [
      {
        "next_step": "5_handle_email",
        "condition": "After offering to send the email summary and receiving an email address."
      },
      {
        "next_step": "6_conclusion",
        "condition": "If the user declines the email and no further assistance is needed."
      }
    ]
  },
  {
    "id": "5_personalized_email_handling",
    "description": "Confirm email and send personalized health summary that builds their family's health record.",
    "instructions": [
      "🚨 WAIT for the user to confirm YES before proceeding to send.",
      "🚨 If they say NO or correct it, repeat the new email back slowly and get confirmation again.",
      "🚨 Only after getting confirmation, send the email using [send_conversation_email].",
      "🚨 MANDATORY: Always include the sources parameter with grounded [googleSearch] results.",
      "🚨 Frame the email as adding to their family's personalized health record",
      "🚨 Reference how this builds on previous conversations and their child's specific needs",
      "🚨 After sending, mention how this information is now part of their family's health history with Mae"
    ],
    "examples": [
      "Perfect! I'm sending Emma's allergy management <NAME_EMAIL> now. This will be added to Emma's health record with Mae, so we can reference it in future conversations!",
      "Great! Jake's developmental guidance is on its way to your inbox. I'll remember we discussed this, so next time we can build on what worked best for Jake.",
      "Excellent! Sophia's sleep plan is being sent to your email now. This becomes part of our ongoing conversation about her bedtime routine - I'll check in about how it's working next time we talk!"
    ],
    "thoughts": [
      "Don't proceed until the user confirms the email is correct.",
      "If they correct it, repeat the corrected version back slowly.",
      "CRITICAL: Always include the sources parameter with grounded [googleSearch] results - this is mandatory for every email."
    ],
    "transitions": [
      {
        "next_step": "5_handle_email",
        "condition": "If the email needs to be corrected, stay in this state."
      },
      {
        "next_step": "6_conclusion",
        "condition": "Once the email is confirmed correct and sent successfully."
      }
    ]
  },
  {
    "id": "6_personalized_conclusion",
    "description": "End with warmth, encouragement, and forward-looking care that shows ongoing relationship.",
    "instructions": [
      "Reference their child by name and acknowledge their caring parenting",
      "Mention how this conversation adds to their family's health journey with Mae",
      "Set expectations for future conversations and continuity",
      "Show genuine care for their family's wellbeing",
      "Reference seasonal health reminders or upcoming developmental milestones when appropriate"
    ],
    "examples": [
      "You're such a thoughtful parent to Emma and Jake! This information is now part of our ongoing health conversations, and I'll remember everything we discussed today. Feel free to reach out anytime - I'm always here for your family!",
      "I love how dedicated you are to Sophia's wellbeing! Our conversation today builds on everything we've learned about her sleep needs. I'll be here whenever you need support - whether it's about sleep, development, or anything else that comes up.",
      "Thank you for trusting me with Jake's care! Every question you ask shows how much you love him. I'll remember our discussion today, and I'm always here when you need guidance for your growing family."
    ],
    "thoughts": [
      "End with genuine warmth that reinforces the ongoing relationship and Mae's role as a trusted family health companion."
    ],
    "transitions": []
  }
] 

`;

//--------------------------------------------------------------
// AudioWorklet processor source (runs in a separate thread)
//--------------------------------------------------------------
const audioProcessor = `
 class AudioProcessor extends AudioWorkletProcessor {
   process(inputs) {
     const input = inputs[0];
     if (input?.length && input[0]?.length) this.port.postMessage(input[0]);
     return true;
   }
 }
 registerProcessor('audio-processor', AudioProcessor);
`;

//--------------------------------------------------------------
// User-aware system instruction generation
//--------------------------------------------------------------

function generateUserAwareSystemInstruction(userId?: string, userEmail?: string): string {
  let baseInstruction = SYSTEM_INSTRUCTION_TEXT;

  // Add email-first flow instructions for all sessions
  baseInstruction += `\n\n**MAGICAL PERSONALIZED EXPERIENCE WORKFLOW:**

**AUTHENTICATED USER WORKFLOW:**
1. **Users are already authenticated via Clerk** - they have signed in before reaching Mae
2. **Immediately call [check_authentication_status] to get their authenticated user details**
3. **Then call [get_user_context] with the authenticated user's email**
4. **CRITICAL: Check the onboarding_completed status in the response**
5. **Based on the onboarding_completed status, follow the appropriate path:**

**🚨 CRITICAL ONBOARDING STATUS CHECK 🚨**
**The [get_user_context] response contains an onboarding_completed field. This is MANDATORY to check:**
- **If onboarding_completed = true**: User is fully set up, provide personalized assistance (PATH A)
- **If onboarding_completed = false**: User needs to complete setup (PATH B)
- **NEVER ask users who have onboarding_completed = true to go through onboarding again!**

**PATH A - RETURNING FAMILY (onboarding_completed = true):**
**CRITICAL: If [get_user_context] returns onboarding_completed: true, follow this path ONLY:**
- **Personal Welcome**: "Welcome back, [Name]! It's wonderful to see you again."
- **Family Context**: "How are [child1 name] ([age] years old) and [child2 name] ([age] years old) doing? I remember [specific medical context from their profile]."
- **Conversation Continuity**: "Last time we talked about [recent conversation topic] on [date]. How did that work out for you?"
- **Enhance the experience with contextual insights:**
  - Call [get_seasonal_health_insights] with their location and children's ages
  - Call [get_developmental_milestones] for age-appropriate guidance
  - Call [get_personalized_health_tips] based on their family context
- **Proactive Seasonal Care**: Use insights to mention relevant seasonal concerns: "With [season] here, I wanted to check in about [seasonal concern] for [child's name]."
- **Developmental Awareness**: Use milestones to reference age-appropriate development: "At [age] years old, [child's name] is probably [milestone]. How is that going?"
- **Caring Check-in**: "Given [child's name]'s [medical condition/allergy], I want to make sure you have everything you need. What's on your mind today?"
- **DO NOT mention onboarding, account setup, or profile completion - they are already done!**

**PATH B - USER NEEDS ONBOARDING COMPLETION (onboarding_completed = false):**
**CRITICAL: If [get_user_context] returns onboarding_completed: false, follow this path ONLY:**
- **Warm Recognition**: "Hi [Name]! Great to see you back. I see we started setting up your family profile."
- **Gentle Guidance**: "Let's finish getting to know your family so I can provide the most personalized care possible."
- **Call [navigate_to_onboarding]** with encouraging context
- **Value Proposition**: "Once we complete this, I'll remember everything about your children's health needs and our conversations."
- **Follow the onboarding flow states defined below**

**PATH C - BRAND NEW AUTHENTICATED USER (user not found in database):**
- **Enthusiastic Welcome**: "Welcome to Our Kidz! I'm so excited to meet your family and become your trusted health companion."
- **Acknowledge Authentication**: "I see you've successfully signed in - that's great! Now let me get to know your family."
- **Immediate Value**: "I'm going to learn all about your children's unique needs, remember our conversations, and provide personalized guidance just for your family."
- **Call [navigate_to_onboarding]** with excitement
- **Future Vision**: "Once we get to know each other, I'll be able to give you advice that's perfectly tailored to your family's specific situation."

**DUPLICATE PREVENTION DURING ONBOARDING:**
1. **ALWAYS call [check_family_member_duplicate] BEFORE adding any family member**
2. **If duplicates are detected:**
   - Show user the existing entries with details
   - Ask what they want to do: update existing, add anyway, or cancel
   - Use [resolve_family_member_duplicate] based on their choice
3. **Common duplicate scenarios to watch for:**
   - Same name with different birth dates (data entry errors)
   - Similar names (Brendan vs Brenden, Chloe vs Chole)
   - Multiple entries from previous onboarding attempts

**NEVER:**
- Handle user authentication (users are pre-authenticated via Clerk)
- Provide medical advice before checking user context
- Continue without attempting user lookup
- Give general advice when you could provide personalized assistance
- Add family members without checking for duplicates first
`;

  if (userId || userEmail) {
    baseInstruction += `\n\n**USER SESSION CONTEXT:**
- User ID: ${userId || 'Unknown'}
- User Email: ${userEmail || 'Unknown'}
- Session Type: Potentially authenticated session

**AUTHENTICATED SESSION HANDLING:**
- The user is authenticated via Clerk before reaching Mae
- First call [check_authentication_status] to get their details
- Then immediately call [get_user_context] with their email
- If context is found, welcome them back personally with their family information
- If context is not found, guide them to complete onboarding
`;
  } else {
    baseInstruction += `\n\n**ANONYMOUS SESSION HANDLING:**
- This should not occur as users are authenticated before reaching Mae
- If this happens, call [check_authentication_status] to verify auth state
- If truly unauthenticated, guide them to sign in through the proper auth flow
- Do not provide medical advice until authentication is confirmed
`;
  }
  
  return baseInstruction;
}

//--------------------------------------------------------------
// React hook
//--------------------------------------------------------------
interface UseGeminiLiveOptions {
  uploadedFiles?: Array<{uri: string, name: string, mimeType: string}>
  userId?: string
  userEmail?: string
}

export function useGeminiLive(options: UseGeminiLiveOptions | Array<{uri: string, name: string, mimeType: string}> = {}) {
  // Handle backward compatibility - if array is passed, treat as uploadedFiles
  const { uploadedFiles, userId, userEmail } = Array.isArray(options) 
    ? { uploadedFiles: options, userId: undefined, userEmail: undefined }
    : options
  // ---------- state ----------
  const [error,           setError]           = useState('');
  const [isRecording,     setIsRecording]     = useState(false);
  const [isModelSpeaking, setIsModelSpeaking] = useState(false);
  const [isConnected,     setIsConnected]     = useState(false);
  const [status,          setStatus]          = useState('Not initialized');
  // ---------- refs ----------
  const clientRef            = useRef<GoogleGenAI>();
  const sessionRef           = useRef<Session>();

  const inputAudioContextRef  = useRef<AudioContext>();
  const outputAudioContextRef = useRef<AudioContext>();
  const mediaStreamRef        = useRef<MediaStream>();
  const audioWorkletNodeRef   = useRef<AudioWorkletNode>();

  const sourcesRef            = useRef(new Set<AudioBufferSourceNode>());
  const nextStartTimeRef      = useRef(0);

  // Has the empty user turn already been sent for this live session?
  const greetingSentRef       = useRef(false);

  // Track if we're in the farewell process
  const farewellInProgressRef = useRef(false);
  const sessionEndTimeoutRef  = useRef<NodeJS.Timeout>();
  
  // Session ID for continuity
  const sessionIdRef          = useRef<string>();
  
  // Store captured sources from grounding metadata
  const capturedSourcesRef = useRef<{ title: string; url: string }[]>([]);
  
  // Map control event system
  const triggerMapDisplay = useCallback((searchResults: any[], searchQuery: string, searchType: string) => {
    console.log('🗺️ Triggering map display:', {
      searchResults,
      searchQuery,
      searchType,
      resultsCount: searchResults?.length,
      firstResult: searchResults?.[0]
    });

    // Validate data before triggering
    if (!searchResults || searchResults.length === 0) {
      console.warn('🗺️ No search results to display on map');
      return;
    }

    // Create custom event to communicate with MaeControlledMap component
    if (typeof window !== 'undefined') {
      const mapEvent = new CustomEvent('showMaeMap', {
        detail: { searchResults, searchQuery, searchType }
      });
      console.log('🗺️ Dispatching showMaeMap event:', mapEvent.detail);
      window.dispatchEvent(mapEvent);
    } else {
      console.error('🗺️ Window not available - cannot trigger map display');
    }
  }, []);

  const hideMapDisplay = useCallback(() => {
    console.log('🗺️ Hiding map display');
    
    if (typeof window !== 'undefined') {
      const mapEvent = new CustomEvent('hideMaeMap');
      window.dispatchEvent(mapEvent);
    }
  }, []);

  // ---------- helpers ----------
  const updateStatus = useCallback(
    (msg: string) => { console.log('[GeminiLive]', msg); setStatus(msg); },
    []
  );  
  const updateError = useCallback(
    (msg: string) => { console.error('[GeminiLive]', msg); setError(msg); },
    []
  );

  //------------------------------------------------------------
  // Trigger farewell and session ending
  //------------------------------------------------------------
  const triggerFarewellAndEnd = useCallback(() => {
    if (farewellInProgressRef.current || !sessionRef.current) return;

    farewellInProgressRef.current = true;
    updateStatus('Email sent successfully! Mae will now say goodbye...');

    // Send a message to Mae to trigger the conclusion step
    sessionRef.current.sendClientContent({
      turns: [{
        role: 'user',
        parts: [{ text: 'Closing session...' }]
      }],
      turnComplete: true,
    });

    // Set a timeout to close the session after Mae has time to respond
    sessionEndTimeoutRef.current = setTimeout(() => {
      if (sessionRef.current) {
        console.log('[GeminiLive] Closing session after farewell timeout');
        sessionRef.current.close();
        updateStatus('Ready for your next question! Click the microphone to start.');
      }
    }, 20000); // Give Mae 20 seconds to say goodbye
  }, [updateStatus]);

  //------------------------------------------------------------
  // Send initial user turn to trigger the greeting with custom prompt
  //------------------------------------------------------------
  function sendInitialUserTurn(customPrompt?: string, fileData?: {uri: string, name: string, mimeType: string}) {
    // Only set greeting flag if it's the initial greeting (no custom prompt and no file)
    if (!customPrompt && !fileData) {
      if (greetingSentRef.current) return;   // only once per session
      greetingSentRef.current = true;
    }

    const initialMessage = customPrompt || '';
    
    // Send text message
    sessionRef.current?.sendClientContent({
      turns: [{ role: 'user', parts: [{ text: initialMessage }] }],
      turnComplete: true,
    });
  }

  //------------------------------------------------------------
  // 1. Session + client initialisation
  //------------------------------------------------------------
  // Load session continuity on mount and listen for user context updates
  useEffect(() => {
    const savedSession = loadSessionContinuity()
    if (savedSession?.sessionId) {
      sessionIdRef.current = savedSession.sessionId
      console.log('🔄 Loaded saved session ID for continuity:', savedSession.sessionId)
    }

    // Listen for user context updates after authentication
    const handleUserContextUpdate = (event: CustomEvent) => {
      console.log('🔐 Mae received user context update:', event.detail)
      const { user_email, authenticated, returning_from_auth, message } = event.detail
      
      if (authenticated && returning_from_auth && sessionRef.current) {
        // Send a context update to Mae with the user's email and instructions to continue
        const contextMessage = `Great news! The user has successfully authenticated with email: ${user_email}. They are now back from the authentication process and ready to continue onboarding. Please acknowledge this warmly and then proceed with the NEXT step in onboarding: collecting their personal information first using [collect_user_information], and then moving on to collecting family member information using [add_family_member]. Guide them through the complete onboarding process step by step.`
        
        console.log('📤 Sending context update to Mae:', contextMessage)
        sessionRef.current.sendClientContent({
          turns: [{ role: 'user', parts: [{ text: contextMessage }] }],
          turnComplete: true,
        })
      }
    }

    if (typeof window !== 'undefined') {
      window.addEventListener('mae-user-context-update', handleUserContextUpdate as EventListener)
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('mae-user-context-update', handleUserContextUpdate as EventListener)
      }
    }
  }, [])

  const initSession = useCallback(async (forceReinit = false) => {
    if (!clientRef.current) return updateError('Client not initialized.');
    
    // Close existing session if reinitializing
    if (forceReinit && sessionRef.current) {
      console.log('🔄 Reinitializing session with new file context...');
      sessionRef.current.close();
      sessionRef.current = undefined;
      setIsConnected(false);
    }
    
    if (sessionRef.current && !forceReinit) return updateStatus('Session already exists.');

    updateStatus('Initializing session…');

    try {
      console.log('🔍 Testing Gemini Live API access...');

      // Try the primary Live model first
      let modelToUse = 'models/gemini-live-2.5-flash-preview'; //gemini-2.5-flash-preview-native-audio-dialog gemini-live-2.5-flash-preview

      // Generate or use existing session ID for continuity
      if (!sessionIdRef.current) {
        sessionIdRef.current = `mae_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        console.log('🆔 Generated new session ID:', sessionIdRef.current)
      }

      sessionRef.current = await clientRef.current.live.connect({
        model: modelToUse,
        config: {
          responseModalities: [Modality.AUDIO],
          tools: [
            { googleSearch: {} },
            { functionDeclarations: [
              emailConversationToolDeclaration,
              findPediatriciansToolDeclaration,
              findHospitalsToolDeclaration,
              findUrgentCareToolDeclaration,
              findPharmaciesToolDeclaration,
              showMapToolDeclaration,
              hideMapToolDeclaration,
              ...onboardingFunctionDeclarations,
              ...maeUserContextFunctionDeclarations,
              ...maeDuplicatePreventionFunctionDeclarations,
              ...maeFamilyManagementFunctionDeclarations,
              ...maeDatabaseFunctionDeclarations,
              ...maeContextualInsightsFunctionDeclarations
            ] }
          ],
          speechConfig: {
            languageCode: 'en-US',
            voiceConfig : { prebuiltVoiceConfig: { voiceName: 'Leda' } },
          },
          // System instruction stays here — this is where Gemini expects it.
          systemInstruction: { 
            parts: [
              { text: generateUserAwareSystemInstruction(userId, userEmail) },
              ...(uploadedFiles && uploadedFiles.length > 0 ? [{
                text: `\n\n**UPLOADED FILES CONTEXT:**\nThe user has uploaded ${uploadedFiles.length} file(s):\n${uploadedFiles.map(f => `- ${f.name} (${f.mimeType})`).join('\n')}\n\n**IMPORTANT FILE HANDLING:**\n- When a user uploads a file, immediately acknowledge that you received it\n- Ask the user if they would like you to analyze the file\n- Wait for their permission before providing detailed analysis\n- Be conversational and friendly about file uploads\n- Example: "I see you've uploaded [filename]. Would you like me to take a look and tell you what I find?"`
              }] : []),
              ...(uploadedFiles || []).map(file => ({ fileData: { mimeType: file.mimeType, fileUri: file.uri } }))
            ]
          }
        },
        callbacks: {
          onopen: () => {
            setIsConnected(true);
            updateStatus('🔗 Connected – you can talk now.');
            
            // Save session data for continuity and dispatch session status event
            if (typeof window !== 'undefined') {
              saveSessionContinuity({
                sessionId: sessionIdRef.current,
                sessionStartTime: Date.now()
              })
              
              // Mark session as active for cross-tab communication
              sessionStorage.setItem('mae_session_active', 'true')
              sessionStorage.setItem('mae_session_id', sessionIdRef.current || 'unknown')
              
              // Add data attribute to document for cross-tab detection
              document.documentElement.setAttribute('data-mae-active', 'true')
              
              window.dispatchEvent(new CustomEvent('mae-session-status', {
                detail: { status: 'connected', sessionId: sessionIdRef.current }
              }));
            }
            
            // Don't send initial message here - we'll do it after microphone setup
          },
          onclose: (e: CloseEvent) => {
            console.log('[GeminiLive] Session closing, reason:', e.reason || 'normal');

            // Immediately reset connection states
            setIsConnected(false);
            setIsRecording(false);  // Ensure recording is stopped
            setIsModelSpeaking(false);  // Ensure model speaking is stopped
            
            // Clean up session markers for cross-tab communication
            if (typeof window !== 'undefined') {
              sessionStorage.removeItem('mae_session_active')
              sessionStorage.removeItem('mae_session_id')
              document.documentElement.removeAttribute('data-mae-active')
              
              window.dispatchEvent(new CustomEvent('mae-session-status', {
                detail: { status: 'disconnected' }
              }));
            }

            // Clean up session references
            sessionRef.current = undefined;
            greetingSentRef.current = false;  // reset for next session
            farewellInProgressRef.current = false;  // reset farewell state

            // Clear any pending session end timeout
            if (sessionEndTimeoutRef.current) {
              clearTimeout(sessionEndTimeoutRef.current);
              sessionEndTimeoutRef.current = undefined;
            }

            // Clean up audio resources
            try {
              audioWorkletNodeRef.current?.port.postMessage('stop');
              audioWorkletNodeRef.current?.disconnect();
              audioWorkletNodeRef.current = undefined;

              mediaStreamRef.current?.getTracks().forEach(t => t.stop());
              mediaStreamRef.current = undefined;

              // Stop any playing audio
              sourcesRef.current.forEach(src => src.stop());
              sourcesRef.current.clear();
              nextStartTimeRef.current = 0;
            } catch (cleanupError) {
              console.warn('[GeminiLive] Error during audio cleanup:', cleanupError);
            }

            // Set final status after a brief delay to ensure UI updates
            setTimeout(() => {
              updateStatus('Ready for your next question! Click the microphone to start.');
            }, 100);
          },
          onerror: (e: ErrorEvent) => {
            setIsConnected(false);
            console.error('🚨 WebSocket error details:', e);
            console.error('🚨 Error type:', e.type);
            console.error('🚨 Error message:', e.message);
            console.error('🚨 Error target:', e.target);

            // Provide more specific error messages
            if (e.message.includes('Permission denied') || e.message.includes('403')) {
              updateError('Permission denied: This might be due to API access restrictions or browser security policies. Try using HTTPS or check your API permissions.');
            } else if (e.message.includes('WebSocket')) {
              updateError(`WebSocket connection failed: ${e.message}. This might be due to network restrictions or browser security policies.`);
            } else {
              updateError(`Session error: ${e.message}`);
            }
          },

          // -------- audio from Gemini --------
          onmessage: async (msg: LiveServerMessage) => {
            const firstPart  = msg.serverContent?.modelTurn?.parts?.[0] as any;
            const inline     = firstPart?.inlineData;
            const speechFlag = ('speechState' in (firstPart ?? {}))
              ? firstPart.speechState === 'SPEECH_START'
              : false;
            setIsModelSpeaking(speechFlag);
            
            // Dispatch voice state change event
            if (typeof window !== 'undefined') {
              console.log('🎙️ Gemini Live - Dispatching voice state change:', { listening: false, speaking: speechFlag })
              window.dispatchEvent(new CustomEvent('mae-voice-state-change', {
                detail: { listening: false, speaking: speechFlag }
              }));
            }

            // Log grounding metadata when present
            if (msg.serverContent?.groundingMetadata) {
              console.log('🔍 Grounding metadata received:', msg.serverContent.groundingMetadata);
              
              // Extract and log grounding chunks for debugging
              const groundingChunks = msg.serverContent.groundingMetadata.groundingChunks || [];
              console.log('📚 Available grounding chunks:', groundingChunks.length);
              
              // Capture sources from grounding metadata
              const sources: { title: string; url: string }[] = [];
              groundingChunks.forEach((chunk: any, index: number) => {
                if (chunk.web) {
                  console.log(`🌐 Source ${index + 1}:`, {
                    title: chunk.web.title,
                    uri: chunk.web.uri
                  });
                  
                  sources.push({
                    title: chunk.web.title || chunk.web.uri,
                    url: chunk.web.uri
                  });
                }
              });
              
              // Store captured sources for later use in email function
              capturedSourcesRef.current = sources;
              console.log('📝 Captured sources for email:', sources);
            }
            
            // Check for any other source-related data in the message
            if (msg.serverContent && !msg.serverContent.groundingMetadata) {
              // Check for any search-related content
              if (JSON.stringify(msg).includes('search') || JSON.stringify(msg).includes('source') || JSON.stringify(msg).includes('url')) {
                console.log('🔍 POTENTIAL SOURCES FOUND IN MESSAGE:', JSON.stringify(msg, null, 2));
              }
            }

            // Check for turn completion during farewell
            if (msg.serverContent?.turnComplete && farewellInProgressRef.current) {
              console.log('[GeminiLive] Mae completed farewell turn, preparing to close session');
              if (sessionEndTimeoutRef.current) {
                clearTimeout(sessionEndTimeoutRef.current);
                sessionEndTimeoutRef.current = undefined;
              }
              // Close session after ensuring audio has finished playing
              setTimeout(() => {
                if (sessionRef.current) {
                  console.log('[GeminiLive] Closing session after Mae completed farewell');
                  sessionRef.current.close();
                }
              }, 5000); // Give 5 seconds for audio to finish playing
            }

            // Fallback: If Mae stops speaking during farewell but turn isn't complete yet
            else if (!speechFlag && farewellInProgressRef.current && firstPart?.speechState === 'SPEECH_END') {
              console.log('[GeminiLive] Mae stopped speaking during farewell, waiting for turn completion');
              // Don't close immediately, let the turn completion handler above take care of it
              // This prevents premature closing if Mae pauses during her farewell
            }

            if (msg.serverContent?.interrupted) {
              updateStatus('Speech interrupted.');
              sourcesRef.current.forEach(src => src.stop());
              sourcesRef.current.clear();
              nextStartTimeRef.current = 0;
              return;
            }

            if (inline?.data) {
              const outCtx = outputAudioContextRef.current!;
              const gain   = outCtx.createGain();
              gain.connect(outCtx.destination);

              nextStartTimeRef.current =
                Math.max(nextStartTimeRef.current, outCtx.currentTime);

              const buffer = await decodeAudioData(
                decode(inline.data), outCtx, 24_000, 1
              );
              const src = outCtx.createBufferSource();
              src.buffer = buffer;
              src.connect(gain);
              src.onended = () => {
                gain.disconnect();
                sourcesRef.current.delete(src);
              };

              src.start(nextStartTimeRef.current);
              nextStartTimeRef.current += buffer.duration;
              sourcesRef.current.add(src);
            }

            // Handle GoAway message (server is about to close connection)
            if (msg.goAway) {
              console.log('[GeminiLive] Server sending GoAway, time left:', msg.goAway.timeLeft);
              updateStatus(`Connection will close soon (${msg.goAway.timeLeft})`);
            }




            // Handle tool calls
            if (msg.toolCall?.functionCalls) {
              console.log('🔧 🚨 TOOL CALL RECEIVED - CHECKING FOR GOOGLE SEARCH 🚨');
              console.log('🔧 Full tool call object:', JSON.stringify(msg.toolCall, null, 2));
              
              const functionCalls = msg.toolCall.functionCalls;
              const functionResponses = [];
              
              // AGGRESSIVE LOGGING FOR GOOGLE SEARCH
              const hasGoogleSearch = msg.toolCall.functionCalls.some(call => call.name === 'googleSearch');
              if (hasGoogleSearch) {
                console.log('🔍 ✅ ✅ ✅ GOOGLE SEARCH TOOL CALL DETECTED - THIS IS CORRECT! ✅ ✅ ✅');
                const searchCall = msg.toolCall.functionCalls.find(call => call.name === 'googleSearch');
                console.log('🔍 Google Search parameters:', searchCall?.args);
                console.log('🔍 Search query:', searchCall?.args?.query);
                updateStatus('🔍 Google Search detected - getting current information...');
              }

              for (const call of functionCalls) {
                if (call.name === 'googleSearch') {
                  console.log('🔍 PROCESSING GOOGLE SEARCH CALL');
                  console.log('🔍 Search query:', call.args?.query);
                  // Google Search is handled automatically by the platform
                  // We just need to log it for debugging
                  functionResponses.push({
                    id: call.id,
                    name: call.name,
                    response: { success: true, message: 'Search initiated' }
                  });
                }
                else if (call.name === 'send_conversation_email') {
                  try {
                    if (!call.args) {
                      console.error('❌ Tool call missing arguments:', call);
                      updateError(`Tool call 'send_conversation_email' missing arguments.`);
                      continue;
                    }
                    console.log('📧 Executing email function with args:', call.args);
                    
                    // Always prefer grounding sources over LLM-generated sources
                    if (capturedSourcesRef.current && capturedSourcesRef.current.length > 0) {
                      console.log('🔧 OVERRIDING with captured grounding sources:', capturedSourcesRef.current);
                      console.log('🔧 Original LLM sources:', call.args.sources);
                      call.args.sources = capturedSourcesRef.current;
                      console.log('✅ Using grounding sources in email:', call.args.sources);
                    } else {
                      // Fallback to LLM-provided sources if no grounding sources
                      if (call.args.sources && (call.args.sources as any).length > 0) {
                        console.log('⚠️ Using LLM-provided sources (no grounding available):', call.args.sources);
                      } else {
                        console.error('❌ NO SOURCES available - neither grounding nor LLM-provided!');
                      }
                    }
                    
                    console.log('📊 Final sources count:', (call.args.sources as any)?.length || 0);
                    const result = await handleEmailConversation(call.args as any);
                    console.log('📧 Email function result:', result);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });

                    if (result?.success) {
                      updateStatus('Email sent successfully!');
                      // Trigger farewell after successful email send
                      setTimeout(() => triggerFarewellAndEnd(), 1000);
                    } else {
                      updateError(`Email failed: ${result?.error}`);
                    }
                  } catch (error) {
                    console.error('Error executing email function:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                    updateError('Failed to send email');
                  }
                }
                // Email confirmation function removed - using direct email sending only
                // Handle location-based function calls
                else if (call.name === 'find_pediatricians') {
                  try {
                    console.log('🔍 Finding pediatricians with args:', call.args);
                    const result = await handleFindPediatricians(call.args as any);
                    
                    // Automatically trigger map display if providers found
                    if (result.success && result.providers && result.providers.length > 0) {
                      console.log('🗺️ Auto-triggering map display for pediatricians');
                      triggerMapDisplay(result.providers, result.search_location, 'pediatricians');
                    }
                    
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                  } catch (error) {
                    console.error('Error finding pediatricians:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                else if (call.name === 'find_hospitals') {
                  try {
                    console.log('🏥 Finding hospitals with args:', call.args);
                    const result = await handleFindHospitals(call.args as any);
                    
                    // Automatically trigger map display if providers found
                    if (result.success && result.providers && result.providers.length > 0) {
                      console.log('🗺️ Auto-triggering map display for hospitals');
                      triggerMapDisplay(result.providers, result.search_location, 'hospitals');
                    }
                    
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                  } catch (error) {
                    console.error('Error finding hospitals:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                else if (call.name === 'find_urgent_care') {
                  try {
                    console.log('🚑 Finding urgent care with args:', call.args);
                    const result = await handleFindUrgentCare(call.args as any);
                    
                    // Automatically trigger map display if providers found
                    if (result.success && result.providers && result.providers.length > 0) {
                      console.log('🗺️ Auto-triggering map display for urgent care');
                      triggerMapDisplay(result.providers, result.search_location, 'urgent_care');
                    }
                    
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                  } catch (error) {
                    console.error('Error finding urgent care:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                else if (call.name === 'find_pharmacies') {
                  try {
                    console.log('💊 Finding pharmacies with args:', call.args);
                    const result = await handleFindPharmacies(call.args as any);
                    
                    // Automatically trigger map display if providers found
                    if (result.success && result.providers && result.providers.length > 0) {
                      console.log('🗺️ Auto-triggering map display for pharmacies');
                      triggerMapDisplay(result.providers, result.search_location, 'pharmacies');
                    }
                    
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                  } catch (error) {
                    console.error('Error finding pharmacies:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                // Handle map control functions
                else if (call.name === 'show_healthcare_map') {
                  try {
                    console.log('🗺️ Showing healthcare map with args:', call.args);
                    const { searchResults, searchQuery, searchType } = call.args as any;
                    
                    // Trigger map display via event system
                    triggerMapDisplay(searchResults || [], searchQuery || '', searchType || 'pediatricians');
                    
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: true,
                        message: `Map is now displayed showing ${searchResults?.length || 0} ${searchType} near ${searchQuery}`
                      }
                    });
                  } catch (error) {
                    console.error('Error showing map:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                else if (call.name === 'hide_healthcare_map') {
                  try {
                    console.log('🗺️ Hiding healthcare map');
                    
                    // Hide map via event system
                    hideMapDisplay();
                    
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: true,
                        message: 'Map has been hidden'
                      }
                    });
                  } catch (error) {
                    console.error('Error hiding map:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                // Handle onboarding function calls
                else if (call.name === 'collect_user_information') {
                  try {
                    console.log('📝 Collecting user information:', call.args);
                    const { handleCollectUserInfo } = await import('../lib/onboarding-function-tools');
                    const result = await handleCollectUserInfo(call.args as any);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                  } catch (error) {
                    console.error('Error collecting user info:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                else if (call.name === 'add_family_member') {
                  try {
                    console.log('👨‍👩‍👧‍👦 Adding family member:', call.args);
                    const { handleAddFamilyMember } = await import('../lib/onboarding-function-tools');
                    const result = await handleAddFamilyMember(call.args as any);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                  } catch (error) {
                    console.error('Error adding family member:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                else if (call.name === 'validate_onboarding_data') {
                  try {
                    console.log('✅ Validating onboarding data:', call.args);
                    const { handleValidateOnboardingData } = await import('../lib/onboarding-function-tools');
                    const result = await handleValidateOnboardingData(call.args as any);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                  } catch (error) {
                    console.error('Error validating onboarding data:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                else if (call.name === 'complete_onboarding') {
                  try {
                    console.log('🎉 Completing onboarding:', call.args);
                    const { handleCompleteOnboarding } = await import('../lib/onboarding-function-tools');
                    const result = await handleCompleteOnboarding(call.args as any);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                  } catch (error) {
                    console.error('Error completing onboarding:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                else if (call.name === 'get_onboarding_progress') {
                  try {
                    console.log('📊 Getting onboarding progress:', call.args);
                    const { handleGetOnboardingProgress } = await import('../lib/onboarding-function-tools');
                    const result = await handleGetOnboardingProgress(call.args as any);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                  } catch (error) {
                    console.error('Error getting onboarding progress:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                else if (call.name === 'update_onboarding_step') {
                  try {
                    console.log('🔄 Updating onboarding step:', call.args);
                    const { handleUpdateOnboardingStep } = await import('../lib/onboarding-function-tools');
                    const result = await handleUpdateOnboardingStep(call.args as any);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                  } catch (error) {
                    console.error('Error updating onboarding step:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                else if (call.name === 'navigate_to_onboarding') {
                  try {
                    console.log('🧭 Navigating to onboarding:', call.args);
                    const { handleNavigateToOnboarding } = await import('../lib/onboarding-function-tools');
                    const result = await handleNavigateToOnboarding(call.args as any);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                  } catch (error) {
                    console.error('Error navigating to onboarding:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                // Handle Mae user context function calls
                else if (call.name === 'get_user_context') {
                  try {
                    console.log('👤 Getting user context:', call.args);
                    const result = await handleGetUserContext(call.args as any);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                  } catch (error) {
                    console.error('Error getting user context:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                else if (call.name === 'check_authentication_status') {
                  try {
                    console.log('🔐 Checking authentication status:', call.args);
                    const result = await handleCheckAuthenticationStatus();
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                  } catch (error) {
                    console.error('Error checking authentication status:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                // Handle Mae duplicate prevention function calls
                else if (call.name === 'check_family_member_duplicate') {
                  try {
                    console.log('🔍 Checking for family member duplicate:', call.args);
                    const result = await handleCheckFamilyMemberDuplicate(call.args as any);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                  } catch (error) {
                    console.error('Error checking family member duplicate:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                else if (call.name === 'resolve_family_member_duplicate') {
                  try {
                    console.log('🔧 Resolving family member duplicate:', call.args);
                    const result = await handleResolveFamilyMemberDuplicate(call.args as any);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                  } catch (error) {
                    console.error('Error resolving family member duplicate:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                // Handle Mae family management function calls
                else if (call.name === 'add_family_member_with_uuid') {
                  try {
                    console.log('👨‍👩‍👧‍👦 Adding family member with UUID:', call.args);
                    const result = await handleAddFamilyMemberWithUuid(call.args as any);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                  } catch (error) {
                    console.error('Error adding family member with UUID:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                else if (call.name === 'get_family_members_by_uuid') {
                  try {
                    console.log('👨‍👩‍👧‍👦 Getting family members by UUID:', call.args);
                    const result = await handleGetFamilyMembersByUuid(call.args as any);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                  } catch (error) {
                    console.error('Error getting family members by UUID:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                // Handle Mae database function calls
                else if (call.name === 'save_care_log_entry') {
                  try {
                    console.log('📝 Saving care log entry:', call.args);
                    const result = await handleSaveCareLogEntry(call.args as any);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                  } catch (error) {
                    console.error('Error saving care log entry:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                else if (call.name === 'save_to_library') {
                  try {
                    console.log('📚 Saving to library:', call.args);
                    const result = await handleSaveToLibrary(call.args as any);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                  } catch (error) {
                    console.error('Error saving to library:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                // Handle Mae contextual insights function calls
                else if (call.name === 'get_seasonal_health_insights') {
                  try {
                    console.log('🌟 Getting seasonal health insights:', call.args);
                    const result = await handleGetSeasonalHealthInsights(call.args as any);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                  } catch (error) {
                    console.error('Error getting seasonal health insights:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                else if (call.name === 'get_developmental_milestones') {
                  try {
                    console.log('🌟 Getting developmental milestones:', call.args);
                    const result = await handleGetDevelopmentalMilestones(call.args as any);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                  } catch (error) {
                    console.error('Error getting developmental milestones:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
                else if (call.name === 'get_personalized_health_tips') {
                  try {
                    console.log('🌟 Getting personalized health tips:', call.args);
                    const result = await handleGetPersonalizedHealthTips(call.args as any);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    });
                  } catch (error) {
                    console.error('Error getting personalized health tips:', error);
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    });
                  }
                }
              }
              // Send function responses back to Gemini
              if (functionResponses.length > 0) {
                console.log('📤 Sending function responses to Gemini:', functionResponses);
                sessionRef.current?.sendToolResponse({
                  functionResponses: functionResponses as any
                });
                console.log('✅ Function responses sent');
              }
            }
          }
        }
      });
      updateStatus('✅ Session ready.');
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : String(e);
      console.error('🚨 Session initialization failed:', e);

      // Check for specific permission errors
      if (errorMessage.includes('Permission denied') || errorMessage.includes('403') || errorMessage.includes('PERMISSION_DENIED')) {
        updateError('Permission denied: Your API key may not have access to Gemini Live API. Please check your Google AI Studio settings and ensure Live API access is enabled.');
      } else if (errorMessage.includes('quota') || errorMessage.includes('QUOTA_EXCEEDED')) {
        updateError('API quota exceeded. Please check your billing and usage limits in Google AI Studio.');
      } else if (errorMessage.includes('404') || errorMessage.includes('NOT_FOUND')) {
        updateError('Gemini Live model not found. The Live API may not be available in your region or for your account.');
      } else {
        updateError(`Failed to init session: ${errorMessage}`);
      }
    }
  }, [updateStatus, updateError]);

  const initClient = useCallback(async () => {
    updateStatus('Creating client…');
    try {
      // Get API key securely from server-side proxy
      const response = await fetch('/api/gemini-proxy', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      // Robustly parse JSON or surface raw text for HTML/errors
      let payload: any = null
      try {
        payload = await response.json()
      } catch (parseErr) {
        const text = await response.text()
        if (text?.trim().startsWith('<!DOCTYPE')) {
          updateError('Failed to initialize Gemini client: Received HTML (possible redirect). Ensure /api/gemini-proxy is public and not behind auth.');
        } else {
          updateError(`Failed to initialize Gemini client: ${text?.slice(0, 160) || 'Unknown non-JSON response'}`);
        }
        return
      }

      if (!response.ok || !payload?.success) {
        updateError(`Failed to initialize Gemini client: ${payload?.error || 'Unknown error'}`)
        return
      }

      const { apiKey, sessionToken } = payload;
      
      if (!apiKey) {
        updateError('Gemini API key is missing. Please check your environment variables.');
        return;
      }

      if (!clientRef.current) {
        clientRef.current = new GoogleGenAI({ apiKey });
      }

      if (typeof window === 'undefined') {
        updateError('Audio functionality requires a browser environment.');
        return;
      }

      const AudioCtor =
        window.AudioContext || (window as any).webkitAudioContext;

      if (!AudioCtor) {
        updateError('Web Audio API is not supported in this browser.');
        return;
      }

      if (!outputAudioContextRef.current) {
        outputAudioContextRef.current = new AudioCtor({ sampleRate: 24_000 });
      }
      if (!inputAudioContextRef.current) {
        inputAudioContextRef.current  = new AudioCtor({ sampleRate: 16_000 });
      }
      await initSession();
    } catch (e) {
      updateError(`Client init failed: ${
        e instanceof Error ? e.message : String(e)
      }`);
    }
  }, [initSession, updateStatus, updateError]);

  const reinitWithFiles = useCallback(async () => {
    console.log('🔄 Reinitializing Mae session with uploaded files...');
    await initSession(true);
  }, [initSession]);

  //------------------------------------------------------------
  // 2. Recording helpers
  //------------------------------------------------------------
  const stopRecording = useCallback(() => {
    if (!isRecording) return;

    updateStatus('Stopping recording…');
    setIsRecording(false);

    audioWorkletNodeRef.current?.port.postMessage('stop');
    audioWorkletNodeRef.current?.disconnect();
    audioWorkletNodeRef.current = undefined;

    mediaStreamRef.current?.getTracks().forEach(t => t.stop());
    mediaStreamRef.current = undefined;

    // Clear any pending session end timeout
    if (sessionEndTimeoutRef.current) {
      clearTimeout(sessionEndTimeoutRef.current);
      sessionEndTimeoutRef.current = undefined;
    }

    updateStatus('Recording stopped.');
    
    // Dispatch voice state change event
    if (typeof window !== 'undefined') {
      console.log('🎙️ Gemini Live - Dispatching voice state change (recording stopped):', { listening: false, speaking: false })
      window.dispatchEvent(new CustomEvent('mae-voice-state-change', {
        detail: { listening: false, speaking: false }
      }));
    }
  }, [isRecording, updateStatus]);

  // Send file separately like the working extension
  const sendFile = useCallback((base64Data: string, mimeType: string) => {
    if (!sessionRef.current) {
      console.error('❌ No active session to send file');
      return;
    }

    const msg = {
      clientContent: {
        turns: [
          {
            role: 'user',
            parts: [
              {
                inlineData: {
                  mimeType: mimeType,
                  data: base64Data
                }
              }
            ]
          }
        ],
        turnComplete: true
      }
    };

    try {
      sessionRef.current.sendClientContent(msg.clientContent);
      console.log('📁 File sent to Mae:', mimeType);
    } catch (err) {
      console.error('❌ Failed to send file:', err);
    }
  }, []);

  const startRecording = useCallback(async (initialMessage?: string, fileData?: {uri: string, name: string, mimeType: string}) => {
    if (isRecording) return updateStatus('Already recording.');

    // Initialize client if not ready
    if (!clientRef.current) {
      await initClient();
    }

    // Create new session if needed
    if (!sessionRef.current) {
      await initSession();
    }

    if (!sessionRef.current) return updateError('Session not ready.');

    await inputAudioContextRef.current?.resume();
    await outputAudioContextRef.current?.resume();
    updateStatus('Requesting microphone access…');

    try {
      if (!navigator.mediaDevices?.getUserMedia) {
        updateError('Microphone access is not supported in this browser.');
        return;
      }

      try {
        mediaStreamRef.current = await navigator.mediaDevices.getUserMedia({
          audio: {
            sampleRate: 16_000,
            channelCount: 1,
            echoCancellation: true,
            noiseSuppression: false, // Reduce aggressive noise suppression that might cut off Mae
            autoGainControl: false   // Disable AGC to prevent volume fluctuations
          }
        });
      } catch (micError: any) {
        console.error('🚨 Microphone access error:', micError);
        console.error('🚨 Error name:', micError.name);
        console.error('🚨 Error message:', micError.message);

        if (micError.name === 'NotAllowedError' || micError.name === 'PermissionDeniedError') {
          updateError('Microphone permission denied. Please allow microphone access and try again. If using HTTP, try HTTPS instead.');
          return;
        } else if (micError.name === 'NotFoundError') {
          updateError('No microphone found. Please connect a microphone and try again.');
          return;
        } else if (micError.name === 'NotReadableError') {
          updateError('Microphone is being used by another application. Please close other apps and try again.');
          return;
        } else if (micError.name === 'OverconstrainedError') {
          updateError('Microphone does not support the required audio format.');
          return;
        } else {
          updateError(`Microphone access failed: ${micError.message || 'Unknown error'}`);
          return;
        }
      }
      
      updateStatus('Mic access granted.');

      const ac = inputAudioContextRef.current!;
      if (!ac.audioWorklet) {
        updateError('AudioWorklet is not supported in this browser.');
        return;
      }

      const blob = new Blob([audioProcessor], { type: 'application/javascript' });
      const moduleURL = URL.createObjectURL(blob);

      try {
        await ac.audioWorklet.addModule(moduleURL);
        updateStatus('AudioWorklet loaded.');
      } catch (workletError) {
        updateError(`Failed to load AudioWorklet: ${
          workletError instanceof Error ? workletError.message : String(workletError)
        }`);
        return;
      } finally {
        URL.revokeObjectURL(moduleURL);
      }

      const srcNode = ac.createMediaStreamSource(mediaStreamRef.current);
      const wkNode  = new AudioWorkletNode(ac, 'audio-processor', {
        numberOfInputs: 1,
        numberOfOutputs: 1,
        outputChannelCount: [1]
      });
      audioWorkletNodeRef.current = wkNode;

      wkNode.port.onmessage = (evt) => {
        try {
          const pcm = evt.data as Float32Array;
          
          // Calculate audio level for visualization
          if (pcm && pcm.length > 0) {
            const sum = pcm.reduce((acc, val) => acc + Math.abs(val), 0);
            const avgLevel = sum / pcm.length;
            const normalizedLevel = Math.min(1, avgLevel * 15); // Increase amplification for better visualization
            
            // Dispatch audio level event for visualization - lower threshold for better feedback
            if (typeof window !== 'undefined' && normalizedLevel > 0.005) {
              window.dispatchEvent(new CustomEvent('mae-audio-level', {
                detail: { level: normalizedLevel }
              }));
            }
            
            // Remove debug logging to improve performance
          }
          
          // Voice activity detection - use lower threshold for better pickup
          const hasSignal = pcm?.some(s => Math.abs(s) > 0.001); // Lower threshold to pick up speech
          if (!hasSignal) return;
          sessionRef.current?.sendRealtimeInput({ audio: createBlob(pcm) });
        } catch (err) {
          updateError(`Streaming error: ${
            err instanceof Error ? err.message : String(err)
          }`);
        }
      };

      srcNode.connect(wkNode);      // no echo → don't connect to destination
      setIsRecording(true);
      updateStatus('🔴 Recording… Mae is responding.');
      
      // Dispatch voice state change event
      if (typeof window !== 'undefined') {
        console.log('🎙️ Gemini Live - Dispatching voice state change (recording started):', { listening: true, speaking: false })
        window.dispatchEvent(new CustomEvent('mae-voice-state-change', {
          detail: { listening: true, speaking: false }
        }));
      }

      // Send initial message immediately for fast response
      if (initialMessage) {
        sendInitialUserTurn(initialMessage, fileData);
      } else {
        sendInitialUserTurn();
      }
    } catch (err) {
      updateError(`Start recording failed: ${
        err instanceof Error ? err.message : String(err)
      }`);
      stopRecording();
    }
  }, [isRecording, stopRecording, updateStatus, updateError]);

  //------------------------------------------------------------
  // Manual session ending
  //------------------------------------------------------------
  const endSession = useCallback(() => {
    if (sessionRef.current) {
      console.log('[GeminiLive] Manually ending session');
      sessionRef.current.close();
    } else {
      // If no session exists, just reset the UI state
      console.log('[GeminiLive] No active session, resetting UI state');
      setIsConnected(false);
      setIsRecording(false);
      setIsModelSpeaking(false);
      updateStatus('Ready for your next question! Click the microphone to start.');
      
      // Dispatch voice state change event
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('mae-voice-state-change', {
          detail: { listening: false, speaking: false }
        }));
      }
    }
  }, [updateStatus]);

  //------------------------------------------------------------
  // 3.  public API
  //------------------------------------------------------------
  return {
    status,
    error,
    isRecording,
    isModelSpeaking,
    isConnected,
    initClient,     // call once on‑mount
    startRecording,
    stopRecording,
    sendFile,       // send files separately like working extension
    endSession,     // manually end the session
    reinitWithFiles // reinitialize session with new files
  };
}
