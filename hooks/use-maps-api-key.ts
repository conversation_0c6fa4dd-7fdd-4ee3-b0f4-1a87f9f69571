"use client"

import { useState, useEffect } from 'react'

export function useMapsApiKey() {
  const [apiKey, setApiKey] = useState<string>('')
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string>('')

  useEffect(() => {
    async function fetchApiKey() {
      try {
        const response = await fetch('/api/maps-config')
        
        if (!response.ok) {
          const errorData = await response.json()
          setError(errorData.error || 'Failed to load Maps API key')
          return
        }

        const data = await response.json()
        setApiKey(data.apiKey || '')
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load Maps API key')
      } finally {
        setLoading(false)
      }
    }

    fetchApiKey()
  }, [])

  return { apiKey, loading, error }
}