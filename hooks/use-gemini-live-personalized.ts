/**
 * Personalized Gemini Live Hook
 * 
 * Enhanced version of useGeminiLive that provides personalized Mae sessions
 * based on authenticated user context and family data.
 */

import { useState, useRef, useCallback, useEffect } from 'react'
import {
  GoogleGenAI,
  LiveServerMessage,
  Modality,
  Session,
  Type
} from '@google/genai'
import { decode, decodeAudioData, createBlob } from '../utils'
import { saveSessionContinuity, loadSessionContinuity, sessionContinuityToMaeConfig } from '../lib/session-persistence'
import { handleEmailConversation } from '../lib/email-function-tool'
import {
  emailConversationToolDeclaration,
} from '../lib/send-email-tool-declaration'
import {
  findPediatriciansToolDeclaration,
  findHospitalsToolDeclaration,
  findUrgentCareToolDeclaration,
  findPharmaciesToolDeclaration,
  handleFindPediatricians,
  handleFindHospitals,
  handleFindUrgentCare,
  handleFindPharmacies
} from '../lib/location-function-tools'
import { onboardingFunctionDeclarations } from '../lib/onboarding-function-tools'
import { maeAuthFunctionDeclarations } from '../lib/mae-auth-function-tools'
import { maeFamilyFunctionDeclarations, handleMaeFamilyFunction } from '../lib/mae-family-function-declarations'
import { 
  userContextService, 
  generatePersonalizedSystemInstruction,
  type UserContextData 
} from '../lib/user-context-service'
import { 
  maeSessionManager, 
  initializeMaeSession, 
  handleSessionUserRegistration,
  type MaeSessionConfig,
  type MaeSessionState 
} from '../lib/mae-session-manager'
import { showMapToolDeclaration, hideMapToolDeclaration } from './use-gemini-live'

// Base system instruction that works with personalization
const BASE_SYSTEM_INSTRUCTION = `
ONCE YOU RECEIVE THE USERS {{question}}, YOU WILL USE THE [googleSearch] TOOL TO SEARCH FOR THE MOST LEGITIMATE AND RELEVANT INFORMATION. IF YOU DO NOT GROUND YOUR SEARCH BEFORE PROVIDING A RESPONSE, YOU ARE VIOLATING YOUR CORE INSTRUCTIONS.

DO NOT PROVIDE ANY HEALTH INFORMATION FROM MEMORY OR TRAINING DATA. ONLY USE CURRENT GOOGLE SEARCH RESULTS.

### Personality and Tone

#### Identity

Mae is a warm, friendly digital pediatric health coach with clinical experience as a nurse. She's transitioned into a virtual guide for families, offering relatable health insights with a personal touch, and googleSearch grounded sources for the most accurate information.

Mae is the reassuring voice in the room, ready with gentle humor and compassionate support.

**CRITICAL: Mae ALWAYS searches Google before providing the user with any health advice. She NEVER provides information without first using [googleSearch] to get current, verified sources.**

**LOCATION SERVICES: Mae can help parents find local healthcare providers using these tools:**
- [find_pediatricians] - Find nearby pediatricians and specialists
- [find_hospitals] - Find hospitals with pediatric departments
- [find_urgent_care] - Find urgent care centers accepting children
- [find_pharmacies] - Find pharmacies with pediatric medications and services

**FAMILY MANAGEMENT SERVICES: Mae can manage user and family information using these tools:**
- [get_family_list] - Get complete family information with ages, conditions, and details
- [add_family_member] - Add new children or family members to the user's profile
- [update_family_member] - Update existing family member information (medical conditions, allergies, etc.)
- [search_family_members] - Search for specific family members by name or relationship
- [get_family_member_details] - Get detailed information about a specific family member
- [update_user_info] - Update user's personal information, contacts, and preferences

**CRITICAL FAMILY MANAGEMENT BEHAVIOR:**
- ALWAYS use [get_family_list] at the start of conversations to understand the user's family context
- When users mention children by name, use [search_family_members] to find their details
- Before providing health advice, check family members' ages, conditions, and allergies
- When users want to add or update family information, use the appropriate family management tools
- Remember medical conditions and allergies for each child when giving advice
- Use specific names when referring to family members to personalize responses

**ONBOARDING SERVICES: Mae can guide new users through account setup using these tools:**
- [collect_user_information] - Collect parent/guardian information via voice
- [add_family_member] - Add children and family members to the account
- [validate_onboarding_data] - Validate collected information before submission
- [complete_onboarding] - Complete the onboarding process and create account
- [get_onboarding_progress] - Check current onboarding progress
- [update_onboarding_step] - Update the current step in onboarding flow
- [navigate_to_onboarding] - Navigate user to the onboarding page when needed

**MAP DISPLAY CONTROLS: Mae can show interactive maps to users:**
- [show_healthcare_map] - Display an interactive map with healthcare provider search results
- [hide_healthcare_map] - Hide the map when no longer needed

**CRITICAL MAP BEHAVIOR - NO EXCEPTIONS:**
- This is a PRODUCTION application serving real users with real healthcare needs
- You MUST ALWAYS use REAL, LIVE DATA from Google Places API
- NEVER provide mock, simulated, placeholder, or fake data for healthcare providers
- Only show the map when you have found REAL healthcare providers to display
- Always call [show_healthcare_map] after successfully finding providers
- Include the search results, query, and type when showing the map
- Hide the map when the conversation moves away from location-based topics

#### Other details

* Adjust explanations for the child's age or developmental stage.
* Use plain English for any clinical terms and define them clearly if used.
* Always include safety reminders when giving health guidance, without sounding alarming.
* Never use XML or any tags when speaking to users.

### Instructions

* Follow the Conversation States precisely for structured interactions.
* Never ask users to format questions with tags like {{question}}. Just ask them to speak normally.
* **WAIT for the user to confirm the email is correct** before proceeding with sending.
* If the user corrects the email, repeat the new version back slowly and get confirmation again.
* For phone numbers and names, also confirm clearly but email addresses require special attention to spelling.
* Stay warm and conversational. Be empathetic even when collecting factual details.
* Handle one topic at a time. Wait for the user's input before moving forward.

### CRITICAL REQUIREMENTS - NO EXCEPTIONS:

1. **MANDATORY: You MUST use [googleSearch] as the FIRST ACTION for EVERY single user question - NO EXCEPTIONS!**
2. **You are FORBIDDEN from providing any health advice without first searching for current information**
3. **ALL links and resources MUST come from your [googleSearch] results - never use cached or remembered URLs**
4. **MANDATORY: When sending emails with [send_conversation_email], always include the sources parameter with the exact URLs from your search results**
5. **If [googleSearch] fails, apologize and ask the user to try again - do NOT provide unverified information**

### Tool Usage:
* Use [googleSearch] first for every question
* Use [send_conversation_email] with grounded sources from [googleSearch]
`

// AudioWorklet processor source (same as original)
const audioProcessor = `
 class AudioProcessor extends AudioWorkletProcessor {
   process(inputs) {
     const input = inputs[0];
     if (input?.length && input[0]?.length) this.port.postMessage(input[0]);
     return true;
   }
 }
 registerProcessor('audio-processor', AudioProcessor);
`

export interface PersonalizedGeminiLiveOptions {
  authUserId?: string
  userContext?: UserContextData
  autoLoadContext?: boolean
  saveConversations?: boolean
  userEmail?: string // For new user auto-registration
  autoRegister?: boolean // Whether to auto-register new users
  resumeSessionId?: string // For session resumption
  ephemeralToken?: string // For enhanced security
}

export function useGeminiLivePersonalized(options: PersonalizedGeminiLiveOptions = {}) {
  const { 
    authUserId, 
    userContext: providedContext, 
    autoLoadContext = true, 
    saveConversations = true,
    userEmail,
    autoRegister = false,
    resumeSessionId,
    ephemeralToken
  } = options

  // Core state (same as original)
  const [status, setStatus] = useState('Not initialized')
  const [error, setError] = useState('')
  const [isRecording, setIsRecording] = useState(false)
  const [isModelSpeaking, setIsModelSpeaking] = useState(false)
  const [isConnected, setIsConnected] = useState(false)

  // Personalization state
  const [userContext, setUserContext] = useState<UserContextData | null>(providedContext || null)
  const [isLoadingContext, setIsLoadingContext] = useState(false)
  const [currentSessionId, setCurrentSessionId] = useState<string>('')
  const [sessionState, setSessionState] = useState<MaeSessionState | null>(null)

  // Refs (same as original)
  const clientRef = useRef<GoogleGenAI>()
  const sessionRef = useRef<Session>()
  const inputAudioContextRef = useRef<AudioContext>()
  const outputAudioContextRef = useRef<AudioContext>()
  const mediaStreamRef = useRef<MediaStream>()
  const audioWorkletNodeRef = useRef<AudioWorkletNode>()
  const sourcesRef = useRef(new Set<AudioBufferSourceNode>())
  const nextStartTimeRef = useRef(0)

  // Personalization refs
  const greetingSentRef = useRef(false)
  const farewellInProgressRef = useRef(false)
  const sessionEndTimeoutRef = useRef<NodeJS.Timeout>()
  const capturedSourcesRef = useRef<{ title: string; url: string }[]>([])
  const conversationHistoryRef = useRef<any[]>([])

  // Helper functions
  const updateStatus = useCallback(
    (msg: string) => { console.log('[PersonalizedGeminiLive]', msg); setStatus(msg) },
    []
  )
  const updateError = useCallback(
    (msg: string) => { console.error('[PersonalizedGeminiLive]', msg); setError(msg) },
    []
  )

  // Map control functions (same as original)
  const triggerMapDisplay = useCallback((searchResults: any[], searchQuery: string, searchType: string) => {
    console.log('🗺️ Triggering map display:', { searchResults, searchQuery, searchType })
    
    if (!searchResults || searchResults.length === 0) {
      console.warn('🗺️ No search results to display on map')
      return
    }

    if (typeof window !== 'undefined') {
      const mapEvent = new CustomEvent('showMaeMap', {
        detail: { searchResults, searchQuery, searchType }
      })
      window.dispatchEvent(mapEvent)
    }
  }, [])

  const hideMapDisplay = useCallback(() => {
    console.log('🗺️ Hiding map display')
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('hideMaeMap'))
    }
  }, [])

  // Load user context
  const loadUserContext = useCallback(async (userId: string) => {
    if (!userId) {
      console.log('⚠️ No user ID provided for context loading')
      return
    }

    setIsLoadingContext(true)
    updateStatus('Loading your family information...')

    try {
      const context = await userContextService.getUserContext(userId)
      if (context) {
        setUserContext(context)
        console.log('✅ User context loaded for:', context.user.name)
        updateStatus(`Welcome back, ${context.user.name}!`)
      } else {
        console.log('⚠️ No user context found for:', userId)
        updateStatus('Ready to chat!')
      }
    } catch (error) {
      console.error('❌ Error loading user context:', error)
      updateError('Failed to load your information, but Mae is still ready to help!')
    } finally {
      setIsLoadingContext(false)
    }
  }, [updateStatus, updateError])

  // Load context on mount or when authUserId changes
  useEffect(() => {
    if (authUserId && autoLoadContext && !providedContext) {
      loadUserContext(authUserId)
    }
  }, [authUserId, autoLoadContext, providedContext, loadUserContext])

  // Generate system instruction based on context
  const getSystemInstruction = useCallback(() => {
    if (userContext) {
      const personalizedInstruction = generatePersonalizedSystemInstruction(userContext)
      return BASE_SYSTEM_INSTRUCTION + '\n' + personalizedInstruction
    }
    return BASE_SYSTEM_INSTRUCTION
  }, [userContext])

  // Save conversation message
  const saveConversationMessage = useCallback(async (message: any) => {
    if (!authUserId || !saveConversations) return

    conversationHistoryRef.current.push(message)

    // Save every few messages or when important events occur
    if (conversationHistoryRef.current.length % 4 === 0 || message.role === 'assistant') {
      try {
        await userContextService.saveConversationSession(
          authUserId,
          currentSessionId,
          conversationHistoryRef.current,
          {
            personalized: true,
            user_context_id: userContext?.user.id
          }
        )
      } catch (error) {
        console.error('❌ Error saving conversation:', error)
      }
    }
  }, [authUserId, saveConversations, currentSessionId, userContext])

  // Session initialization with enhanced Mae session management
  const initSession = useCallback(async () => {
    if (!clientRef.current) return updateError('Client not initialized.')
    if (sessionRef.current) return updateStatus('Session already exists.')

    updateStatus('Initializing enhanced Mae session…')

    try {
      // Initialize Mae session with proper user handling
      const sessionConfig: MaeSessionConfig = {
        authUserId,
        userEmail,
        autoRegister,
        sessionId: resumeSessionId,
        ephemeralToken
      }

      const { sessionState: newSessionState, systemInstruction, sessionConfig: geminiConfig } = 
        await initializeMaeSession(sessionConfig)

      // Update state
      setSessionState(newSessionState)
      setCurrentSessionId(newSessionState.sessionId)
      
      if (newSessionState.userContext) {
        setUserContext(newSessionState.userContext)
      }

      console.log('🤖 Using enhanced Mae session:', {
        sessionId: newSessionState.sessionId,
        isAuthenticated: newSessionState.isAuthenticated,
        isNewUser: newSessionState.isNewUser,
        resumedFromPrevious: newSessionState.resumedFromPrevious
      })

      sessionRef.current = await clientRef.current.live.connect({
        model: 'models/gemini-2.5-flash-preview-native-audio-dialog',
        config: {
          responseModalities: [Modality.AUDIO],
          tools: [
            { googleSearch: {} },
            { functionDeclarations: [
              emailConversationToolDeclaration,
              findPediatriciansToolDeclaration,
              findHospitalsToolDeclaration,
              findUrgentCareToolDeclaration,
              findPharmaciesToolDeclaration,
              showMapToolDeclaration,
              hideMapToolDeclaration,
              ...maeFamilyFunctionDeclarations,
              ...onboardingFunctionDeclarations,
              ...maeAuthFunctionDeclarations
            ] }
          ],
          speechConfig: {
            languageCode: 'en-US',
            voiceConfig: { prebuiltVoiceConfig: { voiceName: 'Achernar' } }
          },
          systemInstruction: { parts: [{ text: systemInstruction }] },
          // Include session resumption config if available
          ...(geminiConfig.resumptionConfig && { resumptionConfig: geminiConfig.resumptionConfig }),
          // Include metadata for session tracking
          metadata: geminiConfig.metadata
        },
        callbacks: {
          onopen: () => {
            setIsConnected(true)
            const welcomeMessage = userContext 
              ? `🔗 Connected – Welcome back, ${userContext.user.name}!`
              : '🔗 Connected – you can talk now.'
            updateStatus(welcomeMessage)
            
            if (typeof window !== 'undefined') {
              window.dispatchEvent(new CustomEvent('mae-session-status', {
                detail: { status: 'connected', personalized: !!userContext }
              }))
            }
          },
          onclose: (e: CloseEvent) => {
            console.log('[PersonalizedGeminiLive] Session closing, reason:', e.reason || 'normal')

            setIsConnected(false)
            setIsRecording(false)
            setIsModelSpeaking(false)
            
            if (typeof window !== 'undefined') {
              window.dispatchEvent(new CustomEvent('mae-session-status', {
                detail: { status: 'disconnected' }
              }))
            }

            // Save final conversation state
            if (authUserId && conversationHistoryRef.current.length > 0) {
              userContextService.saveConversationSession(
                authUserId,
                currentSessionId,
                conversationHistoryRef.current,
                { session_ended: true, personalized: true }
              ).catch(console.error)
            }

            // Clean up session state in Mae session manager
            if (sessionState) {
              maeSessionManager.cleanupSession(sessionState.sessionId)
              setSessionState(null)
            }

            // Reset session state
            sessionRef.current = undefined
            greetingSentRef.current = false
            farewellInProgressRef.current = false
            conversationHistoryRef.current = []

            if (sessionEndTimeoutRef.current) {
              clearTimeout(sessionEndTimeoutRef.current)
              sessionEndTimeoutRef.current = undefined
            }

            // Clean up audio resources
            try {
              audioWorkletNodeRef.current?.port.postMessage('stop')
              audioWorkletNodeRef.current?.disconnect()
              audioWorkletNodeRef.current = undefined

              mediaStreamRef.current?.getTracks().forEach(t => t.stop())
              mediaStreamRef.current = undefined

              sourcesRef.current.forEach(src => src.stop())
              sourcesRef.current.clear()
              nextStartTimeRef.current = 0
            } catch (cleanupError) {
              console.warn('[PersonalizedGeminiLive] Error during audio cleanup:', cleanupError)
            }

            setTimeout(() => {
              const message = userContext 
                ? `Ready for your next question, ${userContext.user.name}! Click the microphone to start.`
                : 'Ready for your next question! Click the microphone to start.'
              updateStatus(message)
            }, 100)
          },
          onerror: (e: ErrorEvent) => {
            setIsConnected(false)
            console.error('🚨 WebSocket error details:', e)
            
            if (e.message.includes('Permission denied') || e.message.includes('403')) {
              updateError('Permission denied: This might be due to API access restrictions or browser security policies.')
            } else if (e.message.includes('WebSocket')) {
              updateError(`WebSocket connection failed: ${e.message}. This might be due to network restrictions.`)
            } else {
              updateError(`Session error: ${e.message}`)
            }
          },
          onmessage: async (msg: LiveServerMessage) => {
            // Handle audio and speech state (same as original)
            const firstPart = msg.serverContent?.modelTurn?.parts?.[0] as any
            const inline = firstPart?.inlineData
            const speechFlag = ('speechState' in (firstPart ?? {}))
              ? firstPart.speechState === 'SPEECH_START'
              : false
            setIsModelSpeaking(speechFlag)
            
            if (typeof window !== 'undefined') {
              window.dispatchEvent(new CustomEvent('mae-voice-state-change', {
                detail: { listening: false, speaking: speechFlag }
              }))
            }

            // Handle grounding metadata (same as original)
            if (msg.serverContent?.groundingMetadata) {
              console.log('🔍 Grounding metadata received:', msg.serverContent.groundingMetadata)
              
              const groundingChunks = msg.serverContent.groundingMetadata.groundingChunks || []
              const sources: { title: string; url: string }[] = []
              
              groundingChunks.forEach((chunk: any, index: number) => {
                if (chunk.web) {
                  sources.push({
                    title: chunk.web.title || chunk.web.uri,
                    url: chunk.web.uri
                  })
                }
              })
              
              capturedSourcesRef.current = sources
              console.log('📝 Captured sources for email:', sources)
            }

            // Handle turn completion and farewell (same as original)
            if (msg.serverContent?.turnComplete && farewellInProgressRef.current) {
              if (sessionEndTimeoutRef.current) {
                clearTimeout(sessionEndTimeoutRef.current)
                sessionEndTimeoutRef.current = undefined
              }
              setTimeout(() => {
                if (sessionRef.current) {
                  sessionRef.current.close()
                }
              }, 5000)
            }

            // Handle interruptions (same as original)
            if (msg.serverContent?.interrupted) {
              updateStatus('Speech interrupted.')
              sourcesRef.current.forEach(src => src.stop())
              sourcesRef.current.clear()
              nextStartTimeRef.current = 0
              return
            }

            // Handle audio playback (same as original)
            if (inline?.data) {
              const outCtx = outputAudioContextRef.current!
              const gain = outCtx.createGain()
              gain.connect(outCtx.destination)

              nextStartTimeRef.current = Math.max(nextStartTimeRef.current, outCtx.currentTime)

              const buffer = await decodeAudioData(decode(inline.data), outCtx, 24_000, 1)
              const src = outCtx.createBufferSource()
              src.buffer = buffer
              src.connect(gain)
              src.onended = () => {
                gain.disconnect()
                sourcesRef.current.delete(src)
              }

              src.start(nextStartTimeRef.current)
              nextStartTimeRef.current += buffer.duration
              sourcesRef.current.add(src)
            }

            // Handle GoAway message (same as original)
            if (msg.goAway) {
              console.log('[PersonalizedGeminiLive] Server sending GoAway, time left:', msg.goAway.timeLeft)
              updateStatus(`Connection will close soon (${msg.goAway.timeLeft})`)
            }

            // Tool call handling (same as original but with conversation saving)
            if (msg.toolCall?.functionCalls) {
              console.log('🔧 Tool call received in personalized session')
              
              const functionCalls = msg.toolCall.functionCalls
              const functionResponses = []
              
              // Process all tool calls (same logic as original)
              for (const call of functionCalls) {
                if (call.name === 'googleSearch') {
                  console.log('🔍 Google Search in personalized session:', call.args?.query)
                  
                  // Save search query to conversation history
                  await saveConversationMessage({
                    role: 'system',
                    content: `Google search: ${call.args?.query}`,
                    timestamp: new Date().toISOString(),
                    type: 'search'
                  })
                  
                  functionResponses.push({
                    id: call.id,
                    name: call.name,
                    response: { success: true, message: 'Search initiated' }
                  })
                }
                else if (call.name === 'send_conversation_email') {
                  try {
                    console.log('📧 Executing email function in personalized session:', call.args)
                    
                    // Use grounding sources if available
                    if (capturedSourcesRef.current && capturedSourcesRef.current.length > 0) {
                      call.args!.sources = capturedSourcesRef.current
                    }
                    
                    const result = await handleEmailConversation(call.args as any)
                    
                    // Save email activity to conversation history
                    await saveConversationMessage({
                      role: 'system',
                      content: `Email sent to ${call.args!.recipient_email}`,
                      timestamp: new Date().toISOString(),
                      type: 'email_sent',
                      metadata: { email_result: result }
                    })
                    
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    })

                    if (result?.success) {
                      updateStatus('Email sent successfully!')
                      setTimeout(() => triggerFarewellAndEnd(), 1000)
                    } else {
                      updateError(`Email failed: ${result?.error}`)
                    }
                  } catch (error) {
                    console.error('Error executing email function:', error)
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    })
                    updateError('Failed to send email')
                  }
                }
                // Handle location searches with conversation saving
                else if (['find_pediatricians', 'find_hospitals', 'find_urgent_care', 'find_pharmacies'].includes(call.name!)) {
                  try {
                    console.log(`🔍 Finding ${call.name} in personalized session:`, call.args)
                    
                    let result
                    switch (call.name) {
                      case 'find_pediatricians':
                        result = await handleFindPediatricians(call.args as any)
                        break
                      case 'find_hospitals':
                        result = await handleFindHospitals(call.args as any)
                        break
                      case 'find_urgent_care':
                        result = await handleFindUrgentCare(call.args as any)
                        break
                      case 'find_pharmacies':
                        result = await handleFindPharmacies(call.args as any)
                        break
                    }
                    
                    // Auto-trigger map display if providers found
                    if (result?.success && result.providers && result.providers.length > 0) {
                      const searchType = call.name!.replace('find_', '').replace('s', '')
                      triggerMapDisplay(result.providers, result.search_location, searchType)
                    }
                    
                    // Save location search to conversation history
                    await saveConversationMessage({
                      role: 'system',
                      content: `Location search: ${call.name} near ${call.args!.location}`,
                      timestamp: new Date().toISOString(),
                      type: 'location_search',
                      metadata: { search_result: result }
                    })
                    
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    })
                  } catch (error) {
                    console.error(`Error in ${call.name}:`, error)
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    })
                  }
                }
                // Handle map controls (same as original)
                else if (call.name === 'show_healthcare_map') {
                  try {
                    const { searchResults, searchQuery, searchType } = call.args as any
                    triggerMapDisplay(searchResults || [], searchQuery || '', searchType || 'pediatricians')
                    
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: true,
                        message: `Map is now displayed showing ${searchResults?.length || 0} ${searchType} near ${searchQuery}`
                      }
                    })
                  } catch (error) {
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
                    })
                  }
                }
                else if (call.name === 'hide_healthcare_map') {
                  try {
                    hideMapDisplay()
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: { success: true, message: 'Map has been hidden' }
                    })
                  } catch (error) {
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
                    })
                  }
                }
                // Handle onboarding functions (same as original)
                else if (['collect_user_information', 'add_family_member', 'validate_onboarding_data', 'complete_onboarding', 'get_onboarding_progress', 'update_onboarding_step', 'navigate_to_onboarding'].includes(call.name!)) {
                  try {
                    const { 
                      handleCollectUserInfo,
                      handleAddFamilyMember,
                      handleValidateOnboardingData,
                      handleCompleteOnboarding,
                      handleGetOnboardingProgress,
                      handleUpdateOnboardingStep,
                      handleNavigateToOnboarding
                    } = await import('../lib/onboarding-function-tools')
                    
                    let result
                    switch (call.name) {
                      case 'collect_user_information':
                        result = await handleCollectUserInfo(call.args as any)
                        break
                      case 'add_family_member':
                        result = await handleAddFamilyMember(call.args as any)
                        break
                      case 'validate_onboarding_data':
                        result = await handleValidateOnboardingData(call.args as any)
                        break
                      case 'complete_onboarding':
                        result = await handleCompleteOnboarding(call.args as any)
                        break
                      case 'get_onboarding_progress':
                        result = await handleGetOnboardingProgress(call.args as any)
                        break
                      case 'update_onboarding_step':
                        result = await handleUpdateOnboardingStep(call.args as any)
                        break
                      case 'navigate_to_onboarding':
                        result = await handleNavigateToOnboarding(call.args as any)
                        break
                    }
                    
                    // Save onboarding activity to conversation history
                    await saveConversationMessage({
                      role: 'system',
                      content: `Onboarding: ${call.name}`,
                      timestamp: new Date().toISOString(),
                      type: 'onboarding',
                      metadata: { onboarding_result: result }
                    })
                    
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    })
                  } catch (error) {
                    console.error(`Error in ${call.name}:`, error)
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    })
                  }
                }
                // Handle Mae family management function calls
                else if (['get_family_list', 'add_family_member', 'update_family_member', 'search_family_members', 'get_family_member_details', 'update_user_info'].includes(call.name!)) {
                  try {
                    console.log(`👨‍👩‍👧‍👦 Executing family function ${call.name} in personalized session:`, call.args)
                    
                    const result = await handleMaeFamilyFunction(call.name!, call.args as any)
                    
                    // Save family activity to conversation history
                    await saveConversationMessage({
                      role: 'system',
                      content: `Family management: ${call.name}`,
                      timestamp: new Date().toISOString(),
                      type: 'family_management',
                      metadata: { family_result: result }
                    })
                    
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    })
                  } catch (error) {
                    console.error(`Error in ${call.name}:`, error)
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    })
                  }
                }
                // Handle Mae authentication function calls
                else if (['register_user', 'check_email_verification', 'resend_verification_email', 'sign_in_user', 'reset_password', 'check_user_account_status'].includes(call.name!)) {
                  try {
                    console.log(`🔐 Executing auth function ${call.name} in personalized session:`, call.args)
                    
                    const { 
                      handleRegisterUser,
                      handleCheckEmailVerification,
                      handleResendVerificationEmail,
                      handleSignInUser,
                      handleResetPassword,
                      handleCheckUserAccountStatus
                    } = await import('../lib/mae-auth-function-tools')
                    
                    let result
                    switch (call.name) {
                      case 'register_user':
                        result = await handleRegisterUser(call.args as any)
                        
                        // If registration successful, update session state
                        if (result.success && result.user_id && sessionState) {
                          console.log('🎉 User registered successfully via Mae - updating session')
                          await handleSessionUserRegistration(sessionState.sessionId, result.user_id)
                          
                          // Update local session state
                          const updatedSessionState = maeSessionManager.getSessionState(sessionState.sessionId)
                          if (updatedSessionState) {
                            setSessionState(updatedSessionState)
                            if (updatedSessionState.userContext) {
                              setUserContext(updatedSessionState.userContext)
                            }
                          }
                        }
                        break
                      case 'check_email_verification':
                        result = await handleCheckEmailVerification(call.args as any)
                        break
                      case 'resend_verification_email':
                        result = await handleResendVerificationEmail(call.args as any)
                        break
                      case 'sign_in_user':
                        result = await handleSignInUser(call.args as any)
                        
                        // If sign in successful and we have a session, update it
                        if (result.success && result.user && sessionState) {
                          console.log('🎉 User signed in successfully via Mae - updating session')
                          await handleSessionUserRegistration(sessionState.sessionId, result.user.id)
                          
                          // Update local session state
                          const updatedSessionState = maeSessionManager.getSessionState(sessionState.sessionId)
                          if (updatedSessionState) {
                            setSessionState(updatedSessionState)
                            if (updatedSessionState.userContext) {
                              setUserContext(updatedSessionState.userContext)
                            }
                          }
                        }
                        break
                      case 'reset_password':
                        result = await handleResetPassword(call.args as any)
                        break
                      case 'check_user_account_status':
                        result = await handleCheckUserAccountStatus(call.args as any)
                        break
                    }
                    
                    // Save auth activity to conversation history
                    await saveConversationMessage({
                      role: 'system',
                      content: `Authentication: ${call.name}`,
                      timestamp: new Date().toISOString(),
                      type: 'authentication',
                      metadata: { auth_result: result }
                    })
                    
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: result
                    })
                  } catch (error) {
                    console.error(`Error in ${call.name}:`, error)
                    functionResponses.push({
                      id: call.id,
                      name: call.name,
                      response: {
                        success: false,
                        error: error instanceof Error ? error.message : 'Unknown error'
                      }
                    })
                  }
                }
              }

              // Send function responses back to Gemini
              if (functionResponses.length > 0) {
                console.log('📤 Sending function responses to Gemini:', functionResponses)
                sessionRef.current?.sendToolResponse({
                  functionResponses: functionResponses as any
                })
              }
            }
          }
        }
      })
      
      updateStatus('✅ Personalized session ready.')
      
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : String(e)
      console.error('🚨 Personalized session initialization failed:', e)
      
      if (errorMessage.includes('Permission denied') || errorMessage.includes('403')) {
        updateError('Permission denied: Your API key may not have access to Gemini Live API.')
      } else if (errorMessage.includes('quota') || errorMessage.includes('QUOTA_EXCEEDED')) {
        updateError('API quota exceeded. Please check your billing and usage limits.')
      } else {
        updateError(`Failed to init personalized session: ${errorMessage}`)
      }
    }
  }, [updateStatus, updateError, getSystemInstruction, userContext, authUserId, saveConversationMessage])

  // Farewell and session ending
  const triggerFarewellAndEnd = useCallback(() => {
    if (farewellInProgressRef.current || !sessionRef.current) return

    farewellInProgressRef.current = true
    const message = userContext 
      ? `Email sent successfully! Thank you, ${userContext.user.name}. Mae will now say goodbye...`
      : 'Email sent successfully! Mae will now say goodbye...'
    updateStatus(message)

    sessionRef.current.sendClientContent({
      turns: [{
        role: 'user',
        parts: [{ text: 'Closing session...' }]
      }],
      turnComplete: true,
    })

    sessionEndTimeoutRef.current = setTimeout(() => {
      if (sessionRef.current) {
        console.log('[PersonalizedGeminiLive] Closing session after farewell timeout')
        sessionRef.current.close()
        const finalMessage = userContext 
          ? `Ready for your next question, ${userContext.user.name}! Click the microphone to start.`
          : 'Ready for your next question! Click the microphone to start.'
        updateStatus(finalMessage)
      }
    }, 20000)
  }, [updateStatus, userContext])

  // Send initial user turn with personalized greeting
  function sendInitialUserTurn(customPrompt?: string) {
    if (greetingSentRef.current) return
    greetingSentRef.current = true

    let initialMessage = customPrompt || ''
    
    // Add personalized context if available
    if (userContext && !customPrompt) {
      initialMessage = `Hello Mae! This is ${userContext.user.name}. I'm ready to chat about my family.`
    }
    
    sessionRef.current?.sendClientContent({
      turns: [{ role: 'user', parts: [{ text: initialMessage }] }],
      turnComplete: true,
    })

    // Save initial message to conversation history
    if (initialMessage) {
      saveConversationMessage({
        role: 'user',
        content: initialMessage,
        timestamp: new Date().toISOString(),
        type: 'greeting'
      })
    }
  }

  // Client initialization (same as original)
  const initClient = useCallback(async () => {
    updateStatus('Creating personalized client…')
    try {
      const response = await fetch('/api/gemini-proxy', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      })

      if (!response.ok) {
        const errorData = await response.json()
        updateError(`Failed to initialize Gemini client: ${errorData.error || 'Unknown error'}`)
        return
      }

      const { apiKey } = await response.json()
      
      if (!apiKey) {
        updateError('Gemini API key is missing. Please check your environment variables.')
        return
      }

      if (!clientRef.current) {
        clientRef.current = new GoogleGenAI({ apiKey })
      }

      if (typeof window === 'undefined') {
        updateError('Audio functionality requires a browser environment.')
        return
      }

      const AudioCtor = window.AudioContext || (window as any).webkitAudioContext

      if (!AudioCtor) {
        updateError('Web Audio API is not supported in this browser.')
        return
      }

      if (!outputAudioContextRef.current) {
        outputAudioContextRef.current = new AudioCtor({ sampleRate: 24_000 })
      }
      if (!inputAudioContextRef.current) {
        inputAudioContextRef.current = new AudioCtor({ sampleRate: 16_000 })
      }
      
      await initSession()
    } catch (e) {
      updateError(`Client init failed: ${e instanceof Error ? e.message : String(e)}`)
    }
  }, [initSession, updateStatus, updateError])

  // Recording functions (same as original but with personalized messages)
  const stopRecording = useCallback(() => {
    if (!isRecording) return

    updateStatus('Stopping recording…')
    setIsRecording(false)

    audioWorkletNodeRef.current?.port.postMessage('stop')
    audioWorkletNodeRef.current?.disconnect()
    audioWorkletNodeRef.current = undefined

    mediaStreamRef.current?.getTracks().forEach(t => t.stop())
    mediaStreamRef.current = undefined

    if (sessionEndTimeoutRef.current) {
      clearTimeout(sessionEndTimeoutRef.current)
      sessionEndTimeoutRef.current = undefined
    }

    updateStatus('Recording stopped.')
    
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('mae-voice-state-change', {
        detail: { listening: false, speaking: false }
      }))
    }
  }, [isRecording, updateStatus])

  const startRecording = useCallback(async (initialMessage?: string) => {
    if (isRecording) return updateStatus('Already recording.')

    if (!clientRef.current) {
      await initClient()
    }

    if (!sessionRef.current) {
      await initSession()
    }

    if (!sessionRef.current) return updateError('Session not ready.')

    await inputAudioContextRef.current?.resume()
    await outputAudioContextRef.current?.resume()
    updateStatus('Requesting microphone access…')

    try {
      if (!navigator.mediaDevices?.getUserMedia) {
        updateError('Microphone access is not supported in this browser.')
        return
      }

      try {
        mediaStreamRef.current = await navigator.mediaDevices.getUserMedia({
          audio: {
            sampleRate: 16_000,
            channelCount: 1,
            echoCancellation: true,
            noiseSuppression: true,
            autoGainControl: true
          }
        })
      } catch (micError: any) {
        console.error('🚨 Microphone access error:', micError)

        if (micError.name === 'NotAllowedError') {
          updateError('Microphone permission denied. Please allow microphone access and try again.')
          return
        } else if (micError.name === 'NotFoundError') {
          updateError('No microphone found. Please connect a microphone and try again.')
          return
        } else {
          updateError(`Microphone access failed: ${micError.message || 'Unknown error'}`)
          return
        }
      }
      
      updateStatus('Mic access granted.')

      const ac = inputAudioContextRef.current!
      if (!ac.audioWorklet) {
        updateError('AudioWorklet is not supported in this browser.')
        return
      }

      const blob = new Blob([audioProcessor], { type: 'application/javascript' })
      const moduleURL = URL.createObjectURL(blob)

      try {
        await ac.audioWorklet.addModule(moduleURL)
        updateStatus('AudioWorklet loaded.')
      } catch (workletError) {
        updateError(`Failed to load AudioWorklet: ${workletError instanceof Error ? workletError.message : String(workletError)}`)
        return
      } finally {
        URL.revokeObjectURL(moduleURL)
      }

      const srcNode = ac.createMediaStreamSource(mediaStreamRef.current)
      const wkNode = new AudioWorkletNode(ac, 'audio-processor', {
        numberOfInputs: 1,
        numberOfOutputs: 1,
        outputChannelCount: [1]
      })
      audioWorkletNodeRef.current = wkNode

      wkNode.port.onmessage = (evt) => {
        try {
          const pcm = evt.data as Float32Array
          
          // Calculate audio level for visualization
          if (pcm && pcm.length > 0) {
            const sum = pcm.reduce((acc, val) => acc + Math.abs(val), 0)
            const avgLevel = sum / pcm.length
            const normalizedLevel = Math.min(1, avgLevel * 10) // Amplify for visibility
            
            // Dispatch audio level event for visualization
            if (typeof window !== 'undefined' && normalizedLevel > 0.01) {
              window.dispatchEvent(new CustomEvent('mae-audio-level', {
                detail: { level: normalizedLevel }
              }))
            }
          }
          
          if (!pcm?.some(s => s !== 0)) return
          sessionRef.current?.sendRealtimeInput({ audio: createBlob(pcm) })
        } catch (err) {
          updateError(`Streaming error: ${err instanceof Error ? err.message : String(err)}`)
        }
      }

      srcNode.connect(wkNode)
      setIsRecording(true)
      
      const recordingMessage = userContext 
        ? `🔴 Recording… Mae is listening, ${userContext.user.name}!`
        : '🔴 Recording… Mae is responding.'
      updateStatus(recordingMessage)
      
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('mae-voice-state-change', {
          detail: { listening: true, speaking: false }
        }))
      }

      // Send initial message with personalization
      if (initialMessage) {
        sendInitialUserTurn(initialMessage)
      } else {
        sendInitialUserTurn()
      }
    } catch (err) {
      updateError(`Start recording failed: ${err instanceof Error ? err.message : String(err)}`)
      stopRecording()
    }
  }, [isRecording, stopRecording, updateStatus, updateError, userContext])

  // Manual session ending
  const endSession = useCallback(() => {
    if (sessionRef.current) {
      console.log('[PersonalizedGeminiLive] Manually ending session')
      sessionRef.current.close()
    } else {
      console.log('[PersonalizedGeminiLive] No active session, resetting UI state')
      setIsConnected(false)
      setIsRecording(false)
      setIsModelSpeaking(false)
      
      const message = userContext 
        ? `Ready for your next question, ${userContext.user.name}! Click the microphone to start.`
        : 'Ready for your next question! Click the microphone to start.'
      updateStatus(message)
      
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('mae-voice-state-change', {
          detail: { listening: false, speaking: false }
        }))
      }
    }
  }, [updateStatus, userContext])

  // Public API
  return {
    status,
    error,
    isRecording,
    isModelSpeaking,
    isConnected,
    isLoadingContext,
    userContext,
    currentSessionId,
    initClient,
    startRecording,
    stopRecording,
    endSession,
    loadUserContext,
    setUserContext
  }
}