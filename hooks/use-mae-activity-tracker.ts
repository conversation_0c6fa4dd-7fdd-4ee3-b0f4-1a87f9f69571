"use client"

import { useState, useEffect, useCallback } from 'react'

export interface MaeActivity {
  id: string
  type: 'tool' | 'function' | 'processing' | 'listening' | 'speaking' | 'navigation'
  name: string
  description: string
  status: 'active' | 'completed' | 'pending' | 'error'
  timestamp: number
  duration?: number
  details?: any
}

export interface MaeActivityState {
  activities: MaeActivity[]
  currentActivity: MaeActivity | null
  isListening: boolean
  isSpeaking: boolean
  isProcessing: boolean
  sessionActive: boolean
  currentTool: string | null
  connectionStatus: 'connected' | 'connecting' | 'disconnected'
}

export function useMaeActivityTracker() {
  const [state, setState] = useState<MaeActivityState>({
    activities: [],
    currentActivity: null,
    isListening: false,
    isSpeaking: false,
    isProcessing: false,
    sessionActive: false,
    currentTool: null,
    connectionStatus: 'disconnected'
  })

  // Add new activity
  const addActivity = useCallback((activity: Omit<MaeActivity, 'id' | 'timestamp'>) => {
    const newActivity: MaeActivity = {
      ...activity,
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      timestamp: Date.now()
    }

    setState(prev => ({
      ...prev,
      activities: [newActivity, ...prev.activities.slice(0, 9)], // Keep last 10
      currentActivity: activity.status === 'active' ? newActivity : prev.currentActivity
    }))

    return newActivity.id
  }, [])

  // Update activity status
  const updateActivity = useCallback((id: string, updates: Partial<MaeActivity>) => {
    setState(prev => ({
      ...prev,
      activities: prev.activities.map(activity => 
        activity.id === id ? { ...activity, ...updates } : activity
      ),
      currentActivity: prev.currentActivity?.id === id ? 
        { ...prev.currentActivity, ...updates } : prev.currentActivity
    }))
  }, [])

  // Set current tool
  const setCurrentTool = useCallback((tool: string | null) => {
    setState(prev => ({ ...prev, currentTool: tool }))
  }, [])

  // Set voice states
  const setVoiceState = useCallback((listening: boolean, speaking: boolean) => {
    console.log('🎛️ Activity Tracker - Setting voice state:', { listening, speaking })
    setState(prev => ({ 
      ...prev, 
      isListening: listening, 
      isSpeaking: speaking,
      sessionActive: listening || speaking || prev.sessionActive
    }))
  }, [])

  // Set processing state
  const setProcessing = useCallback((processing: boolean) => {
    setState(prev => ({ ...prev, isProcessing: processing }))
  }, [])

  // Set connection status
  const setConnectionStatus = useCallback((status: 'connected' | 'connecting' | 'disconnected') => {
    setState(prev => ({ 
      ...prev, 
      connectionStatus: status,
      sessionActive: status === 'connected'
    }))
  }, [])

  // Clear activities
  const clearActivities = useCallback(() => {
    setState(prev => ({
      ...prev,
      activities: [],
      currentActivity: null
    }))
  }, [])

  // Listen for Mae-related events
  useEffect(() => {
    // Listen for AG-UI events to track Mae's function calls
    const handleAGUIEvent = (event: CustomEvent) => {
      const { payload, requestId } = event.detail || {}
      const eventType = event.type

      // Map AG-UI events to activities
      const activityMap: { [key: string]: { name: string; description: string; type: MaeActivity['type'] } } = {
        'fillUserForm': {
          name: 'Fill User Form',
          description: `Filling ${payload?.field || 'user field'} with voice input`,
          type: 'function'
        },
        'addFamilyMember': {
          name: 'Add Family Member',
          description: `Adding family member: ${payload?.name || 'New member'}`,
          type: 'function'
        },
        'validateForm': {
          name: 'Validate Form',
          description: `Validating ${payload?.formType || 'form'} data`,
          type: 'function'
        },
        'updateOnboardingProgress': {
          name: 'Update Progress',
          description: `Moving to step: ${payload?.step || 'next step'}`,
          type: 'navigation'
        },
        'submitUserForm': {
          name: 'Submit Form',
          description: 'Submitting user information to database',
          type: 'function'
        }
      }

      const activityInfo = activityMap[eventType]
      if (activityInfo) {
        addActivity({
          ...activityInfo,
          status: 'active',
          details: { payload, requestId }
        })
      }
    }

    // Listen for Mae function tool calls
    const handleFunctionCall = (event: CustomEvent) => {
      const { functionName, parameters, status } = event.detail

      const functionDescriptions: { [key: string]: string } = {
        'collect_user_information': 'Collecting user information via voice',
        'add_family_member': 'Adding new family member',
        'validate_onboarding_data': 'Validating onboarding data',
        'complete_onboarding': 'Completing onboarding process',
        'get_onboarding_progress': 'Getting current progress',
        'update_onboarding_step': 'Updating onboarding step',
        'navigate_to_onboarding': 'Navigating to onboarding page'
      }

      addActivity({
        type: 'function',
        name: functionName,
        description: functionDescriptions[functionName] || `Executing ${functionName}`,
        status: status || 'active',
        details: parameters
      })

      setCurrentTool(functionName)
    }

    // Listen for Mae tool activities
    const handleToolActivity = (event: CustomEvent) => {
      const { toolName, status, description } = event.detail
      
      addActivity({
        type: 'tool',
        name: toolName,
        description: description || `Using ${toolName}`,
        status: status || 'active'
      })

      if (status === 'active') {
        setCurrentTool(toolName)
      } else if (status === 'completed' || status === 'error') {
        setCurrentTool(null)
      }
    }

    // Listen for Mae processing updates
    const handleProcessingUpdate = (event: CustomEvent) => {
      const { message, status } = event.detail
      
      addActivity({
        type: 'processing',
        name: 'Processing',
        description: message,
        status: status || 'active'
      })

      setProcessing(status === 'active')
    }

    // Listen for Mae voice state changes
    const handleVoiceStateChange = (event: CustomEvent) => {
      const { listening, speaking } = event.detail
      console.log('🎤 Voice state change event received:', { listening, speaking })
      setVoiceState(listening || false, speaking || false)

      if (listening) {
        console.log('🎤 Mae is now listening - adding activity')
        addActivity({
          type: 'listening',
          name: 'Listening',
          description: 'Mae is listening for voice input',
          status: 'active'
        })
      } else if (speaking) {
        console.log('🗣️ Mae is now speaking - adding activity')
        addActivity({
          type: 'speaking',
          name: 'Speaking',
          description: 'Mae is responding',
          status: 'active'
        })
      } else {
        console.log('🤫 Mae is now idle - no voice activity')
      }
    }

    // Listen for Mae session status
    const handleSessionStatus = (event: CustomEvent) => {
      const { status } = event.detail
      setConnectionStatus(status)

      if (status === 'connected') {
        addActivity({
          type: 'processing',
          name: 'Session Started',
          description: 'Mae session initialized and ready',
          status: 'completed'
        })
      }
    }

    // Register event listeners
    if (typeof window !== 'undefined') {
      // AG-UI Events
      window.addEventListener('fillUserForm', handleAGUIEvent as EventListener)
      window.addEventListener('addFamilyMember', handleAGUIEvent as EventListener)
      window.addEventListener('validateForm', handleAGUIEvent as EventListener)
      window.addEventListener('updateOnboardingProgress', handleAGUIEvent as EventListener)
      window.addEventListener('submitUserForm', handleAGUIEvent as EventListener)

      // Mae Events
      window.addEventListener('mae-function-call', handleFunctionCall as EventListener)
      window.addEventListener('mae-tool-activity', handleToolActivity as EventListener)
      window.addEventListener('mae-processing-update', handleProcessingUpdate as EventListener)
      window.addEventListener('mae-voice-state-change', handleVoiceStateChange as EventListener)
      window.addEventListener('mae-session-status', handleSessionStatus as EventListener)
    }

    return () => {
      if (typeof window !== 'undefined') {
        // Remove AG-UI Events
        window.removeEventListener('fillUserForm', handleAGUIEvent as EventListener)
        window.removeEventListener('addFamilyMember', handleAGUIEvent as EventListener)
        window.removeEventListener('validateForm', handleAGUIEvent as EventListener)
        window.removeEventListener('updateOnboardingProgress', handleAGUIEvent as EventListener)
        window.removeEventListener('submitUserForm', handleAGUIEvent as EventListener)

        // Remove Mae Events
        window.removeEventListener('mae-function-call', handleFunctionCall as EventListener)
        window.removeEventListener('mae-tool-activity', handleToolActivity as EventListener)
        window.removeEventListener('mae-processing-update', handleProcessingUpdate as EventListener)
        window.removeEventListener('mae-voice-state-change', handleVoiceStateChange as EventListener)
        window.removeEventListener('mae-session-status', handleSessionStatus as EventListener)
      }
    }
  }, [addActivity, setCurrentTool, setVoiceState, setProcessing, setConnectionStatus])

  // Initialize with session continued activity if session storage indicates it
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const sessionContinued = sessionStorage.getItem('mae-continue-session')
      if (sessionContinued === 'true') {
        setConnectionStatus('connected')
        addActivity({
          type: 'processing',
          name: 'Session Continued',
          description: 'Mae session continued from landing page',
          status: 'completed'
        })
        // Clear the session storage to prevent re-initialization
        sessionStorage.removeItem('mae-continue-session')
      }
    }
  }, []) // Empty dependency array to run only once

  return {
    ...state,
    addActivity,
    updateActivity,
    setCurrentTool,
    setVoiceState,
    setProcessing,
    setConnectionStatus,
    clearActivities
  }
}