-- Migration 002: Fix UUID Relationships and User Context
-- This migration fixes the database structure to ensure proper UUID relationships
-- for <PERSON> to access user context correctly

BEGIN;

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 1. Add missing auth_user_id column to users table
-- This links users table with Supabase auth.users
ALTER TABLE public.users 
ADD COLUMN IF NOT EXISTS auth_user_id UUID;

-- Create index for auth_user_id lookups
CREATE INDEX IF NOT EXISTS idx_users_auth_user_id ON public.users(auth_user_id);

-- 2. Create or update audio_chat_sessions table with proper UUID relationships
CREATE TABLE IF NOT EXISTS public.audio_chat_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    session_id VARCHAR(255) UNIQUE NOT NULL,
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    user_email VARCHAR(255) NOT NULL,
    history JSONB DEFAULT '[]',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- If the table already exists, add missing columns
DO $$
BEGIN
    -- Add user_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'audio_chat_sessions' 
                   AND column_name = 'user_id') THEN
        ALTER TABLE public.audio_chat_sessions 
        ADD COLUMN user_id UUID REFERENCES public.users(id) ON DELETE CASCADE;
    END IF;
    
    -- Add session_id column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'audio_chat_sessions' 
                   AND column_name = 'session_id') THEN
        ALTER TABLE public.audio_chat_sessions 
        ADD COLUMN session_id VARCHAR(255) UNIQUE;
    END IF;
    
    -- Add user_email column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'audio_chat_sessions' 
                   AND column_name = 'user_email') THEN
        ALTER TABLE public.audio_chat_sessions 
        ADD COLUMN user_email VARCHAR(255);
    END IF;
    
    -- Add history column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'audio_chat_sessions' 
                   AND column_name = 'history') THEN
        ALTER TABLE public.audio_chat_sessions 
        ADD COLUMN history JSONB DEFAULT '[]';
    END IF;
    
    -- Add metadata column if it doesn't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'audio_chat_sessions' 
                   AND column_name = 'metadata') THEN
        ALTER TABLE public.audio_chat_sessions 
        ADD COLUMN metadata JSONB DEFAULT '{}';
    END IF;
END $$;

-- 3. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_audio_chat_sessions_user_id ON public.audio_chat_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_audio_chat_sessions_user_email ON public.audio_chat_sessions(user_email);
CREATE INDEX IF NOT EXISTS idx_audio_chat_sessions_session_id ON public.audio_chat_sessions(session_id);
CREATE INDEX IF NOT EXISTS idx_audio_chat_sessions_created_at ON public.audio_chat_sessions(created_at);

-- 4. Add updated_at trigger for audio_chat_sessions
CREATE TRIGGER IF NOT EXISTS update_audio_chat_sessions_updated_at
    BEFORE UPDATE ON public.audio_chat_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 5. Create user_profiles table if it doesn't exist (for additional profile data)
CREATE TABLE IF NOT EXISTS public.user_profiles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    display_name VARCHAR(100),
    avatar_url VARCHAR(255),
    bio TEXT,
    location VARCHAR(100),
    website VARCHAR(255),
    social_links JSONB DEFAULT '{}',
    privacy_settings JSONB DEFAULT '{}',
    notification_preferences JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for user_profiles
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON public.user_profiles(user_id);

-- Add updated_at trigger for user_profiles
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_user_profiles_updated_at') THEN
        CREATE TRIGGER update_user_profiles_updated_at
            BEFORE UPDATE ON public.user_profiles
            FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- 6. Enable Row Level Security for new tables
ALTER TABLE public.audio_chat_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;

-- 7. Create RLS policies for audio_chat_sessions
DO $$
BEGIN
    -- Users can view their own chat sessions
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE schemaname = 'public' AND tablename = 'audio_chat_sessions' AND policyname = 'Users can view their own chat sessions') THEN
        CREATE POLICY "Users can view their own chat sessions" ON public.audio_chat_sessions
            FOR SELECT USING (user_id = auth.uid());
    END IF;
    
    -- Users can manage their own chat sessions
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE schemaname = 'public' AND tablename = 'audio_chat_sessions' AND policyname = 'Users can manage their own chat sessions') THEN
        CREATE POLICY "Users can manage their own chat sessions" ON public.audio_chat_sessions
            FOR ALL USING (user_id = auth.uid());
    END IF;
    
    -- Service role can access all chat sessions (for Mae functionality)
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE schemaname = 'public' AND tablename = 'audio_chat_sessions' AND policyname = 'Service role can access all chat sessions') THEN
        CREATE POLICY "Service role can access all chat sessions" ON public.audio_chat_sessions
            FOR ALL USING (auth.role() = 'service_role');
    END IF;
END $$;

-- 8. Create RLS policies for user_profiles
DO $$
BEGIN
    -- Users can view their own profile
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE schemaname = 'public' AND tablename = 'user_profiles' AND policyname = 'Users can view their own profile') THEN
        CREATE POLICY "Users can view their own profile" ON public.user_profiles
            FOR SELECT USING (user_id = auth.uid());
    END IF;
    
    -- Users can manage their own profile
    IF NOT EXISTS (SELECT 1 FROM pg_policies WHERE schemaname = 'public' AND tablename = 'user_profiles' AND policyname = 'Users can manage their own profile') THEN
        CREATE POLICY "Users can manage their own profile" ON public.user_profiles
            FOR ALL USING (user_id = auth.uid());
    END IF;
END $$;

-- 9. Update existing audio_chat_sessions to link with users table
-- This will populate the user_id column based on user_email
UPDATE public.audio_chat_sessions 
SET user_id = u.id 
FROM public.users u 
WHERE public.audio_chat_sessions.user_email = u.email 
AND public.audio_chat_sessions.user_id IS NULL;

-- 10. Create function to get comprehensive user context for Mae
CREATE OR REPLACE FUNCTION get_user_context_for_mae(user_email_param TEXT)
RETURNS TABLE (
    user_id UUID,
    auth_user_id UUID,
    email VARCHAR(255),
    name VARCHAR(100),
    role VARCHAR(50),
    phone VARCHAR(20),
    zip VARCHAR(10),
    onboarding_completed BOOLEAN,
    onboarding_step VARCHAR(50),
    family_members_count INTEGER,
    recent_sessions_count INTEGER,
    last_session_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.id as user_id,
        u.auth_user_id,
        u.email,
        u.name,
        u.role,
        u.phone,
        u.zip,
        u.onboarding_completed,
        u.onboarding_step,
        COALESCE(fm.family_count, 0)::INTEGER as family_members_count,
        COALESCE(acs.session_count, 0)::INTEGER as recent_sessions_count,
        acs.last_session_date,
        u.created_at
    FROM public.users u
    LEFT JOIN (
        SELECT 
            fm_inner.user_id, 
            COUNT(*) as family_count
        FROM public.family_members fm_inner
        GROUP BY fm_inner.user_id
    ) fm ON u.id = fm.user_id
    LEFT JOIN (
        SELECT 
            acs_inner.user_id,
            COUNT(*) as session_count,
            MAX(acs_inner.created_at) as last_session_date
        FROM public.audio_chat_sessions acs_inner
        WHERE acs_inner.created_at > NOW() - INTERVAL '30 days'
        GROUP BY acs_inner.user_id
    ) acs ON u.id = acs.user_id
    WHERE u.email = user_email_param;
END;
$$ LANGUAGE plpgsql;

-- 11. Create function to link auth users with profile users
CREATE OR REPLACE FUNCTION link_auth_user_to_profile(
    auth_user_uuid UUID,
    user_email_param TEXT
)
RETURNS BOOLEAN AS $$
DECLARE
    user_exists BOOLEAN;
BEGIN
    -- Check if user exists
    SELECT EXISTS(SELECT 1 FROM public.users WHERE email = user_email_param) INTO user_exists;
    
    IF user_exists THEN
        -- Update the auth_user_id
        UPDATE public.users 
        SET auth_user_id = auth_user_uuid 
        WHERE email = user_email_param 
        AND auth_user_id IS NULL;
        
        RETURN TRUE;
    ELSE
        RETURN FALSE;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 12. Add comments for documentation
COMMENT ON TABLE public.audio_chat_sessions IS 'Stores Mae conversation sessions with proper user UUID relationships';
COMMENT ON TABLE public.user_profiles IS 'Extended user profile information and preferences';
COMMENT ON FUNCTION get_user_context_for_mae(TEXT) IS 'Returns comprehensive user context for Mae AI assistant';
COMMENT ON FUNCTION link_auth_user_to_profile(UUID, TEXT) IS 'Links Supabase auth users with profile users';

COMMIT;
