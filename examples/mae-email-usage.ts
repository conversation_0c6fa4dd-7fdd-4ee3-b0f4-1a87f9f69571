/**
 * Example usage of email tools for <PERSON> (Gemini AI Assistant)
 * This demonstrates how <PERSON> can use the email function tools to send emails
 * and check SMTP configuration.
 */

import { Content, GoogleGenAI } from '@google/genai';
import { 
  emailToolDeclarations,
  sendEmailToolDeclaration,
  checkSmtpToolDeclaration 
} from '../lib/send-email-tool-declaration';
import { 
  handleSendEmail, 
  handleCheckSmtp,
  SendEmailParams,
  CheckSmtpResult 
} from '../lib/send-email-function-tool';

// Configure the Gemini client
const ai = new GoogleGenAI({ apiKey: process.env.GOOGLE_API_KEY || process.env.GEMINI_API_KEY });

/**
 * Example 1: Mae checking SMTP configuration
 */
async function exampleCheckSmtp() {
  console.log('\n=== Example 1: Mae Checking SMTP Configuration ===');
  
  const config = {
    tools: [{
      functionDeclarations: [checkSmtpToolDeclaration]
    }]
  };

  const response = await ai.models.generateContent({
    model: 'gemini-2.0-flash-live-001',
    contents: 'Please check if the email system is working properly.',
    config: config
  });

  // Check for function calls in the response
  if (response.functionCalls && response.functionCalls.length > 0) {
    const functionCall = response.functionCalls[0];
    console.log(`Mae wants to call: ${functionCall.name}`);
    
    if (functionCall.name === 'check_smtp_connection') {
      // Execute the function
      const result: CheckSmtpResult = await handleCheckSmtp();
      console.log('SMTP Check Result:', result);
      
      // Send result back to Mae for a user-friendly response
      const finalResponse = await ai.models.generateContent({
        model: 'gemini-2.0-flash-live-001',
        contents: [
          { role: 'user', parts: [{ text: 'Please check if the email system is working properly.' }] },
          response.candidates?.[0]?.content as Content,
          { 
            role: 'user', 
            parts: [{ 
              functionResponse: {
                name: functionCall.name,
                response: { result }
              }
            }] 
          }
        ],
        config: config
      });
      
      console.log('Mae\'s Response:', finalResponse.text);
    }
  }
}

/**
 * Example 2: Mae sending a welcome email
 */
async function exampleSendWelcomeEmail() {
  console.log('\n=== Example 2: Mae Sending Welcome Email ===');
  
  const config = {
    tools: [{
      functionDeclarations: [sendEmailToolDeclaration]
    }]
  };

  const userPrompt = `
    Please send a welcome <NAME_EMAIL>. 
    The subject should be "Welcome to Our Kidz!" 
    Include a warm welcome message about joining our parenting community.
  `;

  const response = await ai.models.generateContent({
    model: 'gemini-2.0-flash-live-001',
    contents: userPrompt,
    config: config
  });

  // Check for function calls in the response
  if (response.functionCalls && response.functionCalls.length > 0) {
    const functionCall = response.functionCalls[0];
    console.log(`Mae wants to call: ${functionCall.name}`);
    console.log(`Arguments:`, JSON.stringify(functionCall.args, null, 2));
    
    if (functionCall.name === 'send_email') {
      // Execute the function
      const result = await handleSendEmail(functionCall.args as unknown as SendEmailParams);
      console.log('Send Email Result:', result);
      
      // Send result back to Mae for a user-friendly response
      const finalResponse = await ai.models.generateContent({
        model: 'gemini-2.0-flash-live-001',
        contents: [
          { role: 'user', parts: [{ text: userPrompt }] },
          response.candidates?.[0]?.content as Content,
          { 
            role: 'user', 
            parts: [{ 
              functionResponse: {
                name: functionCall.name,
                response: { result }
              }
            }] 
          }
        ],
        config: config
      });
      
      console.log('Mae\'s Response:', finalResponse.text);
    }
  }
}

/**
 * Example 3: Mae handling a contact form submission
 */
async function exampleContactFormEmail() {
  console.log('\n=== Example 3: Mae Handling Contact Form ===');
  
  const config = {
    tools: [{
      functionDeclarations: emailToolDeclarations // Both tools available
    }]
  };

  // Simulate contact form data
  const contactFormData = {
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '(*************',
    message: 'I\'m interested in enrolling my 4-year-old daughter in your program. Could you please send me more information about your curriculum and enrollment process?'
  };

  const userPrompt = `
    A parent has submitted a contact form with the following information:
    Name: ${contactFormData.name}
    Email: ${contactFormData.email}
    Phone: ${contactFormData.phone}
    Message: ${contactFormData.message}
    
    Please:
    1. Send a confirmation email to the parent thanking them for their interest
    2. Send a notification <NAME_EMAIL> about this new inquiry
    
    Make the emails professional and warm, reflecting Our Kidz brand.
  `;

  const response = await ai.models.generateContent({
    model: 'gemini-2.0-flash-live-001',
    contents: userPrompt,
    config: config
  });

  // Handle multiple function calls (parallel function calling)
  if (response.functionCalls && response.functionCalls.length > 0) {
    console.log(`Mae wants to make ${response.functionCalls.length} function calls:`);
    
    const functionResults = [];
    
    for (const functionCall of response.functionCalls) {
      console.log(`\nCalling: ${functionCall.name}`);
      console.log(`Arguments:`, JSON.stringify(functionCall.args, null, 2));
      
      let result;
      if (functionCall.name === 'send_email') {
        result = await handleSendEmail(functionCall.args as unknown as SendEmailParams);
      } else if (functionCall.name === 'check_smtp_connection') {
        result = await handleCheckSmtp();
      }
      
      functionResults.push({
        name: functionCall.name,
        response: { result }
      });
      
      console.log('Result:', result);
    }
    
    // Send all results back to Mae for a comprehensive response
    const contents = [
      { role: 'user', parts: [{ text: userPrompt }] },
      response.candidates?.[0]?.content
    ];
    
    // Add function responses
    for (const funcResult of functionResults) {
      contents.push({ 
        role: 'user', 
        parts: [{ functionResponse: funcResult }] 
      });
    }
    
    const finalResponse = await ai.models.generateContent({
      model: 'gemini-2.0-flash-live-001',
      contents: contents as Content[],
      config: config
    });
    
    console.log('\nMae\'s Final Response:', finalResponse.text);
  }
}

/**
 * Example 4: Mae sending a newsletter
 */
async function exampleNewsletterEmail() {
  console.log('\n=== Example 4: Mae Sending Newsletter ===');
  
  const config = {
    tools: [{
      functionDeclarations: [sendEmailToolDeclaration]
    }]
  };

  const userPrompt = `
    Please send our weekly <NAME_EMAIL>. 
    The subject should be "Our Kidz Weekly Update - [Current Week]"
    
    Include these highlights:
    - New playground equipment installed
    - Art class showcase this Friday at 3 PM
    - Parent-teacher conferences next week
    - Reminder about the upcoming field trip permission slips
    
    Make it engaging and include both plain text and HTML versions.
  `;

  const response = await ai.models.generateContent({
    model: 'gemini-2.0-flash-live-001',
    contents: userPrompt,
    config: config
  });

  if (response.functionCalls && response.functionCalls.length > 0) {
    const functionCall = response.functionCalls[0];
    console.log(`Mae created newsletter email:`);
    console.log(`Subject: ${functionCall.args?.subject}`);
    console.log(`Text Content Length: ${(functionCall.args as any)?.text_content?.length || 0} characters`);
    console.log(`HTML Content Length: ${(functionCall.args as any)?.html_content?.length || 0} characters`);
    
    // Execute the function
    const result = await handleSendEmail(functionCall.args as unknown as SendEmailParams);
    console.log('Newsletter Send Result:', result);
  }
}

// Main function to run examples
async function runEmailExamples() {
  console.log('🚀 Starting Mae email tool examples...\n');
  
  try {
    // Uncomment the examples you want to run:
    
    await exampleCheckSmtp();
    // await exampleSendWelcomeEmail();
    // await exampleContactFormEmail();
    // await exampleNewsletterEmail();
    
    console.log('\n✅ All examples completed!');
    
  } catch (error) {
    console.error('❌ Example execution failed:', error);
  }
}

// Export functions for use in other modules
export {
  exampleCheckSmtp,
  exampleSendWelcomeEmail,
  exampleContactFormEmail,
  exampleNewsletterEmail,
  runEmailExamples
};

// Run examples if this file is executed directly
if (require.main === module) {
  runEmailExamples();
}
