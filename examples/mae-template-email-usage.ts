/**
 * Example usage of <PERSON>'s template-based email tools
 * This demonstrates how <PERSON> can use the new template email functions
 * with the updated webhook URL and Our Kidz branding.
 */

import { ContentListUnion, GoogleGenAI, PartUnion } from '@google/genai';
import { 
  templateEmailToolDeclarations,
  sendWelcomeEmailToolDeclaration,
  sendContactConfirmationToolDeclaration,
  sendAdminNotificationToolDeclaration
} from '../lib/send-email-tool-declaration';
import { 
  handleSendWelcomeEmail,
  handleSendContactConfirmation,
  handleSendAdminNotification
} from '../lib/send-email-function-tool';

// Configure the Gemini client
const ai = new GoogleGenAI({ apiKey: process.env.GOOGLE_API_KEY || process.env.GEMINI_API_KEY });

/**
 * Example 1: Mae sending a welcome email to a new user
 */
async function exampleWelcomeEmail() {
  console.log('\n=== Example 1: Mae Sending Welcome Email ===');
  
  const config = {
    tools: [{
      functionDeclarations: [sendWelcomeEmailToolDeclaration]
    }]
  };

  const userPrompt = `
    A new parent named <PERSON> just signed up for Our Kidz <NAME_EMAIL>. 
    Please send her a welcome email to introduce her to our platform.
  `;

  const response = await ai.models.generateContent({
    model: 'gemini-2.0-flash-live-001',
    contents: userPrompt,
    config: config
  });

  if (response.functionCalls && response.functionCalls.length > 0) {
    const functionCall = response.functionCalls[0];
    console.log(`Mae wants to call: ${functionCall.name}`);
    console.log(`Arguments:`, JSON.stringify(functionCall.args, null, 2));
    
    if (functionCall.name === 'send_welcome_email') {
      const result = await handleSendWelcomeEmail(functionCall.args as { recipient_email: string; recipient_name?: string | undefined; log_to_webhook?: boolean | undefined; sources: { title: string; url: string; }[]; });
      console.log('Welcome Email Result:', result);
      
      // Get Mae's final response
      const finalResponse = await ai.models.generateContent({
        model: 'gemini-2.0-flash-live-001',
        contents: [
          { role: 'user', parts: [{ text: userPrompt }] } as PartUnion,
          response.candidates?.[0]?.content as PartUnion,
          { 
            role: 'user', 
            parts: [{ 
              functionResponse: {
                name: functionCall.name,
                response: { result }
              }
            }]
          } as PartUnion
        ],
        config: config
      });
      
      console.log('Mae\'s Response:', finalResponse.text);
    }
  }
}

/**
 * Example 2: Mae handling a complete contact form workflow
 */
async function exampleContactFormWorkflow() {
  console.log('\n=== Example 2: Mae Handling Contact Form Workflow ===');
  
  const config = {
    tools: [{
      functionDeclarations: templateEmailToolDeclarations // All template tools
    }]
  };

  // Simulate contact form submission
  const contactFormData = {
    name: 'Michael Chen',
    email: '<EMAIL>',
    phone: '(*************',
    message: 'Hi! I\'m interested in enrolling my 3-year-old son in your program. Could you please provide information about your curriculum, schedule, and enrollment process? Also, do you offer any trial days?'
  };

  const userPrompt = `
    A parent has submitted our contact form with the following information:
    Name: ${contactFormData.name}
    Email: ${contactFormData.email}
    Phone: ${contactFormData.phone}
    Message: ${contactFormData.message}
    
    Please handle this contact form submission by:
    1. Sending a confirmation email to the parent
    2. Sending a notification to our admin team
    
    Make sure both emails are professional and reflect Our Kidz branding.
  `;

  const response = await ai.models.generateContent({
    model: 'gemini-2.0-flash-live-001',
    contents: userPrompt,
    config: config
  });

  if (response.functionCalls && response.functionCalls.length > 0) {
    console.log(`Mae wants to make ${response.functionCalls.length} function calls:`);
    
    const functionResults = [];
    
    for (const functionCall of response.functionCalls) {
      console.log(`\nCalling: ${functionCall.name}`);
      console.log(`Arguments:`, JSON.stringify(functionCall.args, null, 2));
      
      let result;
      if (functionCall.name === 'send_contact_confirmation') {
        result = await handleSendContactConfirmation(functionCall.args as { recipient_email: string; recipient_name?: string | undefined; user_message?: string | undefined; log_to_webhook?: boolean | undefined; sources: { title: string; url: string; }[]; });
      } else if (functionCall.name === 'send_admin_notification') {
        result = await handleSendAdminNotification(functionCall.args as { contact_name: string; contact_email: string; contact_phone?: string | undefined; contact_message: string; admin_email?: string | undefined; log_to_webhook?: boolean | undefined; });
      }
      
      functionResults.push({
        name: functionCall.name,
        response: { result }
      });
      
      console.log('Result:', result);
    }
    
    // Get Mae's comprehensive response
    const contents = [
      { role: 'user', parts: [{ text: userPrompt }] } as PartUnion,
      response.candidates?.[0]?.content as PartUnion
    ];
    
    for (const funcResult of functionResults) {
      contents.push({
        role: 'user',
        parts: [{ functionResponse: funcResult }]
      } as PartUnion);
    }
    
    const finalResponse = await ai.models.generateContent({
      model: 'gemini-2.0-flash-live-001',
      contents: contents as ContentListUnion,
      config: config
    });
    
    console.log('\nMae\'s Final Response:', finalResponse.text);
  }
}

/**
 * Example 3: Mae responding to a user request for email status
 */
async function exampleEmailStatusCheck() {
  console.log('\n=== Example 3: Mae Checking Email System Status ===');
  
  const config = {
    tools: [{
      functionDeclarations: templateEmailToolDeclarations
    }]
  };

  const userPrompt = `
    I want to make sure our email system is working properly. 
    Can you send a test welcome <NAME_EMAIL> to verify everything is functioning?
  `;

  const response = await ai.models.generateContent({
    model: 'gemini-2.0-flash-live-001',
    contents: userPrompt,
    config: config
  });

  if (response.functionCalls && response.functionCalls.length > 0) {
    const functionCall = response.functionCalls[0];
    console.log(`Mae's test approach: ${functionCall.name}`);
    console.log(`Test parameters:`, JSON.stringify(functionCall.args, null, 2));
    
    if (functionCall.name === 'send_welcome_email') {
      const result = await handleSendWelcomeEmail(functionCall.args as { recipient_email: string; recipient_name?: string | undefined; log_to_webhook?: boolean | undefined; sources: { title: string; url: string; }[]; });
      console.log('Test Email Result:', result);
      
      const finalResponse = await ai.models.generateContent({
        model: 'gemini-2.0-flash-live-001',
        contents: [
          { role: 'user', parts: [{ text: userPrompt }] } as PartUnion,
          response.candidates?.[0]?.content as PartUnion,
          {
            role:'user',
            parts: [{
              functionResponse: {
                name: functionCall.name,
                response: { result }
              }
            }]
          } as PartUnion
        ],
        config: config
      });
      
      console.log('Mae\'s Test Report:', finalResponse.text);
    }
  }
}

/**
 * Example 4: Mae handling multiple email scenarios in one conversation
 */
async function exampleMultipleEmailScenarios() {
  console.log('\n=== Example 4: Mae Handling Multiple Email Scenarios ===');
  
  const config = {
    tools: [{
      functionDeclarations: templateEmailToolDeclarations
    }]
  };

  const userPrompt = `
    We have several email tasks to handle:
    
    1. Welcome a new parent: Lisa Rodriguez (<EMAIL>)
    2. Send confirmation for a contact form from David Kim (<EMAIL>) who asked about our summer programs
    3. Notify admin about David's inquiry
    
    Please handle all of these email tasks efficiently.
  `;

  const response = await ai.models.generateContent({
    model: 'gemini-2.0-flash-live-001',
    contents: userPrompt,
    config: config
  });

  if (response.functionCalls && response.functionCalls.length > 0) {
    console.log(`Mae identified ${response.functionCalls.length} email tasks:`);
    
    const functionResults = [];
    
    for (let i = 0; i < response.functionCalls.length; i++) {
      const functionCall = response.functionCalls[i];
      console.log(`\nTask ${i + 1}: ${functionCall.name}`);
      console.log(`Parameters:`, JSON.stringify(functionCall.args, null, 2));
      
      let result;
      switch (functionCall.name) {
        case 'send_welcome_email':
          result = await handleSendWelcomeEmail(functionCall.args as { recipient_email: string; recipient_name?: string | undefined; log_to_webhook?: boolean | undefined; sources: { title: string; url: string; }[]; });
          break;
        case 'send_contact_confirmation':
          result = await handleSendContactConfirmation(functionCall.args as { recipient_email: string; recipient_name?: string | undefined; user_message?: string | undefined; log_to_webhook?: boolean | undefined; sources: { title: string; url: string; }[]; });
          break;
        case 'send_admin_notification':
          result = await handleSendAdminNotification(functionCall.args as { contact_name: string; contact_email: string; contact_phone?: string | undefined; contact_message: string; admin_email?: string | undefined; log_to_webhook?: boolean | undefined; });
          break;
      }
      
      functionResults.push({
        name: functionCall.name,
        response: { result }
      });
      
      console.log(`Task ${i + 1} Result:`, result?.success ? '✅ Success' : '❌ Failed');
    }
    
    // Get Mae's summary
    const contents = [
      { role: 'user', parts: [{ text: userPrompt }] } as PartUnion,
      response.candidates?.[0]?.content as PartUnion  
    ];
    
    for (const funcResult of functionResults) {
      contents.push({
        role: 'user',
        parts: [{ functionResponse: funcResult }]
      } as PartUnion);
    }
    
    const finalResponse = await ai.models.generateContent({
      model: 'gemini-2.0-flash-live-001',
      contents: contents as ContentListUnion,
      config: config
    });
    
    console.log('\nMae\'s Summary Report:', finalResponse.text);
  }
}

// Main function to run examples
async function runTemplateEmailExamples() {
  console.log('🎨 Starting Mae Template Email Examples...\n');
  console.log('Updated Webhook URL: https://terminallylazy.app.n8n.cloud/webhook/email-log');
  console.log('=' .repeat(70));
  
  try {
    // Uncomment the examples you want to run:
    
    await exampleWelcomeEmail();
    // await exampleContactFormWorkflow();
    // await exampleEmailStatusCheck();
    // await exampleMultipleEmailScenarios();
    
    console.log('\n✅ All template email examples completed!');
    console.log('\n📧 Mae can now handle:');
    console.log('   • Welcome emails for new users');
    console.log('   • Contact form confirmations');
    console.log('   • Admin notifications');
    console.log('   • Multiple email workflows');
    console.log('\n🔗 All emails will be logged to: https://terminallylazy.app.n8n.cloud/webhook/email-log');
    
  } catch (error) {
    console.error('❌ Template email example execution failed:', error);
  }
}

// Export functions for use in other modules
export {
  exampleWelcomeEmail,
  exampleContactFormWorkflow,
  exampleEmailStatusCheck,
  exampleMultipleEmailScenarios,
  runTemplateEmailExamples
};

// Run examples if this file is executed directly
if (require.main === module) {
  runTemplateEmailExamples();
}
