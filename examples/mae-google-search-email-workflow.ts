/**
 * Example showing <PERSON> using Google Search + Email Tools workflow
 * This demonstrates how Mae MUST ALWAYS search Google first, then send emails with sources
 */

import { GoogleGenAI } from '@google/genai';
import { 
  emailToolDeclarations,
  sendWelcomeEmailToolDeclaration,
  sendContactConfirmationToolDeclaration
} from '../lib/send-email-tool-declaration';

// Configure the Gemini client
const ai = new GoogleGenAI({ apiKey: process.env.GOOGLE_API_KEY || process.env.GEMINI_API_KEY });

/**
 * Example 1: Mae sending welcome email with Google Search for parenting tips
 */
async function exampleWelcomeEmailWithSearch() {
  console.log('\n=== Example 1: Mae Welcome Email with Google Search ===');
  
  // Configure Mae with both Google Search and Email tools
  const config = {
    tools: [
      { googleSearch: {} }, // Google Search tool
      { functionDeclarations: [sendWelcomeEmailToolDeclaration] } // Email tool
    ]
  };

  const userPrompt = `
    A new parent named <PERSON> (<EMAIL>) just joined Our Kidz. 
    Please send her a welcome email that includes current, evidence-based parenting tips for new parents.
    
    IMPORTANT: You must search Google for the latest parenting information before sending the email.
  `;

  console.log('🔍 Mae will search Google first, then send email with sources...');

  const response = await ai.models.generateContent({
    model: 'gemini-2.0-flash-live-001',
    contents: userPrompt,
    config: config
  });

  console.log('Mae\'s response includes both search and email actions');
  console.log('Function calls made:', response.functionCalls?.length || 0);
  
  if (response.functionCalls) {
    for (const call of response.functionCalls) {
      console.log(`- ${call.name}: ${JSON.stringify(call.args, null, 2)}`);
    }
  }
}

/**
 * Example 2: Mae handling contact form with research-based response
 */
async function exampleContactFormWithResearch() {
  console.log('\n=== Example 2: Mae Contact Form Response with Research ===');
  
  const config = {
    tools: [
      { googleSearch: {} },
      { functionDeclarations: [sendContactConfirmationToolDeclaration] }
    ]
  };

  const contactFormData = {
    name: 'Michael Chen',
    email: '<EMAIL>',
    message: 'My 3-year-old is having trouble sleeping through the night. What are some evidence-based strategies I can try?'
  };

  const userPrompt = `
    A parent submitted a contact form asking about sleep issues:
    
    Name: ${contactFormData.name}
    Email: ${contactFormData.email}
    Question: ${contactFormData.message}
    
    Please:
    1. Search Google for current, evidence-based information about toddler sleep issues
    2. Send a confirmation email that includes helpful, research-backed advice
    
    IMPORTANT: You must search Google for the latest sleep research before responding.
  `;

  console.log('🔍 Mae will research toddler sleep strategies, then send informed response...');

  const response = await ai.models.generateContent({
    model: 'gemini-2.0-flash-live-001',
    contents: userPrompt,
    config: config
  });

  console.log('Mae\'s research-based response:');
  console.log('Function calls made:', response.functionCalls?.length || 0);
  
  if (response.functionCalls) {
    for (const call of response.functionCalls) {
      console.log(`- ${call.name}`);
      if (call.name === 'send_contact_confirmation' && call.args?.sources) {
        console.log('  Sources included:', (call.args.sources as any[]).length || 0);
        (call.args.sources as any[]).forEach((source: any, index: number) => {
          console.log(`    ${index + 1}. ${source.title}`);
        });
      }
    }
  }
}

/**
 * Example 3: Mae providing nutrition advice with current research
 */
async function exampleNutritionAdviceEmail() {
  console.log('\n=== Example 3: Mae Nutrition Advice with Current Research ===');
  
  const config = {
    tools: [
      { googleSearch: {} },
      { functionDeclarations: emailToolDeclarations }
    ]
  };

  const userPrompt = `
    Please send an <NAME_EMAIL> about healthy snack options for toddlers.
    The subject should be "Healthy Toddler Snacks - Evidence-Based Recommendations"
    
    IMPORTANT: 
    1. Search Google for the latest pediatric nutrition guidelines
    2. Include current research about toddler nutrition
    3. Provide practical, evidence-based snack suggestions
    4. Include all sources in the email
  `;

  console.log('🔍 Mae will research current pediatric nutrition guidelines...');

  const response = await ai.models.generateContent({
    model: 'gemini-2.0-flash-live-001',
    contents: userPrompt,
    config: config
  });

  console.log('Mae\'s nutrition research and email:');
  console.log('Function calls made:', response.functionCalls?.length || 0);
  
  if (response.functionCalls) {
    for (const call of response.functionCalls) {
      console.log(`- ${call.name}`);
      if (call.args?.sources) {
        console.log('  Research sources:', (call.args.sources as any[]).length || 0);
        (call.args.sources as any[]).forEach((source: any, index: number) => {
          console.log(`    ${index + 1}. ${source.title}`);
        });
      }
    }
  }
}

/**
 * Example 4: Mae handling multiple inquiries with comprehensive research
 */
async function exampleMultipleInquiriesWithResearch() {
  console.log('\n=== Example 4: Mae Multiple Inquiries with Research ===');
  
  const config = {
    tools: [
      { googleSearch: {} },
      { functionDeclarations: emailToolDeclarations }
    ]
  };

  const userPrompt = `
    We have several parent inquiries to handle today:
    
    1. Welcome email for new parent Lisa Rodriguez (<EMAIL>) - include current child development milestones
    2. Response to David Kim (<EMAIL>) about screen time guidelines for 4-year-olds
    3. Email to admin about both inquiries
    
    IMPORTANT: For each email, you must:
    1. Search Google for the most current, evidence-based information
    2. Include research sources in every email
    3. Ensure all advice is backed by recent studies
  `;

  console.log('🔍 Mae will conduct comprehensive research for multiple topics...');

  const response = await ai.models.generateContent({
    model: 'gemini-2.0-flash-live-001',
    contents: userPrompt,
    config: config
  });

  console.log('Mae\'s comprehensive research and email workflow:');
  console.log('Total function calls:', response.functionCalls?.length || 0);
  
  let searchCount = 0;
  let emailCount = 0;
  
  if (response.functionCalls) {
    for (const call of response.functionCalls) {
      if (call.name === 'googleSearch') {
        searchCount++;
        console.log(`🔍 Search ${searchCount}: ${call.args?.query}`);
      } else if (call.name?.includes('email')) {
        emailCount++;
        console.log(`📧 Email ${emailCount}: ${call.name}`);
        if (call.args?.sources) {
          console.log(`   Sources: ${(call.args.sources as any[]).length || 0}`);
          (call.args.sources as any[]).forEach((source: any, index: number) => {
            console.log(`    ${index + 1}. ${source.title}`);
          });
        }
      }
    }
  }
  
  console.log(`\nSummary: ${searchCount} searches, ${emailCount} emails`);
}

/**
 * Example 5: Testing Mae's compliance with search-first requirement
 */
async function testMaeSearchCompliance() {
  console.log('\n=== Example 5: Testing Mae\'s Search Compliance ===');
  
  const config = {
    tools: [
      { googleSearch: {} },
      { functionDeclarations: [sendWelcomeEmailToolDeclaration] }
    ]
  };

  const userPrompt = `
    Send a welcome <NAME_EMAIL> with information about child safety at home.
    
    Remember: You MUST search Google for current safety information before sending any email.
  `;

  console.log('🧪 Testing if Mae searches before emailing...');

  const response = await ai.models.generateContent({
    model: 'gemini-2.0-flash-live-001',
    contents: userPrompt,
    config: config
  });

  // Analyze Mae's behavior
  const functionCalls = response.functionCalls || [];
  const hasSearch = functionCalls.some(call => call.name === 'googleSearch');
  const hasEmail = functionCalls.some(call => call.name?.includes('email'));
  
  console.log('🔍 Mae searched Google:', hasSearch ? '✅ YES' : '❌ NO');
  console.log('📧 Mae sent email:', hasEmail ? '✅ YES' : '❌ NO');
  
  if (hasSearch && hasEmail) {
    console.log('✅ COMPLIANCE: Mae searched first, then sent email with sources');
  } else if (hasEmail && !hasSearch) {
    console.log('❌ VIOLATION: Mae sent email without searching Google first');
  } else if (hasSearch && !hasEmail) {
    console.log('⚠️ INCOMPLETE: Mae searched but didn\'t send email');
  }
  
  // Check if email includes sources
  const emailCall = functionCalls.find(call => call.name?.includes('email'));
  if (emailCall && emailCall.args?.sources) {
    console.log(`📚 Sources included: ${(emailCall.args.sources as any[]).length || 0}`);
    (emailCall.args.sources as any[]).forEach((source: any, index: number) => {
      console.log(`   ${index + 1}. ${source.title}`);
    });
  }
}

// Main function to run examples
async function runGoogleSearchEmailExamples() {
  console.log('🔍📧 Starting Mae Google Search + Email Workflow Examples...\n');
  console.log('=' .repeat(80));
  console.log('REQUIREMENT: Mae MUST ALWAYS search Google before sending emails');
  console.log('REQUIREMENT: Mae MUST ALWAYS include sources in every email');
  console.log('=' .repeat(80));
  
  try {
    // Run examples to demonstrate the workflow
    await exampleWelcomeEmailWithSearch();
    // await exampleContactFormWithResearch();
    // await exampleNutritionAdviceEmail();
    // await exampleMultipleInquiriesWithResearch();
    // await testMaeSearchCompliance();
    
    console.log('\n✅ All Google Search + Email examples completed!');
    console.log('\n📋 Mae\'s Required Workflow:');
    console.log('   1. 🔍 ALWAYS search Google for current information');
    console.log('   2. 📧 Send email with research-based content');
    console.log('   3. 📚 ALWAYS include sources in every email');
    console.log('   4. 🔗 Log all activity to webhook');
    
    console.log('\n🎯 This ensures all Our Kidz emails contain:');
    console.log('   • Current, evidence-based information');
    console.log('   • Proper source attribution');
    console.log('   • Trustworthy parenting guidance');
    console.log('   • Professional credibility');
    
  } catch (error) {
    console.error('❌ Google Search + Email workflow failed:', error);
  }
}

// Export functions for use in other modules
export {
  exampleWelcomeEmailWithSearch,
  exampleContactFormWithResearch,
  exampleNutritionAdviceEmail,
  exampleMultipleInquiriesWithResearch,
  testMaeSearchCompliance,
  runGoogleSearchEmailExamples
};

// Run examples if this file is executed directly
if (require.main === module) {
  runGoogleSearchEmailExamples();
}
