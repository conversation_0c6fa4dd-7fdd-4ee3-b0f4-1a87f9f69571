// Example: Edge middleware for redirecting clerk.our-kidz.com
// NOTE: This would need to be deployed separately or configured at CDN level
// This file is for reference only - don't include in main app

import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'

export function middleware(request: NextRequest) {
  const hostname = request.headers.get('host')
  
  // Check if it's a direct browser visit to clerk.our-kidz.com
  if (hostname === 'clerk.our-kidz.com') {
    const acceptHeader = request.headers.get('accept') || ''
    const contentType = request.headers.get('content-type') || ''
    
    // Detect if it's a browser visit (not an API call)
    const isBrowserVisit = 
      acceptHeader.includes('text/html') && 
      !acceptHeader.includes('application/json') &&
      !contentType.includes('application/json') &&
      request.method === 'GET'
    
    if (isBrowserVisit) {
      // Redirect browsers to your auth page
      return NextResponse.redirect('https://our-kidz.com/auth', {
        status: 301,
        headers: {
          'Cache-Control': 'public, max-age=3600'
        }
      })
    }
  }
  
  // Let API calls pass through
  return NextResponse.next()
}

export const config = {
  matcher: '/:path*'
}