/**
 * Example usage of the email service
 * This demonstrates how to send emails with webhook logging
 */

const { sendEmailWithWebhookLogging } = require('./send-email');
const EmailService = require('./email-service');

// Example 1: Using the simple function approach
async function simpleEmailExample() {
  console.log('\n=== Simple Email Function Example ===');
  
  const recipientEmail = '<EMAIL>'; // Replace with actual recipient
  
  const result = await sendEmailWithWebhookLogging(
    recipientEmail,
    'Welcome to Our Kidz!',
    'Thank you for your interest in Our Kidz. We\'re excited to have you join our community!',
    `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h1 style="color: #333;">Welcome to Our Kidz!</h1>
      <p>Thank you for your interest in Our Kidz. We're excited to have you join our community!</p>
      <p>If you have any questions, feel free to reach out to us.</p>
      <p>Best regards,<br>The Our Kidz Team</p>
    </div>
    `
  );

  if (result.success) {
    console.log('✅ Email sent and logged successfully!');
    console.log('📧 Message ID:', result.emailInfo.messageId);
    console.log('📅 Sent at:', result.sentDate);
  } else {
    console.log('❌ Email operation failed:', result.error);
  }
}

// Example 2: Using the EmailService class approach
async function classBasedEmailExample() {
  console.log('\n=== EmailService Class Example ===');
  
  const emailService = new EmailService();
  
  // First, verify the SMTP connection
  const isConnected = await emailService.verifyConnection();
  if (!isConnected) {
    console.error('❌ Cannot proceed - SMTP connection failed');
    return;
  }

  // Send multiple emails
  const recipients = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ];

  for (const recipient of recipients) {
    console.log(`\n📧 Sending email to: ${recipient}`);
    
    const result = await emailService.sendEmailWithLogging(
      recipient,
      'Our Kidz Newsletter - Weekly Update',
      'Here\'s your weekly update from Our Kidz with the latest news and activities.',
      `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <h2 style="color: #4CAF50;">Our Kidz Weekly Update</h2>
        <p>Dear Parent,</p>
        <p>Here are this week's highlights:</p>
        <ul>
          <li>New playground equipment installed</li>
          <li>Art class showcase this Friday</li>
          <li>Parent-teacher conferences next week</li>
        </ul>
        <p>Thank you for being part of the Our Kidz family!</p>
        <p>Best regards,<br>The Our Kidz Team</p>
      </div>
      `
    );

    if (result.success) {
      console.log(`✅ Email sent to ${recipient} - ID: ${result.messageId}`);
    } else {
      console.log(`❌ Failed to send email to ${recipient}: ${result.error}`);
    }

    // Add a small delay between emails to be respectful to the SMTP server
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

// Example 3: Contact form email (typical use case for landing pages)
async function contactFormEmailExample() {
  console.log('\n=== Contact Form Email Example ===');
  
  // Simulate form data that might come from a contact form
  const formData = {
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '(*************',
    message: 'I\'m interested in enrolling my child in your program. Could you please send me more information?'
  };

  // Send confirmation email to the person who submitted the form
  const confirmationResult = await sendEmailWithWebhookLogging(
    formData.email,
    'Thank you for contacting Our Kidz',
    `Dear ${formData.name},\n\nThank you for your interest in Our Kidz! We have received your message and will get back to you within 24 hours.\n\nYour message:\n"${formData.message}"\n\nBest regards,\nThe Our Kidz Team`,
    `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #4CAF50;">Thank you for contacting Our Kidz!</h2>
      <p>Dear ${formData.name},</p>
      <p>Thank you for your interest in Our Kidz! We have received your message and will get back to you within 24 hours.</p>
      <div style="background-color: #f5f5f5; padding: 15px; border-left: 4px solid #4CAF50; margin: 20px 0;">
        <strong>Your message:</strong><br>
        "${formData.message}"
      </div>
      <p>If you have any urgent questions, please call us at (555) 123-KIDZ.</p>
      <p>Best regards,<br>The Our Kidz Team</p>
    </div>
    `
  );

  // Send notification email to admin
  const adminEmail = '<EMAIL>'; // Replace with actual admin email
  const adminNotificationResult = await sendEmailWithWebhookLogging(
    adminEmail,
    'New Contact Form Submission - Our Kidz',
    `New contact form submission received:\n\nName: ${formData.name}\nEmail: ${formData.email}\nPhone: ${formData.phone}\n\nMessage:\n${formData.message}`,
    `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
      <h2 style="color: #FF9800;">New Contact Form Submission</h2>
      <table style="width: 100%; border-collapse: collapse;">
        <tr>
          <td style="padding: 10px; border: 1px solid #ddd; background-color: #f9f9f9;"><strong>Name:</strong></td>
          <td style="padding: 10px; border: 1px solid #ddd;">${formData.name}</td>
        </tr>
        <tr>
          <td style="padding: 10px; border: 1px solid #ddd; background-color: #f9f9f9;"><strong>Email:</strong></td>
          <td style="padding: 10px; border: 1px solid #ddd;">${formData.email}</td>
        </tr>
        <tr>
          <td style="padding: 10px; border: 1px solid #ddd; background-color: #f9f9f9;"><strong>Phone:</strong></td>
          <td style="padding: 10px; border: 1px solid #ddd;">${formData.phone}</td>
        </tr>
        <tr>
          <td style="padding: 10px; border: 1px solid #ddd; background-color: #f9f9f9;"><strong>Message:</strong></td>
          <td style="padding: 10px; border: 1px solid #ddd;">${formData.message}</td>
        </tr>
      </table>
      <p><em>This email was automatically generated from the Our Kidz contact form.</em></p>
    </div>
    `
  );

  console.log('Confirmation email result:', confirmationResult.success ? '✅ Sent' : '❌ Failed');
  console.log('Admin notification result:', adminNotificationResult.success ? '✅ Sent' : '❌ Failed');
}

// Main function to run examples
async function runExamples() {
  console.log('🚀 Starting email service examples...\n');
  
  try {
    // Uncomment the examples you want to run:
    
    // await simpleEmailExample();
    // await classBasedEmailExample();
    // await contactFormEmailExample();
    
    console.log('\n✅ All examples completed!');
    console.log('\n💡 To run these examples:');
    console.log('1. Update the recipient email addresses');
    console.log('2. Make sure your .env.local file has the correct SMTP settings');
    console.log('3. Update the N8N_WEBHOOK_URL in your .env.local file');
    console.log('4. Uncomment the example functions you want to test');
    console.log('5. Run: node example-usage.js');
    
  } catch (error) {
    console.error('❌ Example execution failed:', error.message);
  }
}

// Run the examples
runExamples();
