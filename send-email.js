const nodemailer = require('nodemailer');
const axios = require('axios');
require('dotenv').config({ path: '.env.local' });

/**
 * Send email via SMTP and log to n8n webhook
 * This is a standalone function version for simple email sending
 */

/**
 * Send an email and log the activity to a webhook
 * @param {string} recipientEmail - Email address of the recipient
 * @param {string} subject - Email subject line
 * @param {string} textContent - Plain text email content
 * @param {string} htmlContent - HTML email content (optional)
 * @returns {Promise<Object>} - Result object with success status
 */
async function sendEmailWithWebhookLogging(recipientEmail, subject, textContent, htmlContent = null) {
  // Step 1: Create the SMTP transporter using environment variables
  const transporter = nodemailer.createTransporter({
    host: process.env.SMTP_HOST || 'smtp.hostinger.com',
    port: parseInt(process.env.SMTP_PORT) || 587,
    secure: (process.env.SMTP_SECURE === 'true') || false, // true for 465, false for 587
    auth: {
      user: process.env.SMTP_USER || '<EMAIL>',
      pass: process.env.SMTP_PASS || 'Three110409!!*',
    },
  });

  // Step 2: Define the email content
  const mailOptions = {
    from: `${process.env.FROM_NAME || 'Our Kidz'} <${process.env.FROM_EMAIL || process.env.SMTP_USER}>`,
    to: recipientEmail,
    subject: subject,
    text: textContent,
  };

  // Add HTML content if provided
  if (htmlContent) {
    mailOptions.html = htmlContent;
  }

  try {
    console.log(`📧 Sending email to: ${recipientEmail}`);
    
    // Step 3: Send the email
    const info = await transporter.sendMail(mailOptions);
    console.log('✅ Email sent successfully:', info.response);

    // Step 4: Prepare webhook payload with current timestamp
    const sentDate = new Date().toISOString();
    const webhookUrl = process.env.N8N_WEBHOOK_URL || 'https://my-n8n-instance.com/webhook/email-log';
    
    const payload = {
      email: recipientEmail,
      date: sentDate,
      subject: subject,
      success: true,
      messageId: info.messageId
    };

    // Step 5: Send POST request to n8n webhook
    console.log('📡 Logging email activity to webhook...');
    
    const webhookResponse = await axios.post(webhookUrl, payload, {
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10 second timeout
    });

    console.log('✅ Webhook logged successfully:', webhookResponse.status);

    return {
      success: true,
      emailInfo: info,
      webhookResponse: webhookResponse.data,
      sentDate: sentDate
    };

  } catch (error) {
    console.error('❌ Operation failed:', error.message);

    // Attempt to log the failure to webhook (optional)
    try {
      const failurePayload = {
        email: recipientEmail,
        date: new Date().toISOString(),
        subject: subject,
        success: false,
        error: error.message
      };

      const webhookUrl = process.env.N8N_WEBHOOK_URL || 'https://my-n8n-instance.com/webhook/email-log';
      await axios.post(webhookUrl, failurePayload, {
        headers: { 'Content-Type': 'application/json' },
        timeout: 5000
      });

      console.log('📡 Failure logged to webhook');
    } catch (webhookError) {
      console.error('❌ Failed to log error to webhook:', webhookError.message);
    }

    return {
      success: false,
      error: error.message
    };
  }
}

// Export the function for use in other modules
module.exports = { sendEmailWithWebhookLogging };

// Example usage function
async function example() {
  const recipientEmail = '<EMAIL>'; // Replace with actual recipient
  
  const result = await sendEmailWithWebhookLogging(
    recipientEmail,
    'Test Email from Our Kidz Landing Page',
    'Hello! This is a test email sent from Node.js.',
    '<h1>Hello!</h1><p>This is a <strong>test email</strong> sent from Node.js.</p>'
  );

  if (result.success) {
    console.log('🎉 Email sent and logged successfully!');
    console.log('Message ID:', result.emailInfo.messageId);
    console.log('Sent at:', result.sentDate);
  } else {
    console.log('💥 Email operation failed:', result.error);
  }
}

// Uncomment the line below to run the example
// example();
