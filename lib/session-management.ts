/**
 * Session Management Utilities
 * 
 * Provides utilities for managing Mae conversation sessions, including
 * context compression, session resumption, and conversation summarization.
 */

import { userContextService, type UserContextData } from './user-context-service'

export interface SessionSummary {
  session_id: string
  user_id: string
  start_time: string
  end_time?: string
  message_count: number
  topics_discussed: string[]
  key_outcomes: string[]
  follow_up_needed: string[]
  compressed_context: string
}

export interface ConversationMessage {
  role: 'user' | 'assistant' | 'system'
  content: string
  timestamp: string
  type?: 'greeting' | 'question' | 'response' | 'search' | 'email_sent' | 'location_search' | 'onboarding'
  metadata?: any
}

class SessionManager {
  private readonly MAX_CONTEXT_LENGTH = 4000 // chars
  private readonly COMPRESSION_THRESHOLD = 8000 // chars

  /**
   * Compress conversation history to fit within context window
   */
  compressConversationHistory(messages: ConversationMessage[]): string {
    if (!messages || messages.length === 0) return ''

    // Sort messages by timestamp
    const sortedMessages = [...messages].sort((a, b) => 
      new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()
    )

    // Extract key information from messages
    const keyPoints = this.extractKeyPoints(sortedMessages)
    const recentMessages = this.getRecentMessages(sortedMessages, 5)
    
    const compressed = [
      '=== CONVERSATION SUMMARY ===',
      `Total messages: ${messages.length}`,
      `Time span: ${this.getTimeSpan(sortedMessages)}`,
      '',
      '=== KEY POINTS ===',
      ...keyPoints,
      '',
      '=== RECENT MESSAGES ===',
      ...recentMessages.map(msg => `${msg.role}: ${this.truncateMessage(msg.content, 200)}`)
    ].join('\n')

    // If still too long, further compress
    if (compressed.length > this.MAX_CONTEXT_LENGTH) {
      return this.furtherCompress(compressed)
    }

    return compressed
  }

  /**
   * Resume a session with relevant context
   */
  async resumeSession(authUserId: string, includeHistory: boolean = true): Promise<{
    userContext: UserContextData | null
    conversationSummary: string
    sessionInstructions: string
  }> {
    try {
      console.log('📎 Resuming session for user:', authUserId)

      // Load user context
      const userContext = await userContextService.getUserContext(authUserId)
      
      if (!userContext) {
        return {
          userContext: null,
          conversationSummary: '',
          sessionInstructions: 'This appears to be a new user. Please introduce yourself and offer to help with onboarding.'
        }
      }

      let conversationSummary = ''
      let sessionInstructions = `Welcome back, ${userContext.user.name}!`

      if (includeHistory) {
        // Get recent conversation history
        const recentMessages = await userContextService.getConversationHistory(authUserId, 20)
        
        if (recentMessages.length > 0) {
          conversationSummary = this.compressConversationHistory(recentMessages)
          
          const lastConversationTime = this.getLastConversationTime(recentMessages)
          const timeSinceLastChat = this.getTimeSinceLastConversation(lastConversationTime)
          
          sessionInstructions = [
            `Welcome back, ${userContext.user.name}! ${timeSinceLastChat}`,
            'Here\'s a summary of our recent conversations:',
            conversationSummary,
            '',
            'How can I help you today? Feel free to reference any of our previous discussions.'
          ].join('\n')
        }
      }

      console.log('✅ Session resume prepared:', {
        user: userContext.user.name,
        familyMembers: userContext.family_members.length,
        hasHistory: conversationSummary.length > 0
      })

      return {
        userContext,
        conversationSummary,
        sessionInstructions
      }

    } catch (error) {
      console.error('❌ Error resuming session:', error)
      return {
        userContext: null,
        conversationSummary: '',
        sessionInstructions: 'Hello! I\'m Mae, ready to help with your parenting questions.'
      }
    }
  }

  /**
   * Create a session summary for storage
   */
  createSessionSummary(
    sessionId: string,
    userId: string,
    messages: ConversationMessage[],
    startTime: string,
    endTime?: string
  ): SessionSummary {
    const topics = this.extractTopics(messages)
    const outcomes = this.extractKeyOutcomes(messages)
    const followUps = this.extractFollowUpNeeds(messages)
    const compressedContext = this.compressConversationHistory(messages)

    return {
      session_id: sessionId,
      user_id: userId,
      start_time: startTime,
      end_time: endTime || new Date().toISOString(),
      message_count: messages.length,
      topics_discussed: topics,
      key_outcomes: outcomes,
      follow_up_needed: followUps,
      compressed_context: compressedContext
    }
  }

  /**
   * Generate contextual greeting based on user history
   */
  generateContextualGreeting(userContext: UserContextData, conversationHistory: ConversationMessage[]): string {
    const { user, family_members } = userContext
    const childrenNames = family_members.map(m => m.name).join(', ')
    
    // Determine greeting based on history
    if (conversationHistory.length === 0) {
      return `Hi ${user.name}! I'm Mae, and I'm excited to help you with ${childrenNames}. What's on your mind today?`
    }

    const lastTopics = this.extractTopics(conversationHistory.slice(-5))
    const timeSinceLastChat = this.getTimeSinceLastConversation(
      this.getLastConversationTime(conversationHistory)
    )

    if (lastTopics.length > 0) {
      return `Welcome back, ${user.name}! ${timeSinceLastChat} Last time we talked about ${lastTopics.slice(0, 2).join(' and ')}. How can I help you today?`
    }

    return `Hi ${user.name}! Good to see you again. How are ${childrenNames} doing? What questions do you have for me today?`
  }

  // Private helper methods
  private extractKeyPoints(messages: ConversationMessage[]): string[] {
    const points: string[] = []
    
    for (const message of messages) {
      if (message.role === 'assistant' && message.content) {
        // Extract recommendations or key advice
        if (message.content.includes('recommend') || message.content.includes('suggest')) {
          points.push(`• Recommendation given: ${this.extractFirstSentence(message.content)}`)
        }
        
        // Extract health concerns addressed
        if (message.content.includes('doctor') || message.content.includes('pediatrician')) {
          points.push(`• Medical consultation discussed`)
        }
        
        // Extract important safety information
        if (message.content.includes('safety') || message.content.includes('emergency')) {
          points.push(`• Safety information provided`)
        }
      }
      
      if (message.type === 'email_sent') {
        points.push(`• Summary emailed to user`)
      }
      
      if (message.type === 'location_search') {
        points.push(`• Healthcare providers located`)
      }
    }
    
    return points.slice(0, 5) // Limit to top 5 points
  }

  private getRecentMessages(messages: ConversationMessage[], count: number): ConversationMessage[] {
    return messages.slice(-count)
  }

  private getTimeSpan(messages: ConversationMessage[]): string {
    if (messages.length < 2) return 'Single message'
    
    const first = new Date(messages[0].timestamp)
    const last = new Date(messages[messages.length - 1].timestamp)
    const diffMs = last.getTime() - first.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    
    if (diffMins < 1) return 'Less than 1 minute'
    if (diffMins < 60) return `${diffMins} minutes`
    
    const hours = Math.floor(diffMins / 60)
    return `${hours} hour${hours > 1 ? 's' : ''}`
  }

  private truncateMessage(content: string, maxLength: number): string {
    if (content.length <= maxLength) return content
    return content.substring(0, maxLength) + '...'
  }

  private furtherCompress(text: string): string {
    // More aggressive compression - keep only essential information
    const lines = text.split('\n')
    const essential = lines.filter(line => 
      line.includes('===') || 
      line.includes('•') || 
      line.includes('user:') || 
      line.includes('assistant:')
    )
    
    return essential.slice(0, 15).join('\n') // Limit to 15 lines
  }

  private extractTopics(messages: ConversationMessage[]): string[] {
    const topics = new Set<string>()
    
    for (const message of messages) {
      if (message.role === 'user' && message.content) {
        const content = message.content.toLowerCase()
        
        if (content.includes('sleep')) topics.add('sleep')
        if (content.includes('eat') || content.includes('food')) topics.add('nutrition')
        if (content.includes('sick') || content.includes('fever')) topics.add('health')
        if (content.includes('behavior') || content.includes('tantrum')) topics.add('behavior')
        if (content.includes('development') || content.includes('milestone')) topics.add('development')
        if (content.includes('doctor') || content.includes('appointment')) topics.add('medical care')
        if (content.includes('school') || content.includes('daycare')) topics.add('education')
      }
    }
    
    return Array.from(topics)
  }

  private extractKeyOutcomes(messages: ConversationMessage[]): string[] {
    const outcomes: string[] = []
    
    for (const message of messages) {
      if (message.type === 'email_sent') {
        outcomes.push('Information summary sent via email')
      }
      
      if (message.type === 'location_search') {
        outcomes.push('Healthcare providers located and shared')
      }
      
      if (message.role === 'assistant' && message.content.includes('schedule')) {
        outcomes.push('Scheduling advice provided')
      }
    }
    
    return outcomes.slice(0, 3)
  }

  private extractFollowUpNeeds(messages: ConversationMessage[]): string[] {
    const followUps: string[] = []
    
    // Look for unresolved questions or requests for follow-up
    for (const message of messages) {
      if (message.role === 'user') {
        const content = message.content.toLowerCase()
        
        if (content.includes('follow up') || content.includes('check back')) {
          followUps.push('User requested follow-up')
        }
        
        if (content.includes('appointment') && !content.includes('scheduled')) {
          followUps.push('May need help scheduling appointment')
        }
        
        if (content.includes('worried') || content.includes('concern')) {
          followUps.push('User expressed concern - may need reassurance')
        }
      }
    }
    
    return followUps.slice(0, 3)
  }

  private extractFirstSentence(text: string): string {
    const match = text.match(/[^.!?]*[.!?]/)
    return match ? match[0].trim() : text.substring(0, 100) + '...'
  }

  private getLastConversationTime(messages: ConversationMessage[]): string {
    if (messages.length === 0) return new Date().toISOString()
    
    const sorted = [...messages].sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    )
    
    return sorted[0].timestamp
  }

  private getTimeSinceLastConversation(lastTime: string): string {
    const now = new Date()
    const last = new Date(lastTime)
    const diffMs = now.getTime() - last.getTime()
    const diffMins = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (diffMins < 5) return 'Just a moment ago!'
    if (diffMins < 60) return `It's been ${diffMins} minutes since we last chatted.`
    if (diffHours < 24) return `It's been ${diffHours} hour${diffHours > 1 ? 's' : ''} since our last conversation.`
    if (diffDays === 1) return 'It\'s been about a day since we last talked.'
    if (diffDays < 7) return `It's been ${diffDays} days since our last conversation.`
    
    return `It's been a while since we last chatted!`
  }
}

// Export singleton instance
export const sessionManager = new SessionManager()

/**
 * Helper function to create a session resume prompt
 */
export function createSessionResumePrompt(
  userContext: UserContextData,
  conversationSummary: string
): string {
  const basePrompt = `
RETURNING USER SESSION - PERSONALIZED CONTEXT:

USER: ${userContext.user.name}
CHILDREN: ${userContext.family_members.map(m => `${m.name} (${m.age})`).join(', ')}
FAMILY CONCERNS: ${userContext.family_summary.primary_concerns.join(', ') || 'None'}

${conversationSummary ? `RECENT CONVERSATION HISTORY:\n${conversationSummary}\n` : ''}

INSTRUCTIONS:
- Greet the user by name warmly
- Reference their children by name when appropriate
- Consider any ongoing concerns or previous discussions
- Offer to continue where you left off or help with new questions
- Maintain continuity with previous conversations when relevant
`

  return basePrompt
}