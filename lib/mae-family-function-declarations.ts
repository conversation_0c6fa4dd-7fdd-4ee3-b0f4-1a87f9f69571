/**
 * Mae Family Function Tool Declarations
 * 
 * Function tool declarations for Gemini Live API that allow <PERSON> to interact 
 * with user and family data through voice commands.
 */

import type { FunctionDeclaration } from '@google/genai'

export const maeFamilyFunctionDeclarations: FunctionDeclaration[] = [
  {
    name: 'get_family_list',
    description: 'Get the user\'s complete family information including children and family members with their ages, medical conditions, and other details. Use this to understand the user\'s family context before providing advice.',
    parameters: {
      type: 'object',
      properties: {},
      required: []
    }
  },
  {
    name: 'add_family_member',
    description: 'Add a new family member (child or other family member) to the user\'s family. Use this when the user wants to add information about a new child, sibling, or family member.',
    parameters: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          description: 'The full name of the family member'
        },
        date_of_birth: {
          type: 'string',
          description: 'Date of birth in YYYY-MM-DD format (e.g., 2020-03-15)'
        },
        gender: {
          type: 'string',
          enum: ['male', 'female', 'other', 'prefer_not_to_say'],
          description: 'Gender of the family member'
        },
        relationship: {
          type: 'string',
          description: 'Relationship to the user (e.g., child, stepchild, son, daughter, sibling, etc.)'
        },
        medical_conditions: {
          type: 'array',
          items: { type: 'string' },
          description: 'List of medical conditions or diagnoses (e.g., ["asthma", "food allergies"])'
        },
        allergies: {
          type: 'array',
          items: { type: 'string' },
          description: 'List of allergies (e.g., ["peanuts", "shellfish", "pollen"])'
        },
        additional_notes: {
          type: 'string',
          description: 'Any additional notes about the family member\'s health or care needs'
        },
        is_primary: {
          type: 'boolean',
          description: 'Whether this is the primary child/family member (usually the main focus of care)'
        }
      },
      required: ['name', 'date_of_birth', 'relationship']
    }
  },
  {
    name: 'update_family_member',
    description: 'Update information about an existing family member. Use this when the user wants to modify or add details about a child or family member.',
    parameters: {
      type: 'object',
      properties: {
        member_id: {
          type: 'string',
          description: 'The unique ID of the family member to update (get this from get_family_list first)'
        },
        name: {
          type: 'string',
          description: 'Updated full name of the family member'
        },
        date_of_birth: {
          type: 'string',
          description: 'Updated date of birth in YYYY-MM-DD format'
        },
        gender: {
          type: 'string',
          enum: ['male', 'female', 'other', 'prefer_not_to_say'],
          description: 'Updated gender of the family member'
        },
        relationship: {
          type: 'string',
          description: 'Updated relationship to the user'
        },
        medical_conditions: {
          type: 'array',
          items: { type: 'string' },
          description: 'Updated list of medical conditions'
        },
        allergies: {
          type: 'array',
          items: { type: 'string' },
          description: 'Updated list of allergies'
        },
        additional_notes: {
          type: 'string',
          description: 'Updated additional notes'
        },
        is_primary: {
          type: 'boolean',
          description: 'Whether this should be the primary family member'
        }
      },
      required: ['member_id']
    }
  },
  {
    name: 'search_family_members',
    description: 'Search for family members by name or relationship. Use this to quickly find specific family members when the user mentions them by name.',
    parameters: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'Search query - can be a name, partial name, or relationship (e.g., "Emma", "daughter", "son")'
        }
      },
      required: ['query']
    }
  },
  {
    name: 'get_family_member_details',
    description: 'Get detailed information about a specific family member including calculated age, medical history, and care notes.',
    parameters: {
      type: 'object',
      properties: {
        member_id: {
          type: 'string',
          description: 'The unique ID of the family member (get this from get_family_list or search_family_members)'
        }
      },
      required: ['member_id']
    }
  },
  {
    name: 'update_user_info',
    description: 'Update the user\'s personal information like contact details, emergency contacts, or preferences.',
    parameters: {
      type: 'object',
      properties: {
        name: {
          type: 'string',
          description: 'Updated user name'
        },
        phone: {
          type: 'string',
          description: 'Updated phone number'
        },
        zip: {
          type: 'string',
          description: 'Updated ZIP/postal code'
        },
        emergency_contact: {
          type: 'object',
          properties: {
            name: { type: 'string', description: 'Emergency contact name' },
            phone: { type: 'string', description: 'Emergency contact phone number' },
            relationship: { type: 'string', description: 'Emergency contact relationship (e.g., spouse, parent, sibling)' }
          },
          description: 'Emergency contact information'
        },
        preferences: {
          type: 'object',
          description: 'User preferences (notifications, language, etc.)'
        }
      },
      required: []
    }
  }
]

/**
 * Handle Mae family function calls
 */
export async function handleMaeFamilyFunction(functionName: string, args: any) {
  try {
    console.log('🔵 Mae family function call:', functionName, args)
    
    let response: Response
    
    switch (functionName) {
      case 'get_family_list':
        response = await fetch('/api/mae/family/list', {
          method: 'GET',
        })
        break
        
      case 'add_family_member':
        response = await fetch('/api/mae/family/add', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(args)
        })
        break
        
      case 'update_family_member':
        response = await fetch('/api/mae/family/update', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(args)
        })
        break
        
      case 'search_family_members':
        response = await fetch(`/api/mae/family/search?query=${encodeURIComponent(args.query)}`, {
          method: 'GET',
        })
        break
        
      case 'get_family_member_details':
        response = await fetch(`/api/mae/family/details?member_id=${encodeURIComponent(args.member_id)}`, {
          method: 'GET',
        })
        break
        
      case 'update_user_info':
        response = await fetch('/api/mae/user/update', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(args)
        })
        break
        
      default:
        console.error('❌ Unknown Mae family function:', functionName)
        return {
          success: false,
          error: `Unknown function: ${functionName}`
        }
    }
    
    if (!response.ok) {
      const errorData = await response.json()
      console.error('❌ Mae family function API error:', errorData)
      return {
        success: false,
        error: errorData.error || `API call failed with status ${response.status}`
      }
    }
    
    const result = await response.json()
    console.log('✅ Mae family function result:', functionName, result)
    
    return result
    
  } catch (error) {
    console.error('❌ Mae family function error:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}