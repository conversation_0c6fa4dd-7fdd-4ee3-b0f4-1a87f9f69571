/**
 * AG-UI CustomEvents System for Voice-Driven Form Interactions
 * 
 * This module provides a robust event system for <PERSON> to interact with forms
 * via voice commands. Events are dispatched via the window object and handled
 * by onboarding components.
 */

// Event payload type definitions
export interface FillUserFormPayload {
  field: 'email' | 'name' | 'role' | 'phone' | 'zip' | 'date_of_birth' | 'emergency_contact'
  value: string | object
  validate?: boolean
}

export interface SubmitUserFormPayload {
  validate?: boolean
  skipValidation?: boolean
}

export interface AddFamilyMemberPayload {
  name: string
  date_of_birth: string
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say'
  relationship: string
  medical_conditions?: string[]
  allergies?: string[]
  medications?: {
    name: string
    dosage: string
    frequency: string
  }[]
  additional_notes?: string
  is_primary?: boolean
}

export interface UpdateFamilyMemberPayload {
  memberId: string
  updates: Partial<AddFamilyMemberPayload>
}

export interface ValidateFormPayload {
  formType: 'user' | 'family_member'
  data: any
  showErrors?: boolean
}

export interface OnboardingProgressPayload {
  step: 'welcome' | 'user_info' | 'family_info' | 'complete'
  data?: any
}

// Event response types
export interface EventResponse {
  success: boolean
  message?: string
  error?: string
  data?: any
  validationErrors?: string[]
}

// Custom event types
export interface AGUICustomEvent<T = any> extends CustomEvent {
  detail: {
    payload: T
    requestId: string
    timestamp: number
  }
}

// Event names as constants
export const AG_UI_EVENTS = {
  FILL_USER_FORM: 'fillUserForm',
  SUBMIT_USER_FORM: 'submitUserForm',
  ADD_FAMILY_MEMBER: 'addFamilyMember',
  UPDATE_FAMILY_MEMBER: 'updateFamilyMember',
  VALIDATE_FORM: 'validateForm',
  UPDATE_PROGRESS: 'updateOnboardingProgress',
  SHOW_CONFIRMATION: 'showConfirmation',
  CLEAR_FORM: 'clearForm',
  RESET_ONBOARDING: 'resetOnboarding'
} as const

// Event dispatcher class
export class AGUIEventDispatcher {
  private static instance: AGUIEventDispatcher
  private responseCallbacks: Map<string, (response: EventResponse) => void> = new Map()
  private timeout = 10000 // 10 seconds timeout for responses

  static getInstance(): AGUIEventDispatcher {
    if (!AGUIEventDispatcher.instance) {
      AGUIEventDispatcher.instance = new AGUIEventDispatcher()
    }
    return AGUIEventDispatcher.instance
  }

  private constructor() {
    // Listen for response events
    if (typeof window !== 'undefined') {
      window.addEventListener('agui-response', this.handleResponse.bind(this) as EventListener)
    }
  }

  private handleResponse(event: CustomEvent) {
    const { requestId, response } = event.detail
    const callback = this.responseCallbacks.get(requestId)
    
    if (callback) {
      callback(response)
      this.responseCallbacks.delete(requestId)
    }
  }

  private generateRequestId(): string {
    return `agui-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  private dispatchEvent<T>(eventName: string, payload: T): Promise<EventResponse> {
    return new Promise((resolve, reject) => {
      if (typeof window === 'undefined') {
        reject(new Error('Window not available - cannot dispatch events'))
        return
      }

      const requestId = this.generateRequestId()

      // Check if there are any listeners for this event
      const hasListeners = this.hasEventListeners(eventName)
      if (!hasListeners) {
        console.warn(`🎯 No listeners found for event: ${eventName}`)
        reject(new Error(`No listeners registered for event: ${eventName}`))
        return
      }

      // Set up timeout
      const timeoutId = setTimeout(() => {
        this.responseCallbacks.delete(requestId)
        reject(new Error(`Event ${eventName} timed out after ${this.timeout}ms`))
      }, this.timeout)

      // Set up response callback with timeout clearing
      this.responseCallbacks.set(requestId, (response) => {
        clearTimeout(timeoutId)
        resolve(response)
      })

      // Dispatch the event
      const customEvent = new CustomEvent(eventName, {
        detail: {
          payload,
          requestId,
          timestamp: Date.now()
        }
      })

      console.log(`🎯 Dispatching AG-UI event: ${eventName}`, { payload, requestId })
      window.dispatchEvent(customEvent)
    })
  }

  private hasEventListeners(eventName: string): boolean {
    if (typeof window === 'undefined') return false

    // For now, assume listeners exist if we're in a browser environment
    // A more sophisticated implementation would track registered listeners
    return true
  }

  // Event dispatcher methods
  async fillUserForm(payload: FillUserFormPayload): Promise<EventResponse> {
    return this.dispatchEvent(AG_UI_EVENTS.FILL_USER_FORM, payload)
  }

  async submitUserForm(payload: SubmitUserFormPayload = {}): Promise<EventResponse> {
    return this.dispatchEvent(AG_UI_EVENTS.SUBMIT_USER_FORM, payload)
  }

  async addFamilyMember(payload: AddFamilyMemberPayload): Promise<EventResponse> {
    return this.dispatchEvent(AG_UI_EVENTS.ADD_FAMILY_MEMBER, payload)
  }

  async updateFamilyMember(payload: UpdateFamilyMemberPayload): Promise<EventResponse> {
    return this.dispatchEvent(AG_UI_EVENTS.UPDATE_FAMILY_MEMBER, payload)
  }

  async validateForm(payload: ValidateFormPayload): Promise<EventResponse> {
    return this.dispatchEvent(AG_UI_EVENTS.VALIDATE_FORM, payload)
  }

  async updateProgress(payload: OnboardingProgressPayload): Promise<EventResponse> {
    return this.dispatchEvent(AG_UI_EVENTS.UPDATE_PROGRESS, payload)
  }

  async showConfirmation(payload: { message: string; type?: 'success' | 'error' | 'info' }): Promise<EventResponse> {
    return this.dispatchEvent(AG_UI_EVENTS.SHOW_CONFIRMATION, payload)
  }

  async clearForm(payload: { formType: 'user' | 'family_member' }): Promise<EventResponse> {
    return this.dispatchEvent(AG_UI_EVENTS.CLEAR_FORM, payload)
  }

  async resetOnboarding(): Promise<EventResponse> {
    return this.dispatchEvent(AG_UI_EVENTS.RESET_ONBOARDING, {})
  }
}

// Event listener helper class
export class AGUIEventListener {
  private listeners: Map<string, (event: AGUICustomEvent) => void> = new Map()

  constructor() {
    if (typeof window === 'undefined') {
      console.warn('AGUIEventListener: Window not available')
      return
    }
  }

  private sendResponse(requestId: string, response: EventResponse) {
    if (typeof window !== 'undefined') {
      const responseEvent = new CustomEvent('agui-response', {
        detail: { requestId, response }
      })
      window.dispatchEvent(responseEvent)
    }
  }

  addEventListener<T>(eventName: string, handler: (payload: T, requestId: string) => Promise<EventResponse> | EventResponse) {
    if (typeof window === 'undefined') return

    const listener = async (event: AGUICustomEvent) => {
      console.log(`🎯 Raw event received for ${eventName}:`, event)
      console.log(`🎯 Event detail:`, event.detail)

      const { payload, requestId } = event.detail || {}
      console.log(`🎯 Received AG-UI event: ${eventName}`, { payload, requestId, hasDetail: !!event.detail })

      // Add additional safety checks
      if (!requestId) {
        console.error(`🎯 No requestId for event: ${eventName}`, event.detail)
        return
      }

      try {
        const response = await handler(payload, requestId)
        this.sendResponse(requestId, response)
      } catch (error) {
        console.error(`Error handling ${eventName}:`, error)
        console.error(`Payload was:`, payload)
        console.error(`RequestId was:`, requestId)
        this.sendResponse(requestId, {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    this.listeners.set(eventName, listener)
    window.addEventListener(eventName, listener as unknown as EventListener)
    console.log(`🎯 Registered AG-UI event listener: ${eventName}`)
  }

  removeEventListener(eventName: string) {
    if (typeof window === 'undefined') return

    const listener = this.listeners.get(eventName)
    if (listener) {
      window.removeEventListener(eventName, listener as EventListener)
      this.listeners.delete(eventName)
      console.log(`🎯 Removed AG-UI event listener: ${eventName}`)
    }
  }

  removeAllListeners() {
    if (typeof window === 'undefined') return

    this.listeners.forEach((listener, eventName) => {
      window.removeEventListener(eventName, listener as EventListener)
    })
    this.listeners.clear()
    console.log('🎯 Removed all AG-UI event listeners')
  }
}

// Validation helpers
export class OnboardingValidator {
  static validateUserData(data: any): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!data.email) {
      errors.push('Email is required')
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
      errors.push('Please provide a valid email address')
    }

    if (!data.name || data.name.trim().length === 0) {
      errors.push('Name is required')
    }

    if (data.phone && !/^\+?[\d\s\-\(\)]+$/.test(data.phone)) {
      errors.push('Please provide a valid phone number')
    }

    if (data.zip && !/^\d{5}(-\d{4})?$/.test(data.zip)) {
      errors.push('Please provide a valid ZIP code')
    }

    if (data.date_of_birth) {
      const birthDate = new Date(data.date_of_birth)
      const today = new Date()
      const age = today.getFullYear() - birthDate.getFullYear()
      
      if (birthDate > today) {
        errors.push('Date of birth cannot be in the future')
      } else if (age < 18) {
        errors.push('You must be at least 18 years old to register')
      } else if (age > 120) {
        errors.push('Please provide a valid date of birth')
      }
    }

    return { valid: errors.length === 0, errors }
  }

  static validateFamilyMemberData(data: any): { valid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!data.name || data.name.trim().length === 0) {
      errors.push('Name is required')
    }

    if (!data.date_of_birth || data.date_of_birth.trim() === '') {
      errors.push('Date of birth is required')
    } else {
      const birthDate = new Date(data.date_of_birth)
      const today = new Date()

      if (isNaN(birthDate.getTime())) {
        errors.push('Please provide a valid date of birth')
      } else if (birthDate > today) {
        errors.push('Date of birth cannot be in the future')
      } else if (birthDate < new Date('1900-01-01')) {
        errors.push('Please provide a valid date of birth')
      }
    }

    if (!data.relationship || data.relationship.trim().length === 0) {
      errors.push('Relationship is required')
    }

    return { valid: errors.length === 0, errors }
  }

  static parseVoiceInput(input: string, field: string): any {
    const lowerInput = input.toLowerCase().trim()

    switch (field) {
      case 'email':
        // Handle voice input like "my email is john at gmail dot com"
        return lowerInput
          .replace(/\s+at\s+/g, '@')
          .replace(/\s+dot\s+/g, '.')
          .replace(/\s+/g, '')

      case 'phone':
        // Handle voice input like "my phone number is five five five one two three four"
        const phoneMap: { [key: string]: string } = {
          'zero': '0', 'one': '1', 'two': '2', 'three': '3', 'four': '4',
          'five': '5', 'six': '6', 'seven': '7', 'eight': '8', 'nine': '9'
        }
        
        let phone = lowerInput
        Object.entries(phoneMap).forEach(([word, digit]) => {
          phone = phone.replace(new RegExp(word, 'g'), digit)
        })
        
        return phone.replace(/[^\d]/g, '')

      case 'date_of_birth':
        // Handle various date formats from voice
        const datePatterns = [
          /(\d{1,2})[\/\-\s](\d{1,2})[\/\-\s](\d{4})/,  // MM/DD/YYYY
          /(\d{4})[\/\-\s](\d{1,2})[\/\-\s](\d{1,2})/,  // YYYY/MM/DD
          /(january|february|march|april|may|june|july|august|september|october|november|december)\s+(\d{1,2})\s+(\d{4})/i
        ]

        // First try to parse the input directly as a date
        try {
          const directDate = new Date(input)
          if (!isNaN(directDate.getTime())) {
            return directDate.toISOString().split('T')[0]
          }
        } catch {
          // Continue to pattern matching
        }

        // Try pattern matching
        for (const pattern of datePatterns) {
          const match = lowerInput.match(pattern)
          if (match) {
            try {
              let dateStr = ''
              if (pattern.source.includes('january')) {
                // Month name format: "january 15 2020"
                const monthNames = ['january', 'february', 'march', 'april', 'may', 'june',
                                  'july', 'august', 'september', 'october', 'november', 'december']
                const monthIndex = monthNames.indexOf(match[1].toLowerCase()) + 1
                dateStr = `${match[3]}-${monthIndex.toString().padStart(2, '0')}-${match[2].padStart(2, '0')}`
              } else if (match[1].length === 4) {
                // YYYY/MM/DD format
                dateStr = `${match[1]}-${match[2].padStart(2, '0')}-${match[3].padStart(2, '0')}`
              } else {
                // MM/DD/YYYY format
                dateStr = `${match[3]}-${match[1].padStart(2, '0')}-${match[2].padStart(2, '0')}`
              }

              const date = new Date(dateStr)
              if (!isNaN(date.getTime())) {
                return date.toISOString().split('T')[0]
              }
            } catch {
              // Continue to next pattern
            }
          }
        }

        // If no pattern matches, return the original input (might be empty)
        return input || ''

      case 'gender':
        if (lowerInput.includes('boy') || lowerInput.includes('male')) return 'male'
        if (lowerInput.includes('girl') || lowerInput.includes('female')) return 'female'
        if (lowerInput.includes('other')) return 'other'
        if (lowerInput.includes('prefer not')) return 'prefer_not_to_say'
        return input

      case 'relationship':
        if (lowerInput.includes('son') || lowerInput.includes('boy')) return 'child'
        if (lowerInput.includes('daughter') || lowerInput.includes('girl')) return 'child'
        if (lowerInput.includes('step')) return 'stepchild'
        if (lowerInput.includes('grand')) return 'grandchild'
        return input

      default:
        return input
    }
  }
}

// Export singleton instance
export const aguiEventDispatcher = AGUIEventDispatcher.getInstance()
export const aguiEventListener = new AGUIEventListener()
export const onboardingValidator = new OnboardingValidator()