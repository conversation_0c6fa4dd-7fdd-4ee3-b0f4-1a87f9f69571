/**
 * Mae Agent - AG-UI Protocol Implementation
 * 
 * This implements <PERSON> as a proper AG-UI agent that follows the Agent User Interaction Protocol.
 * <PERSON> is the AI pediatric health coach that guides users through onboarding and provides
 * healthcare information with voice-driven form interactions.
 */

import { Observable } from 'rxjs'

// AG-UI Protocol Types (simplified for this implementation)
export interface RunAgentInput {
  threadId: string
  runId: string
  state: any
  messages: Message[]
  tools: Tool[]
  context: Context[]
  forwardedProps: any
}

export interface Message {
  id: string
  role: 'user' | 'assistant' | 'system' | 'tool'
  content: string
  toolCalls?: ToolCall[]
}

export interface ToolCall {
  id: string
  type: 'function'
  function: {
    name: string
    arguments: string
  }
}

export interface Tool {
  name: string
  description: string
  parameters: any // JSON Schema
}

export interface Context {
  description: string
  value: string
}

// AG-UI Event Types
export enum EventType {
  RUN_STARTED = 'RUN_STARTED',
  RUN_FINISHED = 'RUN_FINISHED',
  RUN_ERROR = 'RUN_ERROR',
  TEXT_MESSAGE_START = 'TEXT_MESSAGE_START',
  TEXT_MESSAGE_CONTENT = 'TEXT_MESSAGE_CONTENT',
  TEXT_MESSAGE_END = 'TEXT_MESSAGE_END',
  TOOL_CALL_START = 'TOOL_CALL_START',
  TOOL_CALL_ARGS = 'TOOL_CALL_ARGS',
  TOOL_CALL_END = 'TOOL_CALL_END',
  TOOL_CALL_RESULT = 'TOOL_CALL_RESULT',
  STATE_SNAPSHOT = 'STATE_SNAPSHOT',
  STATE_DELTA = 'STATE_DELTA',
  MESSAGES_SNAPSHOT = 'MESSAGES_SNAPSHOT'
}

export interface BaseEvent {
  type: EventType
  timestamp?: number
}

export interface RunStartedEvent extends BaseEvent {
  type: EventType.RUN_STARTED
  threadId: string
  runId: string
}

export interface RunFinishedEvent extends BaseEvent {
  type: EventType.RUN_FINISHED
  threadId: string
  runId: string
  result?: any
}

export interface TextMessageStartEvent extends BaseEvent {
  type: EventType.TEXT_MESSAGE_START
  messageId: string
  role: 'assistant'
}

export interface TextMessageContentEvent extends BaseEvent {
  type: EventType.TEXT_MESSAGE_CONTENT
  messageId: string
  delta: string
}

export interface TextMessageEndEvent extends BaseEvent {
  type: EventType.TEXT_MESSAGE_END
  messageId: string
}

export interface ToolCallStartEvent extends BaseEvent {
  type: EventType.TOOL_CALL_START
  toolCallId: string
  toolCallName: string
  parentMessageId?: string
}

export interface ToolCallArgsEvent extends BaseEvent {
  type: EventType.TOOL_CALL_ARGS
  toolCallId: string
  delta: string
}

export interface ToolCallEndEvent extends BaseEvent {
  type: EventType.TOOL_CALL_END
  toolCallId: string
}

export interface ToolCallResultEvent extends BaseEvent {
  type: EventType.TOOL_CALL_RESULT
  messageId: string
  toolCallId: string
  content: string
  role?: 'tool'
}

export interface StateSnapshotEvent extends BaseEvent {
  type: EventType.STATE_SNAPSHOT
  snapshot: any
}

export interface MessagesSnapshotEvent extends BaseEvent {
  type: EventType.MESSAGES_SNAPSHOT
  messages: Message[]
}

export type AgentEvent = 
  | RunStartedEvent
  | RunFinishedEvent
  | TextMessageStartEvent
  | TextMessageContentEvent
  | TextMessageEndEvent
  | ToolCallStartEvent
  | ToolCallArgsEvent
  | ToolCallEndEvent
  | ToolCallResultEvent
  | StateSnapshotEvent
  | MessagesSnapshotEvent

// Abstract Agent Base Class
export abstract class AbstractAgent {
  protected messages: Message[] = []
  protected state: any = {}
  
  constructor(
    public agentId: string,
    public threadId: string,
    initialMessages: Message[] = [],
    initialState: any = {}
  ) {
    this.messages = initialMessages
    this.state = initialState
  }

  abstract run(input: RunAgentInput): () => Observable<AgentEvent>

  runAgent(params: {
    runId?: string
    tools?: Tool[]
    context?: Context[]
    forwardedProps?: any
  } = {}): Observable<AgentEvent> {
    const runId = params.runId || `run_${Date.now()}`
    
    const input: RunAgentInput = {
      threadId: this.threadId,
      runId,
      state: this.state,
      messages: this.messages,
      tools: params.tools || [],
      context: params.context || [],
      forwardedProps: params.forwardedProps || {}
    }

    return this.run(input)()
  }
}

// Mae Agent Implementation
export class MaeAgent extends AbstractAgent {
  private geminiApiKey: string

  constructor(
    agentId: string = 'mae-agent',
    threadId: string = `thread_${Date.now()}`,
    geminiApiKey?: string
  ) {
    super(agentId, threadId)
    this.geminiApiKey = geminiApiKey || process.env.GEMINI_API_KEY || ''
    
    // Initialize Mae's system message
    this.messages = [{
      id: 'system_1',
      role: 'system',
      content: `You are Mae, a warm, friendly digital pediatric health coach with clinical experience. 
      You help families with onboarding to the Our Kidz platform through natural conversation.
      
      When helping with onboarding:
      1. Guide users through collecting their information step by step
      2. Use the provided tools to fill out forms as you collect information
      3. Be conversational and reassuring
      4. Always validate information before proceeding
      5. Use Google Search to provide evidence-based health information when relevant
      
      You have access to tools for:
      - Collecting user information (collect_user_information)
      - Adding family members (add_family_member)
      - Updating onboarding progress (update_onboarding_step)
      - Completing the onboarding process (complete_onboarding)
      
      Always be helpful, warm, and professional.`
    }]
  }

  public run(input: RunAgentInput): () => Observable<AgentEvent> {
    return () => new Observable<AgentEvent>((observer) => {
      this.processRun(input, observer)
    })
  }

  private async processRun(
    input: RunAgentInput, 
    observer: any
  ) {
    try {
      // Emit run started
      observer.next({
        type: EventType.RUN_STARTED,
        threadId: input.threadId,
        runId: input.runId,
        timestamp: Date.now()
      })

      // Update internal state
      this.state = input.state
      this.messages = input.messages

      // Get the latest user message
      const userMessages = input.messages.filter(m => m.role === 'user')
      const latestUserMessage = userMessages[userMessages.length - 1]

      if (latestUserMessage) {
        // Process the user's message and generate response
        await this.generateResponse(latestUserMessage, input, observer)
      } else {
        // Send initial greeting
        await this.sendGreeting(input, observer)
      }

      // Emit run finished
      observer.next({
        type: EventType.RUN_FINISHED,
        threadId: input.threadId,
        runId: input.runId,
        timestamp: Date.now()
      })

      observer.complete()

    } catch (error) {
      observer.error({
        type: EventType.RUN_ERROR,
        message: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now()
      })
    }
  }

  private async sendGreeting(input: RunAgentInput, observer: any) {
    const messageId = `msg_${Date.now()}`
    
    // Start message
    observer.next({
      type: EventType.TEXT_MESSAGE_START,
      messageId,
      role: 'assistant',
      timestamp: Date.now()
    })

    // Send greeting content
    const greeting = "Hello! I'm Mae, your AI pediatric health coach. I'm here to help you get set up with Our Kidz. Let's start by collecting some basic information about you and your family. What's your name?"

    // Stream the greeting
    for (let i = 0; i < greeting.length; i += 10) {
      const chunk = greeting.slice(i, i + 10)
      observer.next({
        type: EventType.TEXT_MESSAGE_CONTENT,
        messageId,
        delta: chunk,
        timestamp: Date.now()
      })
      
      // Small delay to simulate streaming
      await new Promise(resolve => setTimeout(resolve, 50))
    }

    // End message
    observer.next({
      type: EventType.TEXT_MESSAGE_END,
      messageId,
      timestamp: Date.now()
    })

    // Add message to conversation
    this.messages.push({
      id: messageId,
      role: 'assistant',
      content: greeting
    })
  }

  private async generateResponse(
    userMessage: Message,
    input: RunAgentInput,
    observer: any
  ) {
    // This is a simplified implementation
    // In a real implementation, you would integrate with Gemini API
    // and use the tools provided in input.tools
    
    const messageId = `msg_${Date.now()}`
    
    // Start message
    observer.next({
      type: EventType.TEXT_MESSAGE_START,
      messageId,
      role: 'assistant',
      timestamp: Date.now()
    })

    // Generate a simple response based on user input
    let response = "Thank you for that information. "
    
    // Check if we should use a tool
    if (userMessage.content.toLowerCase().includes('name')) {
      // Simulate tool call for collecting user info
      await this.simulateToolCall('collect_user_information', {
        field: 'name',
        value: userMessage.content,
        user_email: '<EMAIL>'
      }, observer)
      
      response += "I've recorded your name. Now, could you please tell me your email address?"
    } else {
      response += "I understand. Could you tell me a bit more about what you'd like help with today?"
    }

    // Stream the response
    for (let i = 0; i < response.length; i += 15) {
      const chunk = response.slice(i, i + 15)
      observer.next({
        type: EventType.TEXT_MESSAGE_CONTENT,
        messageId,
        delta: chunk,
        timestamp: Date.now()
      })
      
      await new Promise(resolve => setTimeout(resolve, 80))
    }

    // End message
    observer.next({
      type: EventType.TEXT_MESSAGE_END,
      messageId,
      timestamp: Date.now()
    })

    // Add message to conversation
    this.messages.push({
      id: messageId,
      role: 'assistant',
      content: response
    })
  }

  private async simulateToolCall(
    toolName: string,
    args: any,
    observer: any
  ) {
    const toolCallId = `tool_${Date.now()}`
    
    // Start tool call
    observer.next({
      type: EventType.TOOL_CALL_START,
      toolCallId,
      toolCallName: toolName,
      timestamp: Date.now()
    })

    // Stream arguments
    const argsString = JSON.stringify(args)
    for (let i = 0; i < argsString.length; i += 20) {
      const chunk = argsString.slice(i, i + 20)
      observer.next({
        type: EventType.TOOL_CALL_ARGS,
        toolCallId,
        delta: chunk,
        timestamp: Date.now()
      })
      
      await new Promise(resolve => setTimeout(resolve, 30))
    }

    // End tool call
    observer.next({
      type: EventType.TOOL_CALL_END,
      toolCallId,
      timestamp: Date.now()
    })

    // Simulate tool result
    observer.next({
      type: EventType.TOOL_CALL_RESULT,
      messageId: `msg_${Date.now()}`,
      toolCallId,
      content: JSON.stringify({ success: true, message: 'Information collected successfully' }),
      role: 'tool',
      timestamp: Date.now()
    })
  }
}
