// Debug utility to check environment variables
export function debugEnvironmentVariables() {
  console.log('🔍 Environment Variables Debug:')
  console.log('NODE_ENV:', process.env.NODE_ENV)
  console.log('NEXT_PUBLIC_SUPABASE_URL:', process.env.NEXT_PUBLIC_SUPABASE_URL ? '✅ Set' : '❌ Missing')
  console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing')
  console.log('SUPABASE_PROJECT_ID:', process.env.SUPABASE_PROJECT_ID ? '✅ Set' : '❌ Missing')
  console.log('SUPABASE_SERVICE_ROLE_KEY:', process.env.SUPABASE_SERVICE_ROLE_KEY ? '✅ Set' : '❌ Missing')
  
  // Check if we're in browser context
  if (typeof window !== 'undefined') {
    console.log('🌐 Running in browser context')
    console.log('  - SUPABASE_SERVICE_ROLE_KEY should NOT be available in browser')
    console.log('  - Only NEXT_PUBLIC_* variables are available in browser')
  } else {
    console.log('🖥️ Running in server context')
    console.log('  - All environment variables should be available')
  }
}

// Safe environment variable access
export function getEnvironmentVariable(name: string, required: boolean = true): string | undefined {
  const value = process.env[name]
  
  if (required && !value) {
    const context = typeof window !== 'undefined' ? 'browser' : 'server'
    throw new Error(`Environment variable ${name} is required but not set in ${context} context`)
  }
  
  return value
}

// Check if we're in a server context where service role key should be available
export function isServerContext(): boolean {
  return typeof window === 'undefined'
}

// Get Supabase configuration safely
export function getSupabaseConfig() {
  const config = {
    url: getEnvironmentVariable('NEXT_PUBLIC_SUPABASE_URL') || 
         `https://${getEnvironmentVariable('SUPABASE_PROJECT_ID')}.supabase.co`,
    anonKey: getEnvironmentVariable('NEXT_PUBLIC_SUPABASE_ANON_KEY', true),
    serviceRoleKey: isServerContext() ? getEnvironmentVariable('SUPABASE_SERVICE_ROLE_KEY', false) : undefined
  }
  
  return config
}