/**
 * Mae Contextual Insights Tools
 * 
 * Function tools that provide <PERSON> with contextual insights like seasonal health
 * reminders, developmental milestones, and personalized health tips based on
 * the user's family context.
 */

import { Type, FunctionDeclaration } from '@google/genai'

export const maeContextualInsightsFunctionDeclarations: FunctionDeclaration[] = [
  {
    name: 'get_seasonal_health_insights',
    description: 'Get current seasonal health insights and reminders relevant to the user\'s children and location. Use this to provide proactive, contextual health guidance.',
    parameters: {
      type: Type.OBJECT,
      properties: {
        user_location: {
          type: Type.STRING,
          description: 'User\'s ZIP code or location for regional health insights'
        },
        children_ages: {
          type: Type.ARRAY,
          items: { type: Type.NUMBER },
          description: 'Ages of the user\'s children to provide age-appropriate seasonal guidance'
        },
        known_conditions: {
          type: Type.ARRAY,
          items: { type: Type.STRING },
          description: 'Known medical conditions or allergies to consider for seasonal advice'
        }
      },
      required: []
    }
  },
  {
    name: 'get_developmental_milestones',
    description: 'Get age-appropriate developmental milestones and guidance for specific children. Use this to provide personalized developmental insights.',
    parameters: {
      type: Type.OBJECT,
      properties: {
        child_age: {
          type: Type.NUMBER,
          description: 'Age of the child in years'
        },
        child_name: {
          type: Type.STRING,
          description: 'Name of the child for personalized response'
        },
        focus_area: {
          type: Type.STRING,
          description: 'Specific developmental area of interest (physical, cognitive, social, emotional, language)'
        }
      },
      required: ['child_age']
    }
  },
  {
    name: 'get_personalized_health_tips',
    description: 'Generate personalized health tips based on the family\'s history, previous conversations, and current needs.',
    parameters: {
      type: Type.OBJECT,
      properties: {
        family_context: {
          type: Type.STRING,
          description: 'Summary of the family\'s health context and previous conversations'
        },
        current_concerns: {
          type: Type.ARRAY,
          items: { type: Type.STRING },
          description: 'Current health concerns or topics the family is dealing with'
        }
      },
      required: []
    }
  }
]

// Handler functions for the contextual insights tools
export async function handleGetSeasonalHealthInsights(args: {
  user_location?: string
  children_ages?: number[]
  known_conditions?: string[]
}) {
  try {
    console.log('🌟 Mae requesting seasonal health insights:', args)

    const currentMonth = new Date().getMonth() + 1 // 1-12
    const currentSeason = getSeason(currentMonth)
    
    const insights = {
      success: true,
      season: currentSeason,
      month: currentMonth,
      location: args.user_location || 'General',
      insights: generateSeasonalInsights(currentSeason, args.children_ages || [], args.known_conditions || []),
      regional_considerations: args.user_location ? getRegionalConsiderations(args.user_location, currentSeason) : [],
      proactive_reminders: getProactiveReminders(currentSeason, args.children_ages || [])
    }

    return insights

  } catch (error) {
    console.error('❌ Error getting seasonal health insights:', error)
    return {
      success: false,
      error: 'Failed to get seasonal health insights',
      message: 'Unable to retrieve seasonal health information at this time.'
    }
  }
}

export async function handleGetDevelopmentalMilestones(args: {
  child_age: number
  child_name?: string
  focus_area?: string
}) {
  try {
    console.log('🌟 Mae requesting developmental milestones:', args)

    const milestones = {
      success: true,
      child_name: args.child_name || 'your child',
      age: args.child_age,
      focus_area: args.focus_area || 'general',
      milestones: getDevelopmentalMilestones(args.child_age, args.focus_area),
      next_milestones: getUpcomingMilestones(args.child_age),
      encouragement: generateEncouragement(args.child_name, args.child_age),
      red_flags: getDevelopmentalRedFlags(args.child_age)
    }

    return milestones

  } catch (error) {
    console.error('❌ Error getting developmental milestones:', error)
    return {
      success: false,
      error: 'Failed to get developmental milestones',
      message: 'Unable to retrieve developmental information at this time.'
    }
  }
}

export async function handleGetPersonalizedHealthTips(args: {
  family_context?: string
  current_concerns?: string[]
}) {
  try {
    console.log('🌟 Mae requesting personalized health tips:', args)

    const tips = {
      success: true,
      personalized_tips: generatePersonalizedTips(args.family_context, args.current_concerns || []),
      preventive_care: getPreventiveCareReminders(),
      family_wellness: getFamilyWellnessTips(),
      follow_up_suggestions: getFollowUpSuggestions(args.current_concerns || [])
    }

    return tips

  } catch (error) {
    console.error('❌ Error getting personalized health tips:', error)
    return {
      success: false,
      error: 'Failed to get personalized health tips',
      message: 'Unable to generate personalized health tips at this time.'
    }
  }
}

// Helper functions
function getSeason(month: number): string {
  if (month >= 3 && month <= 5) return 'Spring'
  if (month >= 6 && month <= 8) return 'Summer'
  if (month >= 9 && month <= 11) return 'Fall'
  return 'Winter'
}

function generateSeasonalInsights(season: string, ages: number[], conditions: string[]): string[] {
  const insights: string[] = []
  
  switch (season) {
    case 'Winter':
      insights.push('Cold and flu season is in full swing - focus on hand hygiene and immune support')
      insights.push('Dry winter air can worsen eczema and respiratory conditions')
      if (ages.some(age => age < 5)) {
        insights.push('Young children are especially susceptible to RSV during winter months')
      }
      break
    case 'Spring':
      insights.push('Allergy season begins - prepare for pollen and seasonal allergies')
      insights.push('Great time for outdoor activities and vitamin D from sunshine')
      if (conditions.some(c => c.toLowerCase().includes('allergy'))) {
        insights.push('Monitor allergy symptoms closely as pollen counts rise')
      }
      break
    case 'Summer':
      insights.push('Sun safety is crucial - use sunscreen and stay hydrated')
      insights.push('Watch for heat-related illnesses during hot weather')
      insights.push('Swimming safety and ear infection prevention are important')
      break
    case 'Fall':
      insights.push('Back-to-school season brings increased exposure to germs')
      insights.push('Flu vaccination season - schedule annual flu shots')
      insights.push('Establish healthy sleep routines as school schedules begin')
      break
  }
  
  return insights
}

function getRegionalConsiderations(location: string, season: string): string[] {
  // This would ideally use real location data, but for now we'll provide general guidance
  return [
    'Check local pollen counts and air quality indexes',
    'Be aware of regional disease outbreaks or health advisories',
    'Consider local climate factors that may affect health'
  ]
}

function getProactiveReminders(season: string, ages: number[]): string[] {
  const reminders: string[] = []
  
  if (season === 'Fall' || season === 'Winter') {
    reminders.push('Schedule annual flu vaccinations')
    reminders.push('Stock up on hand sanitizer and tissues')
  }
  
  if (season === 'Spring' || season === 'Summer') {
    reminders.push('Update sunscreen supplies')
    reminders.push('Check outdoor play equipment for safety')
  }
  
  return reminders
}

function getDevelopmentalMilestones(age: number, focusArea?: string): string[] {
  const milestones: string[] = []
  
  if (age >= 0 && age <= 1) {
    milestones.push('Smiling, laughing, and making eye contact')
    milestones.push('Rolling over and sitting with support')
    milestones.push('Babbling and responding to their name')
  } else if (age >= 1 && age <= 2) {
    milestones.push('Walking independently')
    milestones.push('Saying first words and following simple instructions')
    milestones.push('Playing simple games like peek-a-boo')
  } else if (age >= 2 && age <= 3) {
    milestones.push('Running, jumping, and climbing')
    milestones.push('Speaking in 2-3 word phrases')
    milestones.push('Playing alongside other children')
  } else if (age >= 3 && age <= 5) {
    milestones.push('Pedaling a tricycle and drawing circles')
    milestones.push('Speaking in complete sentences')
    milestones.push('Playing cooperatively with others')
  } else if (age >= 5 && age <= 8) {
    milestones.push('Reading simple books and writing letters')
    milestones.push('Developing friendships and social skills')
    milestones.push('Understanding rules and following multi-step instructions')
  }
  
  return milestones
}

function getUpcomingMilestones(age: number): string[] {
  // Return milestones for the next age range
  return getDevelopmentalMilestones(age + 1)
}

function generateEncouragement(childName?: string, age?: number): string {
  const name = childName || 'your child'
  return `${name} is at such an exciting stage of development! Every child grows at their own pace, and you're doing a wonderful job supporting their growth.`
}

function getDevelopmentalRedFlags(age: number): string[] {
  const redFlags: string[] = []
  
  if (age >= 1 && age <= 2) {
    redFlags.push('Not walking by 18 months')
    redFlags.push('No words by 15 months')
  } else if (age >= 2 && age <= 3) {
    redFlags.push('Not speaking in 2-word phrases by 24 months')
    redFlags.push('Loss of previously acquired skills')
  }
  
  return redFlags
}

function generatePersonalizedTips(familyContext?: string, concerns?: string[]): string[] {
  const tips: string[] = []
  
  if (concerns.includes('sleep')) {
    tips.push('Establish consistent bedtime routines for better sleep quality')
  }
  
  if (concerns.includes('nutrition')) {
    tips.push('Focus on colorful fruits and vegetables for balanced nutrition')
  }
  
  tips.push('Regular family activities promote both physical and emotional health')
  tips.push('Open communication helps children express their feelings and concerns')
  
  return tips
}

function getPreventiveCareReminders(): string[] {
  return [
    'Schedule regular pediatric checkups',
    'Keep vaccinations up to date',
    'Maintain good dental hygiene habits',
    'Encourage regular physical activity'
  ]
}

function getFamilyWellnessTips(): string[] {
  return [
    'Family meals together promote healthy eating habits',
    'Limit screen time and encourage outdoor play',
    'Create calm, supportive home environments',
    'Model healthy behaviors for children to follow'
  ]
}

function getFollowUpSuggestions(concerns: string[]): string[] {
  const suggestions: string[] = []
  
  if (concerns.length > 0) {
    suggestions.push('Monitor progress and note any changes')
    suggestions.push('Keep a health journal to track patterns')
    suggestions.push('Schedule follow-up if concerns persist')
  }
  
  return suggestions
}
