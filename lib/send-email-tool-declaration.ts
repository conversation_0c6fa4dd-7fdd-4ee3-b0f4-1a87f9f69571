// ------------------------------------------------------------
//  Builds FunctionDeclarations (Gemini SDK) from the JSON
//  schemas defined in `send-email-function-tool.ts`.
// ------------------------------------------------------------
import {
    FunctionDeclaration,
    Schema,
  } from '@google/genai';
  import {
    sendEmailTool,
    checkSmtpTool,
    sendWelcomeEmailTool,
    sendContactConfirmationTool,
    sendAdminNotificationTool
  } from './send-email-function-tool';
  import { emailConversationTool } from './email-function-tool';
  import {
    emailConfirmationToolDeclaration,
    processConfirmationToolDeclaration
  } from './email-confirmation-tool';
  
  export const emailConversationToolDeclaration: FunctionDeclaration = {
    name: emailConversationTool.name,
    description: emailConversationTool.description,
    parameters: emailConversationTool.parameters as unknown as Schema, // cast OK – shapes match
  };
  
  export const sendEmailToolDeclaration: FunctionDeclaration = {
    name: sendEmailTool.name,
    description: sendEmailTool.description,
    parameters: sendEmailTool.parameters as unknown as Schema, // cast OK – shapes match
  };

  export const checkSmtpToolDeclaration: FunctionDeclaration = {
    name: checkSmtpTool.name,
    description: checkSmtpTool.description,
    parameters: checkSmtpTool.parameters as unknown as Schema, // cast OK – shapes match
  };

  export const sendWelcomeEmailToolDeclaration: FunctionDeclaration = {
    name: sendWelcomeEmailTool.name,
    description: sendWelcomeEmailTool.description,
    parameters: sendWelcomeEmailTool.parameters as unknown as Schema, // cast OK – shapes match
  };

  export const sendContactConfirmationToolDeclaration: FunctionDeclaration = {
    name: sendContactConfirmationTool.name,
    description: sendContactConfirmationTool.description,
    parameters: sendContactConfirmationTool.parameters as unknown as Schema, // cast OK – shapes match
  };

  export const sendAdminNotificationToolDeclaration: FunctionDeclaration = {
    name: sendAdminNotificationTool.name,
    description: sendAdminNotificationTool.description,
    parameters: sendAdminNotificationTool.parameters as unknown as Schema, // cast OK – shapes match
  };

  export const emailConfirmationToolDeclarationForGemini: FunctionDeclaration = {
    name: emailConfirmationToolDeclaration.name,
    description: emailConfirmationToolDeclaration.description,
    parameters: emailConfirmationToolDeclaration.parameters as unknown as Schema, // cast OK – shapes match
  };

  export const processConfirmationToolDeclarationForGemini: FunctionDeclaration = {
    name: processConfirmationToolDeclaration.name,
    description: processConfirmationToolDeclaration.description,
    parameters: processConfirmationToolDeclaration.parameters as unknown as Schema, // cast OK – shapes match
  };

  // Export all tools as arrays for convenience
  export const emailToolDeclarations: FunctionDeclaration[] = [
    sendEmailToolDeclaration,
    checkSmtpToolDeclaration,
    sendWelcomeEmailToolDeclaration,
    sendContactConfirmationToolDeclaration,
    sendAdminNotificationToolDeclaration,
    emailConfirmationToolDeclarationForGemini,
    processConfirmationToolDeclarationForGemini
  ];

  // Export template-based tools separately
  export const templateEmailToolDeclarations: FunctionDeclaration[] = [
    sendWelcomeEmailToolDeclaration,
    sendContactConfirmationToolDeclaration,
    sendAdminNotificationToolDeclaration
  ];

  // Export core tools separately
  export const coreEmailToolDeclarations: FunctionDeclaration[] = [
    sendEmailToolDeclaration,
    checkSmtpToolDeclaration
  ];
