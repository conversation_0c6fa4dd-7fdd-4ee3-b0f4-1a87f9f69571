/**
 * Mae Family Management Function Tools
 * 
 * These functions allow <PERSON> to interact with user and family data in the database.
 * They can be called by the Gemini Live API during voice conversations to:
 * - Add new family members
 * - Update existing family member information
 * - Retrieve family data for context
 * - Manage user preferences and settings
 */

import { createClient } from '@supabase/supabase-js'
import { auth, currentUser } from '@clerk/nextjs/server'

// Initialize Supabase client with service role for full access
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

/**
 * Get current user's context for Mae
 */
export async function getCurrentUserContext() {
  try {
    const clerkUser = await currentUser()
    if (!clerkUser) {
      return {
        success: false,
        error: 'User not authenticated',
        data: null
      }
    }

    // Get user from Supabase
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('*')
      .eq('clerk_id', clerkUser.id)
      .single()

    if (userError) {
      return {
        success: false,
        error: 'User not found in database',
        data: null
      }
    }

    // Get family members
    const { data: familyMembers, error: familyError } = await supabase
      .from('family_members')
      .select('*')
      .eq('user_id', user.id)
      .order('is_primary', { ascending: false })
      .order('date_of_birth', { ascending: false })

    return {
      success: true,
      data: {
        user,
        family_members: familyMembers || [],
        user_name: user.name,
        family_count: (familyMembers || []).length
      }
    }
  } catch (error) {
    console.error('❌ Error getting user context:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: null
    }
  }
}

/**
 * Add a new family member
 */
export async function addFamilyMember(familyMemberData: {
  name: string
  date_of_birth: string  // YYYY-MM-DD format
  gender?: string
  relationship: string
  medical_conditions?: string[]
  allergies?: string[]
  additional_notes?: string
  is_primary?: boolean
}) {
  try {
    const clerkUser = await currentUser()
    if (!clerkUser) {
      return {
        success: false,
        error: 'User not authenticated',
        data: null
      }
    }

    // Get user from Supabase
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', clerkUser.id)
      .single()

    if (userError) {
      return {
        success: false,
        error: 'User not found in database',
        data: null
      }
    }

    // Validate date of birth
    const dobDate = new Date(familyMemberData.date_of_birth)
    if (dobDate > new Date()) {
      return {
        success: false,
        error: 'Date of birth cannot be in the future',
        data: null
      }
    }

    // If this is being set as primary, unset other primary members
    if (familyMemberData.is_primary) {
      await supabase
        .from('family_members')
        .update({ is_primary: false })
        .eq('user_id', user.id)
    }

    // Add the family member
    const { data: newMember, error: insertError } = await supabase
      .from('family_members')
      .insert({
        user_id: user.id,
        name: familyMemberData.name,
        date_of_birth: familyMemberData.date_of_birth,
        gender: familyMemberData.gender,
        relationship: familyMemberData.relationship,
        medical_conditions: familyMemberData.medical_conditions || [],
        allergies: familyMemberData.allergies || [],
        additional_notes: familyMemberData.additional_notes,
        is_primary: familyMemberData.is_primary || false
      })
      .select()
      .single()

    if (insertError) {
      return {
        success: false,
        error: `Failed to add family member: ${insertError.message}`,
        data: null
      }
    }

    console.log('✅ Added family member:', newMember.name)
    
    return {
      success: true,
      message: `Successfully added ${newMember.name} to your family`,
      data: newMember
    }
  } catch (error) {
    console.error('❌ Error adding family member:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: null
    }
  }
}

/**
 * Update existing family member
 */
export async function updateFamilyMember(
  memberId: string,
  updates: {
    name?: string
    date_of_birth?: string
    gender?: string
    relationship?: string
    medical_conditions?: string[]
    allergies?: string[]
    additional_notes?: string
    is_primary?: boolean
  }
) {
  try {
    const clerkUser = await currentUser()
    if (!clerkUser) {
      return {
        success: false,
        error: 'User not authenticated',
        data: null
      }
    }

    // Get user from Supabase
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', clerkUser.id)
      .single()

    if (userError) {
      return {
        success: false,
        error: 'User not found in database',
        data: null
      }
    }

    // Verify the family member belongs to this user
    const { data: existingMember, error: memberError } = await supabase
      .from('family_members')
      .select('*')
      .eq('id', memberId)
      .eq('user_id', user.id)
      .single()

    if (memberError || !existingMember) {
      return {
        success: false,
        error: 'Family member not found or access denied',
        data: null
      }
    }

    // If setting as primary, unset other primary members
    if (updates.is_primary) {
      await supabase
        .from('family_members')
        .update({ is_primary: false })
        .eq('user_id', user.id)
        .neq('id', memberId)
    }

    // Update the family member
    const { data: updatedMember, error: updateError } = await supabase
      .from('family_members')
      .update(updates)
      .eq('id', memberId)
      .eq('user_id', user.id)
      .select()
      .single()

    if (updateError) {
      return {
        success: false,
        error: `Failed to update family member: ${updateError.message}`,
        data: null
      }
    }

    console.log('✅ Updated family member:', updatedMember.name)
    
    return {
      success: true,
      message: `Successfully updated ${updatedMember.name}'s information`,
      data: updatedMember
    }
  } catch (error) {
    console.error('❌ Error updating family member:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: null
    }
  }
}

/**
 * Remove a family member
 */
export async function removeFamilyMember(memberId: string) {
  try {
    const clerkUser = await currentUser()
    if (!clerkUser) {
      return {
        success: false,
        error: 'User not authenticated',
        data: null
      }
    }

    // Get user from Supabase
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', clerkUser.id)
      .single()

    if (userError) {
      return {
        success: false,
        error: 'User not found in database',
        data: null
      }
    }

    // Get the member name before deleting
    const { data: memberToDelete, error: memberError } = await supabase
      .from('family_members')
      .select('name')
      .eq('id', memberId)
      .eq('user_id', user.id)
      .single()

    if (memberError || !memberToDelete) {
      return {
        success: false,
        error: 'Family member not found or access denied',
        data: null
      }
    }

    // Delete the family member
    const { error: deleteError } = await supabase
      .from('family_members')
      .delete()
      .eq('id', memberId)
      .eq('user_id', user.id)

    if (deleteError) {
      return {
        success: false,
        error: `Failed to remove family member: ${deleteError.message}`,
        data: null
      }
    }

    console.log('✅ Removed family member:', memberToDelete.name)
    
    return {
      success: true,
      message: `Successfully removed ${memberToDelete.name} from your family`,
      data: { removed_member: memberToDelete.name }
    }
  } catch (error) {
    console.error('❌ Error removing family member:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: null
    }
  }
}

/**
 * Update user information
 */
export async function updateUserInfo(updates: {
  name?: string
  phone?: string
  zip?: string
  preferences?: any
  emergency_contact?: {
    name: string
    phone: string
    relationship: string
  }
}) {
  try {
    const clerkUser = await currentUser()
    if (!clerkUser) {
      return {
        success: false,
        error: 'User not authenticated',
        data: null
      }
    }

    // Update user in Supabase
    const { data: updatedUser, error: updateError } = await supabase
      .from('users')
      .update(updates)
      .eq('clerk_id', clerkUser.id)
      .select()
      .single()

    if (updateError) {
      return {
        success: false,
        error: `Failed to update user information: ${updateError.message}`,
        data: null
      }
    }

    console.log('✅ Updated user info for:', updatedUser.name)
    
    return {
      success: true,
      message: `Successfully updated your information`,
      data: updatedUser
    }
  } catch (error) {
    console.error('❌ Error updating user info:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: null
    }
  }
}

/**
 * Search family members by name or relationship
 */
export async function searchFamilyMembers(query: string) {
  try {
    const clerkUser = await currentUser()
    if (!clerkUser) {
      return {
        success: false,
        error: 'User not authenticated',
        data: null
      }
    }

    // Get user from Supabase
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', clerkUser.id)
      .single()

    if (userError) {
      return {
        success: false,
        error: 'User not found in database',
        data: null
      }
    }

    // Search family members by name or relationship
    const { data: familyMembers, error: searchError } = await supabase
      .from('family_members')
      .select('*')
      .eq('user_id', user.id)
      .or(`name.ilike.%${query}%,relationship.ilike.%${query}%`)
      .order('is_primary', { ascending: false })
      .order('date_of_birth', { ascending: false })

    if (searchError) {
      return {
        success: false,
        error: `Failed to search family members: ${searchError.message}`,
        data: null
      }
    }

    return {
      success: true,
      data: familyMembers || [],
      message: `Found ${(familyMembers || []).length} family members matching "${query}"`
    }
  } catch (error) {
    console.error('❌ Error searching family members:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: null
    }
  }
}

/**
 * Get family member by ID with full details
 */
export async function getFamilyMemberDetails(memberId: string) {
  try {
    const clerkUser = await currentUser()
    if (!clerkUser) {
      return {
        success: false,
        error: 'User not authenticated',
        data: null
      }
    }

    // Get user from Supabase
    const { data: user, error: userError } = await supabase
      .from('users')
      .select('id')
      .eq('clerk_id', clerkUser.id)
      .single()

    if (userError) {
      return {
        success: false,
        error: 'User not found in database',
        data: null
      }
    }

    // Get family member details
    const { data: familyMember, error: memberError } = await supabase
      .from('family_members')
      .select('*')
      .eq('id', memberId)
      .eq('user_id', user.id)
      .single()

    if (memberError || !familyMember) {
      return {
        success: false,
        error: 'Family member not found or access denied',
        data: null
      }
    }

    // Calculate age
    const birthDate = new Date(familyMember.date_of_birth)
    const today = new Date()
    let age = today.getFullYear() - birthDate.getFullYear()
    const monthDiff = today.getMonth() - birthDate.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--
    }

    return {
      success: true,
      data: {
        ...familyMember,
        age,
        age_description: age < 1 ? `${Math.floor(age * 12)} months old` : `${age} years old`
      }
    }
  } catch (error) {
    console.error('❌ Error getting family member details:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      data: null
    }
  }
}