/**
 * Cross-Tab Communication for Mae Auth Context
 * Handles communication between tabs when auth callback opens in a new tab
 */

interface AuthContextMessage {
  type: 'AUTH_SUCCESS' | 'AUTH_ERROR' | 'MAE_CONTEXT_REQUEST' | 'MAE_CONTEXT_RESPONSE' | 'MAE_SESSION_STATE' | 'FAB_STATE_SYNC'
  data: {
    email?: string
    user_id?: string
    error?: string
    sessionId?: string
    returning_from_auth?: boolean
    maeSessionActive?: boolean
    fabVisible?: boolean
    fabFloating?: boolean
    isRecording?: boolean
  }
}

class CrossTabCommunicator {
  private channel: BroadcastChannel | null = null
  private storageKey = 'mae_auth_context'

  constructor() {
    if (typeof window !== 'undefined' && 'BroadcastChannel' in window) {
      this.channel = new BroadcastChannel('mae_auth_channel')
      console.log('🔗 CrossTabCommunicator initialized with BroadcastChannel')
      this.setupMessageListener()
    } else {
      console.warn('⚠️ BroadcastChannel not available, cross-tab communication disabled')
    }
  }

  private setupMessageListener() {
    if (!this.channel) return

    this.channel.addEventListener('message', (event: MessageEvent<AuthContextMessage>) => {
      console.log('🔗 Cross-tab message received:', event.data)
      
      switch (event.data.type) {
        case 'AUTH_SUCCESS':
          this.handleAuthSuccess(event.data.data)
          break
        case 'MAE_CONTEXT_REQUEST':
          this.handleMaeContextRequest()
          break
        case 'MAE_CONTEXT_RESPONSE':
          this.handleMaeContextResponse(event.data.data)
          break
        case 'MAE_SESSION_STATE':
          this.handleMaeSessionState(event.data.data)
          break
        case 'FAB_STATE_SYNC':
          this.handleFABStateSync(event.data.data)
          break
      }
    })
  }

  // Send auth success from callback tab to Mae tab
  sendAuthSuccess(email: string, user_id: string) {
    console.log('🚀 sendAuthSuccess called with:', { email, user_id })
    
    const message: AuthContextMessage = {
      type: 'AUTH_SUCCESS',
      data: {
        email,
        user_id,
        returning_from_auth: true
      }
    }

    // Store in localStorage as backup
    localStorage.setItem(this.storageKey, JSON.stringify(message.data))
    console.log('💾 Stored auth context in localStorage:', message.data)
    
    // Send via BroadcastChannel
    if (this.channel) {
      console.log('📤 Sending auth success to Mae tab:', message)
      this.channel.postMessage(message)
    } else {
      console.error('❌ No BroadcastChannel available to send message')
    }

    // Also try to close this tab and redirect to original tab
    this.redirectToMaeTab()
  }

  // Handle receiving auth success in Mae tab
  private handleAuthSuccess(data: AuthContextMessage['data']) {
    console.log('🎉 Auth success received in Mae tab:', data)
    
    if (data.email && data.user_id) {
      // Dispatch to Mae session
      window.dispatchEvent(new CustomEvent('user-authenticated', {
        detail: {
          email: data.email,
          user_id: data.user_id,
          returning_from_auth: true
        }
      }))

      // Clear the stored context
      localStorage.removeItem(this.storageKey)
    }
  }

  // Request Mae context from other tabs
  requestMaeContext() {
    const message: AuthContextMessage = {
      type: 'MAE_CONTEXT_REQUEST',
      data: {}
    }

    if (this.channel) {
      console.log('📤 Requesting Mae context from other tabs')
      this.channel.postMessage(message)
    }
  }

  // Handle Mae context request
  private handleMaeContextRequest() {
    // Check if this tab has an active Mae session
    const maeStatus = this.getMaeSessionStatus()
    
    if (maeStatus.hasActiveSession) {
      const response: AuthContextMessage = {
        type: 'MAE_CONTEXT_RESPONSE',
        data: {
          sessionId: maeStatus.sessionId
        }
      }

      if (this.channel) {
        console.log('📤 Sending Mae context response:', response)
        this.channel.postMessage(response)
      }
    }
  }

  // Handle Mae context response
  private handleMaeContextResponse(data: AuthContextMessage['data']) {
    console.log('📥 Mae context response received:', data)
    
    if (data.sessionId) {
      // This tab now knows there's an active Mae session in another tab
      // Redirect to that tab or show appropriate message
      this.redirectToMaeTab()
    }
  }

  // Get Mae session status from current tab
  private getMaeSessionStatus() {
    // Check if Mae is active in this tab
    const maeElements = document.querySelectorAll('[data-mae-active="true"]')
    const maeSessionStorage = sessionStorage.getItem('mae_session_active')
    const hasActiveSession = maeElements.length > 0 || maeSessionStorage === 'true'
    
    console.log('🔍 Checking Mae session status:', {
      maeElements: maeElements.length,
      maeSessionStorage,
      hasActiveSession
    })
    
    return {
      hasActiveSession,
      sessionId: sessionStorage.getItem('mae_session_id') || 'unknown'
    }
  }

  // Try to redirect back to original Mae tab
  private redirectToMaeTab() {
    try {
      // Store redirect intent
      localStorage.setItem('mae_redirect_intent', 'true')
      
      // Try to close this tab if it was opened by auth
      if (window.opener) {
        window.opener.focus()
        window.close()
      } else {
        // If we can't close, redirect to Mae page
        setTimeout(() => {
          window.location.href = '/onboarding'
        }, 2000)
      }
    } catch (error) {
      console.warn('Could not redirect to Mae tab:', error)
      // Fallback: just redirect to onboarding
      window.location.href = '/onboarding'
    }
  }

  // Send Mae session state to other tabs
  sendMaeSessionState(sessionActive: boolean, isRecording: boolean, sessionId?: string) {
    const message: AuthContextMessage = {
      type: 'MAE_SESSION_STATE',
      data: {
        maeSessionActive: sessionActive,
        isRecording,
        sessionId
      }
    }

    if (this.channel) {
      console.log('📤 Sending Mae session state:', message)
      this.channel.postMessage(message)
    }

    // Also store in sessionStorage for persistence
    sessionStorage.setItem('mae_session_state', JSON.stringify(message.data))
  }

  // Handle Mae session state from other tabs
  private handleMaeSessionState(data: AuthContextMessage['data']) {
    console.log('📥 Mae session state received:', data)

    // Dispatch event for components to handle
    window.dispatchEvent(new CustomEvent('mae-session-state-sync', {
      detail: data
    }))
  }

  // Send FAB state to other tabs
  sendFABState(visible: boolean, floating: boolean) {
    const message: AuthContextMessage = {
      type: 'FAB_STATE_SYNC',
      data: {
        fabVisible: visible,
        fabFloating: floating
      }
    }

    if (this.channel) {
      console.log('📤 Sending FAB state:', message)
      this.channel.postMessage(message)
    }

    // Store in sessionStorage
    sessionStorage.setItem('mae_fab_visible', visible.toString())
    sessionStorage.setItem('mae_fab_floating', floating.toString())
  }

  // Handle FAB state from other tabs
  private handleFABStateSync(data: AuthContextMessage['data']) {
    console.log('📥 FAB state sync received:', data)

    // Dispatch event for GlobalFAB to handle
    window.dispatchEvent(new CustomEvent('mae-fab-state-change', {
      detail: {
        visible: data.fabVisible,
        floating: data.fabFloating
      }
    }))
  }

  // Check for stored Mae session state on page load
  checkStoredMaeState() {
    const storedSession = sessionStorage.getItem('mae_session_state')
    const storedFABVisible = sessionStorage.getItem('mae_fab_visible')
    const storedFABFloating = sessionStorage.getItem('mae_fab_floating')

    if (storedSession) {
      try {
        const sessionData = JSON.parse(storedSession)
        console.log('📥 Found stored Mae session state:', sessionData)
        this.handleMaeSessionState(sessionData)
      } catch (error) {
        console.warn('Could not parse stored Mae session state:', error)
        sessionStorage.removeItem('mae_session_state')
      }
    }

    if (storedFABVisible === 'true' || storedFABFloating === 'true') {
      console.log('📥 Found stored FAB state, restoring...')
      this.handleFABStateSync({
        fabVisible: storedFABVisible === 'true',
        fabFloating: storedFABFloating === 'true'
      })
    }
  }

  // Check for stored auth context on page load
  checkStoredAuthContext() {
    const stored = localStorage.getItem(this.storageKey)
    if (stored) {
      try {
        const data = JSON.parse(stored)
        console.log('📥 Found stored auth context:', data)
        this.handleAuthSuccess(data)
      } catch (error) {
        console.warn('Could not parse stored auth context:', error)
        localStorage.removeItem(this.storageKey)
      }
    }
  }

  // Check if this tab should redirect to Mae
  checkRedirectIntent() {
    const shouldRedirect = localStorage.getItem('mae_redirect_intent')
    if (shouldRedirect === 'true') {
      localStorage.removeItem('mae_redirect_intent')
      
      // Check if we're not already on a Mae page
      const currentPath = window.location.pathname
      if (!currentPath.includes('/onboarding') && !currentPath.includes('/mae')) {
        console.log('🔄 Redirecting to Mae onboarding page')
        window.location.href = '/onboarding'
      }
    }
  }

  // Cleanup
  destroy() {
    if (this.channel) {
      this.channel.close()
      this.channel = null
    }
  }
}

// Global instance
let crossTabCommunicator: CrossTabCommunicator | null = null

export function getCrossTabCommunicator(): CrossTabCommunicator {
  if (!crossTabCommunicator && typeof window !== 'undefined') {
    crossTabCommunicator = new CrossTabCommunicator()
  }
  return crossTabCommunicator!
}

// Export methods for easy access
export function sendMaeSessionState(sessionActive: boolean, isRecording: boolean, sessionId?: string) {
  const communicator = getCrossTabCommunicator()
  if (communicator) {
    communicator.sendMaeSessionState(sessionActive, isRecording, sessionId)
  }
}

export function sendFABState(visible: boolean, floating: boolean) {
  const communicator = getCrossTabCommunicator()
  if (communicator) {
    communicator.sendFABState(visible, floating)
  }
}

export function initializeCrossTabCommunication() {
  if (typeof window !== 'undefined') {
    console.log('🔧 Initializing cross-tab communication...')
    const communicator = getCrossTabCommunicator()
    
    // Check for stored auth context on load
    console.log('🔍 Checking for stored auth context...')
    communicator.checkStoredAuthContext()

    // Check for stored Mae state
    console.log('🔍 Checking for stored Mae state...')
    communicator.checkStoredMaeState()

    // Check for redirect intent
    console.log('🔍 Checking for redirect intent...')
    communicator.checkRedirectIntent()
    
    console.log('✅ Cross-tab communication initialized successfully')
    return communicator
  }
  console.warn('⚠️ Window not available, cross-tab communication not initialized')
  return null
}