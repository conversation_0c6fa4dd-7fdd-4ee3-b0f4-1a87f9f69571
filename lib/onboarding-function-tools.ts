/**
 * Voice Onboarding Function Tools for Mae
 * 
 * This module provides function tools that enable <PERSON> to guide users through
 * the onboarding process via voice interactions and AG-UI CustomEvents.
 */

import { Type, FunctionDeclaration } from '@google/genai'
import { aguiEventDispatcher, EventResponse, OnboardingValidator } from './ag-ui-events'

// Helper function to retry API calls with exponential backoff
async function retryApiCall(
  url: string,
  options: RequestInit,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<Response> {
  // Ensure absolute URL for server-side calls
  const absoluteUrl = url.startsWith('http') ? url : `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}${url}`
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch(absoluteUrl, options)

      // If not rate limited, return the response
      if (response.status !== 429) {
        return response
      }

      // If this was the last attempt, return the rate limited response
      if (attempt === maxRetries) {
        return response
      }

      // Wait before retrying with exponential backoff
      const delay = baseDelay * Math.pow(2, attempt)
      console.log(`⏳ Rate limited, retrying in ${delay}ms (attempt ${attempt + 1}/${maxRetries + 1})`)
      await new Promise(resolve => setTimeout(resolve, delay))

    } catch (error) {
      // If this was the last attempt, throw the error
      if (attempt === maxRetries) {
        throw error
      }

      // Wait before retrying
      const delay = baseDelay * Math.pow(2, attempt)
      console.log(`⏳ Request failed, retrying in ${delay}ms (attempt ${attempt + 1}/${maxRetries + 1})`)
      await new Promise(resolve => setTimeout(resolve, delay))
    }
  }

  // This should never be reached, but TypeScript requires it
  throw new Error('Max retries exceeded')
}

// Function tool declarations for Gemini Live API
export const collectUserInfoToolDeclaration: FunctionDeclaration = {
  name: 'collect_user_information',
  description: 'Collect parent/guardian information during onboarding via voice interaction',
  parameters: {
    type: Type.OBJECT,
    properties: {
      field: {
        type: Type.STRING,
        description: 'The field to collect (email, name, role, phone, zip, date_of_birth, emergency_contact_name, emergency_contact_phone, emergency_contact_relationship, notifications, language)',
        enum: ['email', 'name', 'role', 'phone', 'zip', 'date_of_birth', 'emergency_contact_name', 'emergency_contact_phone', 'emergency_contact_relationship', 'notifications', 'language']
      },
      value: {
        type: Type.STRING,
        description: 'The value provided by the user via voice'
      },
      user_email: {
        type: Type.STRING,
        description: 'User email for session tracking'
      },
      validate_immediately: {
        type: Type.BOOLEAN,
        description: 'Whether to validate the field immediately',
        default: false
      }
    },
    required: ['field', 'value', 'user_email']
  }
}

export const addFamilyMemberToolDeclaration: FunctionDeclaration = {
  name: 'add_family_member',
  description: 'Add a family member (child) during onboarding via voice interaction',
  parameters: {
    type: Type.OBJECT,
    properties: {
      name: {
        type: Type.STRING,
        description: 'Full name of the family member'
      },
      date_of_birth: {
        type: Type.STRING,
        description: 'Date of birth in YYYY-MM-DD format'
      },
      gender: {
        type: Type.STRING,
        description: 'Gender of the family member',
        enum: ['male', 'female', 'other', 'prefer_not_to_say']
      },
      relationship: {
        type: Type.STRING,
        description: 'Relationship to the user (e.g., child, stepchild, grandchild)'
      },
      medical_conditions: {
        type: Type.ARRAY,
        description: 'Array of medical conditions',
        items: { type: Type.STRING }
      },
      allergies: {
        type: Type.ARRAY,
        description: 'Array of allergies',
        items: { type: Type.STRING }
      },
      medications: {
        type: Type.ARRAY,
        description: 'Array of medications',
        items: {
          type: Type.OBJECT,
          properties: {
            name: { type: Type.STRING },
            dosage: { type: Type.STRING },
            frequency: { type: Type.STRING }
          }
        }
      },
      additional_notes: {
        type: Type.STRING,
        description: 'Any additional notes about the family member'
      },
      user_email: {
        type: Type.STRING,
        description: 'User email for session tracking'
      },
      is_primary: {
        type: Type.BOOLEAN,
        description: 'Whether this is the primary child',
        default: false
      }
    },
    required: ['date_of_birth', 'relationship', 'user_email']
  }
}

export const validateOnboardingDataToolDeclaration: FunctionDeclaration = {
  name: 'validate_onboarding_data',
  description: 'Validate collected onboarding data before submission',
  parameters: {
    type: Type.OBJECT,
    properties: {
      data_type: {
        type: Type.STRING,
        description: 'Type of data to validate',
        enum: ['user_info', 'family_member', 'all']
      },
      user_email: {
        type: Type.STRING,
        description: 'User email for session tracking'
      },
      show_errors: {
        type: Type.BOOLEAN,
        description: 'Whether to show validation errors to the user',
        default: true
      }
    },
    required: ['data_type', 'user_email']
  }
}

export const completeOnboardingToolDeclaration: FunctionDeclaration = {
  name: 'complete_onboarding',
  description: 'Complete the onboarding process and create user account',
  parameters: {
    type: Type.OBJECT,
    properties: {
      user_email: {
        type: Type.STRING,
        description: 'User email for the account being created'
      },
      send_welcome_email: {
        type: Type.BOOLEAN,
        description: 'Whether to send a welcome email',
        default: true
      }
    },
    required: ['user_email']
  }
}

export const getOnboardingProgressToolDeclaration: FunctionDeclaration = {
  name: 'get_onboarding_progress',
  description: 'Get current onboarding progress for a user',
  parameters: {
    type: Type.OBJECT,
    properties: {
      user_email: {
        type: Type.STRING,
        description: 'User email to check progress for'
      }
    },
    required: ['user_email']
  }
}

export const updateOnboardingStepToolDeclaration: FunctionDeclaration = {
  name: 'update_onboarding_step',
  description: 'Update the current onboarding step',
  parameters: {
    type: Type.OBJECT,
    properties: {
      user_email: {
        type: Type.STRING,
        description: 'User email for session tracking'
      },
      step: {
        type: Type.STRING,
        description: 'The onboarding step to move to',
        enum: ['welcome', 'user_info', 'family_info', 'complete']
      }
    },
    required: ['user_email', 'step']
  }
}

// Function tool handlers
export async function handleCollectUserInfo(parameters: {
  field: string
  value: string
  user_email: string
  validate_immediately?: boolean
}) {
  try {
    console.log('📝 Collecting user info via voice:', parameters)

    // Check if user is authenticated first - if not, suggest showing auth UI
    if (typeof window !== 'undefined') {
      try {
        const { supabase } = await import('@/lib/supabase-client')
        const { data: { session } } = await supabase.auth.getSession()
        
        if (!session) {
          console.log('🔐 No authenticated session - user needs to sign up/in')
          return {
            success: false,
            error: 'Authentication required',
            action_required: 'show_auth_ui',
            message: 'Please sign up or log in to save your information securely.'
          }
        }
      } catch (authError) {
        console.warn('Could not check authentication status:', authError)
      }
    }

    // Dispatch activity event for Mae status tracking
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('mae-function-call', {
        detail: {
          functionName: 'collect_user_information',
          parameters,
          status: 'active'
        }
      }))
    }

    // Parse voice input for the specific field
    const parsedValue = OnboardingValidator.parseVoiceInput(parameters.value, parameters.field)
    
    // Try to dispatch AG-UI event to fill form (global handler will manage navigation)
    let formResponse: EventResponse = { success: false, data: null, validationErrors: [] }
    try {
      // Validate payload structure before dispatching
      const payload = {
        field: parameters.field as any,
        value: parsedValue,
        validate: parameters.validate_immediately
      }

      // Ensure required fields are present
      if (!payload.field) {
        throw new Error('Field is required for fillUserForm event')
      }

      if (payload.value === undefined || payload.value === null) {
        throw new Error('Value is required for fillUserForm event')
      }

      formResponse = await aguiEventDispatcher.fillUserForm(payload)

      if (formResponse.success) {
        console.log('✅ Successfully updated form UI')
        
        // Remove automatic step progression - let Mae control the flow
      }
    } catch (formError) {
      console.log('ℹ️ AG-UI event handled by global handler or timed out:', formError)
    }

    // Save to database via API - create user if needed, update if exists
    try {
      // Check if user exists first
      const baseUrl = typeof window !== 'undefined' ? window.location.origin : process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
      let userResponse = await fetch(`${baseUrl}/api/onboarding/users?email=${encodeURIComponent(parameters.user_email)}`)
      let userId: string | null = null
      let existingUserData: any = {}

      if (userResponse.ok) {
        const userData = await userResponse.json()
        userId = userData.user?.id
        existingUserData = userData.user || {}
      } else if (userResponse.status === 404) {
        // User doesn't exist yet - this is expected for new users
        console.log('User not found in database, will create new user')
      } else {
        console.error('Error checking for existing user:', userResponse.status)
      }

      // Prepare user data for API - accumulate with existing data
      const userUpdateData: any = {
        email: parameters.user_email,
        ...existingUserData
      }

      // Handle nested fields properly
      if (parameters.field.startsWith('emergency_contact_')) {
        const emergencyContactField = parameters.field.replace('emergency_contact_', '')
        userUpdateData.emergency_contact = {
          ...userUpdateData.emergency_contact,
          [emergencyContactField]: parsedValue
        }
      } else if (parameters.field === 'notifications') {
        userUpdateData.preferences = {
          ...userUpdateData.preferences,
          notifications: parsedValue === 'true' || parsedValue === true
        }
      } else if (parameters.field === 'language') {
        userUpdateData.preferences = {
          ...userUpdateData.preferences,
          language: parsedValue
        }
      } else {
        userUpdateData[parameters.field] = parsedValue
      }

      // Ensure we have a name field (required for user creation)
      if (!userUpdateData.name && parameters.field !== 'name') {
        userUpdateData.name = 'User' // Temporary name
      }

      // Clean up date fields - convert empty strings to null
      if (userUpdateData.date_of_birth === '') {
        userUpdateData.date_of_birth = null
      }

      // Create or update user via API with retry logic
      // For Mae integration, we need to pass the auth token if available
      const headers: Record<string, string> = { 'Content-Type': 'application/json' }
      
      // Try to get auth token from current session
      if (typeof window !== 'undefined') {
        try {
          const { supabase } = await import('@/lib/supabase-client')
          const { data: { session } } = await supabase.auth.getSession()
          if (session?.access_token) {
            headers['Authorization'] = `Bearer ${session.access_token}`
            console.log('✅ Using authenticated session for API call')
          } else {
            console.log('📝 No authenticated session, proceeding with Mae registration flow')
          }
        } catch (error) {
          console.warn('Could not get auth token, proceeding without authentication:', error)
        }
      }

      console.log('📤 Making API call to create/update user:', {
        method: userId ? 'PUT' : 'POST',
        url: `${baseUrl}/api/onboarding/users`,
        data: userId ? { id: userId, ...userUpdateData } : userUpdateData,
        hasAuth: !!headers['Authorization']
      })

      const apiResponse = await retryApiCall(`${baseUrl}/api/onboarding/users`, {
        method: userId ? 'PUT' : 'POST',
        headers,
        body: JSON.stringify(userId ? { id: userId, ...userUpdateData } : userUpdateData)
      })

      if (!apiResponse.ok) {
        let errorData: any = {}
        try {
          const responseText = await apiResponse.text()
          if (responseText.trim()) {
            errorData = JSON.parse(responseText)
          } else {
            errorData = { error: 'Empty response from server' }
          }
        } catch (parseError) {
          console.error('Failed to parse error response:', parseError)
          errorData = {
            error: `Server returned ${apiResponse.status} ${apiResponse.statusText}`,
            details: 'Unable to parse error response'
          }
        }

        console.error('API call failed:', errorData)
        console.error('API Response status:', apiResponse.status)
        console.error('API Response headers:', Object.fromEntries(apiResponse.headers.entries()))
        if (apiResponse.status === 429) {
          console.warn('⚠️ Rate limit exceeded for user info collection')
        }
        // Don't fail the entire operation if API fails, but log it
      } else {
        let responseData: any = {}
        try {
          const responseText = await apiResponse.text()
          if (responseText.trim()) {
            responseData = JSON.parse(responseText)
          } else {
            responseData = { success: true, message: 'Empty success response' }
          }
        } catch (parseError) {
          console.error('Failed to parse success response:', parseError)
          responseData = { success: true, message: 'Response parsing failed but request succeeded' }
        }

        console.log('✅ Successfully saved to database via API:', responseData.user?.id)
      }
    } catch (apiError) {
      console.error('Error calling API:', apiError)
      // Don't fail the entire operation if API fails
    }

    // If validation was requested and failed
    if (parameters.validate_immediately && formResponse.validationErrors?.length) {
      return {
        success: false,
        error: 'Validation failed',
        validation_errors: formResponse.validationErrors,
        field: parameters.field,
        attempted_value: parameters.value
      }
    }

    // Dispatch completion event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('mae-function-call', {
        detail: {
          functionName: 'collect_user_information',
          parameters,
          status: 'completed'
        }
      }))
    }

    return {
      success: true,
      message: `Successfully updated ${parameters.field}`,
      field: parameters.field,
      value: parsedValue,
      data: formResponse.data
    }

  } catch (error) {
    console.error('Error in handleCollectUserInfo:', error)
    
    // Dispatch error event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('mae-function-call', {
        detail: {
          functionName: 'collect_user_information',
          parameters,
          status: 'error'
        }
      }))
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      field: parameters.field
    }
  }
}

export async function handleAddFamilyMember(parameters: {
  name: string
  date_of_birth: string
  gender?: string
  relationship: string
  medical_conditions?: string[]
  allergies?: string[]
  medications?: any[]
  additional_notes?: string
  user_email: string
  is_primary?: boolean
}) {
  try {
    console.log('👨‍👩‍👧‍👦 Adding family member via voice:', parameters)

    // Dispatch activity event for Mae status tracking
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('mae-function-call', {
        detail: {
          functionName: 'add_family_member',
          parameters,
          status: 'active'
        }
      }))
    }

    // Parse voice inputs
    const parsedData = {
      name: parameters.name.trim(),
      date_of_birth: OnboardingValidator.parseVoiceInput(parameters.date_of_birth, 'date_of_birth'),
      gender: parameters.gender ? OnboardingValidator.parseVoiceInput(parameters.gender, 'gender') : undefined,
      relationship: OnboardingValidator.parseVoiceInput(parameters.relationship, 'relationship'),
      medical_conditions: parameters.medical_conditions || [],
      allergies: parameters.allergies || [],
      medications: parameters.medications || [],
      additional_notes: parameters.additional_notes,
      is_primary: parameters.is_primary || false
    }

    console.log('🔍 Parsed family member data:', parsedData)
    console.log('🔍 Original date_of_birth input:', parameters.date_of_birth)
    console.log('🔍 Parsed date_of_birth:', parsedData.date_of_birth)

    // Validate family member data
    const validation = OnboardingValidator.validateFamilyMemberData(parsedData)
    if (!validation.valid) {
      return {
        success: false,
        error: 'Family member data validation failed',
        validation_errors: validation.errors,
        attempted_data: parsedData
      }
    }

    // Try to dispatch AG-UI event to add family member (global handler will manage navigation)
    let formResponse: EventResponse = { success: false, data: null }
    try {
      // Validate payload structure before dispatching
      // if (!parsedData.name) {
      //   throw new Error('Name is required for addFamilyMember event')
      // }

      if (!parsedData.date_of_birth) {
        throw new Error('Date of birth is required for addFamilyMember event')
      }

      if (!parsedData.relationship) {
        throw new Error('Relationship is required for addFamilyMember event')
      }

      formResponse = await aguiEventDispatcher.addFamilyMember(parsedData)

      if (formResponse.success) {
        console.log('✅ Successfully updated family member form UI')
      }
    } catch (formError) {
      console.log('ℹ️ AG-UI event handled by global handler or timed out:', formError)
    }

    // Also save directly to database via API
    try {
      // Get user ID first, create user if they don't exist
      const baseUrl = typeof window !== 'undefined' ? window.location.origin : process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
      console.log('🌐 Base URL for API calls:', baseUrl)
      let userResponse = await fetch(`${baseUrl}/api/onboarding/users?email=${encodeURIComponent(parameters.user_email)}`)
      let userId: string | null = null

      if (userResponse.ok) {
        const userData = await userResponse.json()
        userId = userData.user?.id
      }

      // If user doesn't exist, create a minimal user record
      if (!userId) {
        console.log('👤 User not found, creating minimal user record for family member')
        const createUserResponse = await retryApiCall(`${baseUrl}/api/onboarding/users`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: parameters.user_email,
            name: 'User', // Temporary name, will be updated when Mae collects it
            onboarding_completed: false,
            onboarding_step: 'family_info'
          })
        })

        if (createUserResponse.ok) {
          let newUserData: any = {}
          try {
            const responseText = await createUserResponse.text()
            if (responseText.trim()) {
              newUserData = JSON.parse(responseText)
            } else {
              newUserData = { user: { id: null } }
            }
          } catch (parseError) {
            console.error('Failed to parse user creation response:', parseError)
            newUserData = { user: { id: null } }
          }

          userId = newUserData.user?.id
          console.log('✅ Created minimal user record with ID:', userId)
        } else {
          let errorData: any = {}
          try {
            const responseText = await createUserResponse.text()
            if (responseText.trim()) {
              errorData = JSON.parse(responseText)
            } else {
              errorData = { error: 'Empty error response from server' }
            }
          } catch (parseError) {
            console.error('Failed to parse error response:', parseError)
            errorData = {
              error: `Server returned ${createUserResponse.status} ${createUserResponse.statusText}`,
              details: 'Unable to parse error response'
            }
          }

          if (createUserResponse.status === 429) {
            throw new Error('Rate limit exceeded. Please wait a moment and try again.')
          }
          throw new Error(`Failed to create user: ${errorData.error || 'Unknown error'}`)
        }
      }

      if (!userId) {
        throw new Error('Unable to get or create user ID')
      }

      // Prepare family member data for API (include all fields that exist in DB schema)
      const familyMemberData = {
        user_id: userId,
        ...parsedData,
        // Ensure date_of_birth is not an empty string
        date_of_birth: parsedData.date_of_birth === '' ? null : parsedData.date_of_birth
      }

      console.log('📝 Sending family member data to API:', familyMemberData)

      // Create family member via API with retry logic
      // For Mae integration, we need to pass the auth token if available
      const headers: Record<string, string> = { 'Content-Type': 'application/json' }

      // Try to get auth token from current session
      if (typeof window !== 'undefined') {
        try {
          const { data: { session } } = await import('@/lib/supabase-client').then(m => m.supabase.auth.getSession())
          if (session?.access_token) {
            headers['Authorization'] = `Bearer ${session.access_token}`
            console.log('🔑 Using auth token for API call')
          } else {
            console.log('🔑 No auth token available')
          }
        } catch (error) {
          console.warn('Could not get auth token, proceeding without authentication')
        }
      }

      const apiUrl = `${baseUrl}/api/onboarding/family-members`
      console.log('🌐 Making API call to:', apiUrl)
      console.log('🌐 Request headers:', headers)
      console.log('🌐 Request body:', JSON.stringify(familyMemberData))

      const apiResponse = await retryApiCall(apiUrl, {
        method: 'POST',
        headers,
        body: JSON.stringify(familyMemberData)
      })

      if (!apiResponse.ok) {
        let errorData: any = {}
        let responseText = ''
        try {
          responseText = await apiResponse.text()
          console.log('🔍 Raw API response text:', responseText)
          console.log('🔍 API response status:', apiResponse.status)
          console.log('🔍 API response headers:', Object.fromEntries(apiResponse.headers.entries()))

          if (responseText.trim()) {
            errorData = JSON.parse(responseText)
          } else {
            errorData = { error: 'Empty error response from server' }
          }
        } catch (parseError) {
          console.error('Failed to parse family member error response:', parseError)
          console.error('Raw response text was:', responseText)
          errorData = {
            error: `Server returned ${apiResponse.status} ${apiResponse.statusText}`,
            details: 'Unable to parse error response',
            raw_response: responseText
          }
        }

        console.error('API call failed:', errorData)
        if (apiResponse.status === 429) {
          return {
            success: false,
            error: 'Rate limit exceeded. Please wait a moment and try again.',
            attempted_data: parsedData
          }
        }
        if (apiResponse.status === 409) {
          return {
            success: false,
            error: errorData.error || 'A family member with this name and date of birth already exists',
            attempted_data: parsedData
          }
        }
        return {
          success: false,
          error: `Failed to save family member to database: ${errorData.error || 'Unknown error'}`,
          attempted_data: parsedData
        }
      } else {
        console.log('✅ Successfully saved family member to database via API')
      }
    } catch (apiError) {
      console.error('Error calling API:', apiError)
      return {
        success: false,
        error: `Database error: ${apiError instanceof Error ? apiError.message : 'Unknown error'}`,
        attempted_data: parsedData
      }
    }

    // Dispatch completion event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('mae-function-call', {
        detail: {
          functionName: 'add_family_member',
          parameters,
          status: 'completed'
        }
      }))
    }

    return {
      success: true,
      message: `Successfully added ${parsedData.name} as a family member`,
      family_member: parsedData,
      data: formResponse.data
    }

  } catch (error) {
    console.error('Error in handleAddFamilyMember:', error)
    
    // Dispatch error event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('mae-function-call', {
        detail: {
          functionName: 'add_family_member',
          parameters,
          status: 'error'
        }
      }))
    }

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}

export async function handleValidateOnboardingData(parameters: {
  data_type: string
  user_email: string
  show_errors?: boolean
}) {
  try {
    console.log('✅ Validating onboarding data:', parameters)

    // Dispatch AG-UI event to validate form
    const response = await aguiEventDispatcher.validateForm({
      formType: parameters.data_type === 'user_info' ? 'user' : 'family_member',
      data: {}, // The form component will provide the current data
      showErrors: parameters.show_errors !== false
    })

    if (!response.success) {
      return {
        success: false,
        error: response.error || 'Validation failed',
        validation_errors: response.validationErrors
      }
    }

    return {
      success: true,
      message: `${parameters.data_type} validation passed`,
      validation_result: response.data,
      errors: response.validationErrors || []
    }

  } catch (error) {
    console.error('Error in handleValidateOnboardingData:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}

export async function handleCompleteOnboarding(parameters: {
  user_email: string
  send_welcome_email?: boolean
}) {
  try {
    console.log('🎉 Completing onboarding:', parameters)

    // First validate all data
    const userValidation = await aguiEventDispatcher.validateForm({
      formType: 'user',
      data: parameters || {},
      showErrors: false
    })

    if (!userValidation.success) {
      return {
        success: false,
        error: 'User information validation failed',
        validation_errors: userValidation.validationErrors
      }
    }

    // Try to submit user form via AG-UI (for UI feedback on onboarding page)
    try {
      const userSubmission = await aguiEventDispatcher.submitUserForm({
        validate: true
      })

      if (userSubmission.success) {
        console.log('✅ Successfully submitted form UI')
      }
    } catch (formError) {
      console.log('ℹ️ No onboarding form available (likely on landing page) - will complete via database only')
    }

    // Mark onboarding as complete in database via API
    try {
      // Get user first, create if they don't exist
      const baseUrl = typeof window !== 'undefined' ? window.location.origin : process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
      let userResponse = await fetch(`${baseUrl}/api/onboarding/users?email=${encodeURIComponent(parameters.user_email)}`)
      let userId: string | null = null
      let userExisted = false

      if (userResponse.ok) {
        const userData = await userResponse.json()
        userId = userData.user?.id
        userExisted = true
      }

      // If user doesn't exist, create them with completion status
      if (!userId) {
        console.log('👤 User not found, creating user record for onboarding completion')
        const createUserResponse = await retryApiCall(`${baseUrl}/api/onboarding/users`, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            email: parameters.user_email,
            name: 'User', // Temporary name
            onboarding_completed: true,
            onboarding_step: 'complete'
          })
        })

        if (createUserResponse.ok) {
          const newUserData = await createUserResponse.json()
          userId = newUserData.user?.id
          console.log('✅ Created user record and marked as complete:', userId)
        } else {
          const errorData = await createUserResponse.json()
          if (createUserResponse.status === 429) {
            throw new Error('Rate limit exceeded. Please wait a moment and try again.')
          }
          throw new Error(`Failed to create user: ${errorData.error}`)
        }
      } else {
        // User existed, update them to mark onboarding as complete
        const updateResponse = await retryApiCall(`${baseUrl}/api/onboarding/users`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            id: userId,
            onboarding_completed: true,
            onboarding_step: 'complete'
          })
        })

        if (!updateResponse.ok) {
          const errorData = await updateResponse.json()
          if (updateResponse.status === 429) {
            throw new Error('Rate limit exceeded. Please wait a moment and try again.')
          }
          throw new Error(`Failed to complete onboarding: ${errorData.error}`)
        }

        console.log('✅ Successfully marked existing user onboarding as complete in database')
      }

      if (!userId) {
        throw new Error('Unable to get or create user ID')
      }
    } catch (apiError) {
      console.error('Error completing onboarding via API:', apiError)
      return {
        success: false,
        error: `Database error: ${apiError instanceof Error ? apiError.message : 'Unknown error'}`
      }
    }

    // Try to update progress via AG-UI (for UI feedback on onboarding page)
    try {
      const progressUpdate = await aguiEventDispatcher.updateProgress({
        step: 'complete',
        data: { completed_at: new Date().toISOString() }
      })

      if (progressUpdate.success) {
        console.log('✅ Successfully updated progress UI')
      }
    } catch (progressError) {
      console.log('ℹ️ No onboarding form available for progress update')
    }

    // Try to show completion confirmation (for UI feedback on onboarding page)
    try {
      await aguiEventDispatcher.showConfirmation({
        message: 'Onboarding completed successfully! Welcome to Our Kidz!',
        type: 'success'
      })
      console.log('✅ Successfully showed completion confirmation')
    } catch (confirmError) {
      console.log('ℹ️ No onboarding form available for confirmation display')
    }

    // Send welcome email if requested
    if (parameters.send_welcome_email) {
      try {
        // This would integrate with the existing email system
        console.log('📧 Sending welcome email to:', parameters.user_email)
        // Implementation would go here
      } catch (emailError) {
        console.warn('Failed to send welcome email:', emailError)
        // Don't fail the entire onboarding for email issues
      }
    }

    return {
      success: true,
      message: 'Onboarding completed successfully!',
      user_email: parameters.user_email,
      completed_at: new Date().toISOString(),
      welcome_email_sent: parameters.send_welcome_email || false
    }

  } catch (error) {
    console.error('Error in handleCompleteOnboarding:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}

export async function handleGetOnboardingProgress(parameters: {
  user_email: string
}) {
  try {
    console.log('📊 Getting onboarding progress for:', parameters.user_email)

    // Get progress from API endpoint to avoid server-side dependency
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000'
    const apiUrl = `${baseUrl}/api/onboarding/progress?email=${encodeURIComponent(parameters.user_email)}`
    
    const response = await fetch(apiUrl)
    const result = await response.json()

    if (!response.ok) {
      return {
        success: false,
        error: result.error || 'Failed to fetch onboarding progress'
      }
    }

    if (!result.progress) {
      return {
        success: true,
        message: 'No onboarding progress found',
        progress: {
          user_id: null,
          current_step: 'welcome',
          onboarding_completed: false,
          family_members_count: 0,
          session_data: {}
        }
      }
    }

    return {
      success: true,
      message: 'Onboarding progress retrieved successfully',
      progress: result.progress
    }

  } catch (error) {
    console.error('Error in handleGetOnboardingProgress:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}

export async function handleUpdateOnboardingStep(parameters: {
  user_email: string
  step: string
}) {
  try {
    console.log('🔄 Updating onboarding step:', parameters)

    // Dispatch AG-UI event to update progress
    const response = await aguiEventDispatcher.updateProgress({
      step: parameters.step as any,
      data: { updated_at: new Date().toISOString() }
    })

    if (!response.success) {
      return {
        success: false,
        error: response.error || 'Failed to update onboarding step'
      }
    }

    return {
      success: true,
      message: `Successfully updated onboarding step to ${parameters.step}`,
      step: parameters.step,
      data: response.data
    }

  } catch (error) {
    console.error('Error in handleUpdateOnboardingStep:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}

// Navigation function tool declaration
export const navigateToOnboardingToolDeclaration: FunctionDeclaration = {
  name: 'navigate_to_onboarding',
  description: 'Navigate the user to the onboarding page to begin or continue the onboarding process',
  parameters: {
    type: Type.OBJECT,
    properties: {
      message: {
        type: Type.STRING,
        description: 'Optional message to explain why navigation is happening'
      }
    }
  }
}

// Navigation function handler
export async function handleNavigateToOnboarding(parameters: {
  message?: string
}) {
  try {
    console.log('🧭 Navigating to onboarding page:', parameters)

    if (typeof window !== 'undefined') {
      // Store a message for the onboarding page if provided
      if (parameters.message) {
        sessionStorage.setItem('mae-navigation-message', parameters.message)
      }

      // Set flag to indicate Mae session should continue
      sessionStorage.setItem('mae-continue-session', 'true')
      
      // Store current conversation context for continuation
      sessionStorage.setItem('mae-conversation-context', JSON.stringify({
        navigatedFrom: 'landing',
        timestamp: Date.now(),
        message: parameters.message
      }))

      // Dispatch custom event to trigger client-side navigation
      const navigationEvent = new CustomEvent('mae-navigate-to-onboarding', {
        detail: {
          message: parameters.message,
          preserveSession: true
        }
      })
      
      console.log('🚀 Dispatching navigation event to preserve voice session')
      window.dispatchEvent(navigationEvent)

      return {
        success: true,
        message: parameters.message || 'Navigating to onboarding page with preserved session',
        navigated: true,
        sessionPreserved: true
      }
    } else {
      return {
        success: false,
        error: 'Navigation not available in server environment'
      }
    }

  } catch (error) {
    console.error('Error in handleNavigateToOnboarding:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}

// Export all function declarations for easy import
export const onboardingFunctionDeclarations: FunctionDeclaration[] = [
  collectUserInfoToolDeclaration,
  addFamilyMemberToolDeclaration,
  validateOnboardingDataToolDeclaration,
  completeOnboardingToolDeclaration,
  getOnboardingProgressToolDeclaration,
  updateOnboardingStepToolDeclaration,
  navigateToOnboardingToolDeclaration
]