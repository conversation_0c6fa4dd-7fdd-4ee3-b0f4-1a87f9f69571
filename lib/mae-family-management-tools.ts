/**
 * Mae Family Management Tools
 * 
 * Function tools that allow <PERSON> to manage family members using proper UUIDs
 * and database operations. These tools ensure <PERSON> always has the correct
 * user context before performing family member operations.
 */

import { Type, FunctionDeclaration } from '@google/genai'

export const maeFamilyManagementFunctionDeclarations: FunctionDeclaration[] = [
  {
    name: 'add_family_member_with_uuid',
    description: 'Add a family member using the proper user UUID. IMPORTANT: You must call get_user_context first to get the user_id (UUID) before using this function.',
    parameters: {
      type: Type.OBJECT,
      properties: {
        user_id: {
          type: Type.STRING,
          description: 'The user UUID from get_user_context response (must be a valid UUID, not email or Clerk ID)'
        },
        name: {
          type: Type.STRING,
          description: 'Full name of the family member'
        },
        date_of_birth: {
          type: Type.STRING,
          description: 'Date of birth in YYYY-MM-DD format'
        },
        gender: {
          type: Type.STRING,
          description: 'Gender of the family member',
          enum: ['male', 'female', 'other', 'prefer_not_to_say']
        },
        relationship: {
          type: Type.STRING,
          description: 'Relationship to the user (e.g., child, stepchild, grandchild)'
        },
        medical_conditions: {
          type: Type.ARRAY,
          description: 'Array of medical conditions',
          items: { type: Type.STRING }
        },
        allergies: {
          type: Type.ARRAY,
          description: 'Array of allergies',
          items: { type: Type.STRING }
        },
        medications: {
          type: Type.ARRAY,
          description: 'Array of medications',
          items: {
            type: Type.OBJECT,
            properties: {
              name: { type: Type.STRING },
              dosage: { type: Type.STRING },
              frequency: { type: Type.STRING }
            }
          }
        },
        additional_notes: {
          type: Type.STRING,
          description: 'Any additional notes about the family member'
        },
        is_primary: {
          type: Type.BOOLEAN,
          description: 'Whether this is the primary child',
          default: false
        }
      },
      required: ['user_id', 'name', 'date_of_birth', 'relationship']
    }
  },
  {
    name: 'get_family_members_by_uuid',
    description: 'Get all family members for a user using their UUID. IMPORTANT: You must call get_user_context first to get the user_id (UUID).',
    parameters: {
      type: Type.OBJECT,
      properties: {
        user_id: {
          type: Type.STRING,
          description: 'The user UUID from get_user_context response (must be a valid UUID, not email or Clerk ID)'
        }
      },
      required: ['user_id']
    }
  }
]

export async function handleAddFamilyMemberWithUuid(args: {
  user_id: string
  name: string
  date_of_birth: string
  gender?: string
  relationship: string
  medical_conditions?: string[]
  allergies?: string[]
  medications?: any[]
  additional_notes?: string
  is_primary?: boolean
}) {
  try {
    console.log('👨‍👩‍👧‍👦 Mae adding family member with UUID:', args)

    // Validate that user_id is a proper UUID
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    
    if (!uuidRegex.test(args.user_id)) {
      return {
        success: false,
        message: 'Invalid user_id format. You must call get_user_context first to get the proper user UUID.',
        error: 'invalid_user_id_format',
        action_required: 'get_user_context',
        recommendation: 'Call get_user_context first to get the proper user UUID, then use that UUID for family member operations.'
      }
    }

    const response = await fetch('/api/mae-family-members', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        user_id: args.user_id,
        name: args.name,
        date_of_birth: args.date_of_birth,
        gender: args.gender,
        relationship: args.relationship,
        medical_conditions: args.medical_conditions || [],
        allergies: args.allergies || [],
        medications: args.medications || [],
        additional_notes: args.additional_notes,
        is_primary: args.is_primary || false
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ Family member API error:', response.status, errorText)
      return {
        success: false,
        message: `Error adding family member: ${response.status}`,
        error: errorText
      }
    }

    const result = await response.json()

    if (!result.success) {
      return {
        success: false,
        message: 'Failed to add family member',
        error: result.error,
        details: result.details
      }
    }

    return {
      success: true,
      message: `Successfully added ${args.name} as a ${args.relationship}!`,
      family_member: result.family_member,
      recommendation: 'Family member has been added to the database. You can now ask about their health, development, or other topics related to this child.'
    }

  } catch (error) {
    console.error('❌ Error in handleAddFamilyMemberWithUuid:', error)
    return {
      success: false,
      message: 'Error adding family member',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export async function handleGetFamilyMembersByUuid(args: {
  user_id: string
}) {
  try {
    console.log('👨‍👩‍👧‍👦 Mae getting family members for UUID:', args.user_id)

    // Validate that user_id is a proper UUID
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    
    if (!uuidRegex.test(args.user_id)) {
      return {
        success: false,
        message: 'Invalid user_id format. You must call get_user_context first to get the proper user UUID.',
        error: 'invalid_user_id_format',
        action_required: 'get_user_context'
      }
    }

    const response = await fetch(`/api/mae-family-members?user_id=${args.user_id}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ Family members API error:', response.status, errorText)
      return {
        success: false,
        message: `Error fetching family members: ${response.status}`,
        error: errorText
      }
    }

    const result = await response.json()

    if (!result.success) {
      return {
        success: false,
        message: 'Failed to fetch family members',
        error: result.error
      }
    }

    const familyMembers = result.family_members || []
    const summary = familyMembers.map((member: any) => {
      const birthDate = new Date(member.date_of_birth)
      const age = Math.floor((Date.now() - birthDate.getTime()) / (365.25 * 24 * 60 * 60 * 1000))
      return `${member.name} (${age} years old, ${member.relationship})`
    }).join(', ')

    return {
      success: true,
      message: familyMembers.length > 0 
        ? `Found ${familyMembers.length} family member(s): ${summary}`
        : 'No family members found for this user.',
      family_members: familyMembers,
      count: familyMembers.length
    }

  } catch (error) {
    console.error('❌ Error in handleGetFamilyMembersByUuid:', error)
    return {
      success: false,
      message: 'Error fetching family members',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}
