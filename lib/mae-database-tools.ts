/**
 * Mae Database Management Tools
 * 
 * Comprehensive function tools that allow <PERSON> to save and manage data
 * across all relevant database tables for complete user assistance.
 */

import { Type, FunctionDeclaration } from '@google/genai'

export const maeDatabaseFunctionDeclarations: FunctionDeclaration[] = [
  {
    name: 'save_care_log_entry',
    description: 'Save a care log entry for a child including health data, activities, mood, and notes. Use this to track daily health and development.',
    parameters: {
      type: Type.OBJECT,
      properties: {
        user_id: {
          type: Type.STRING,
          description: 'The user UUID from get_user_context response'
        },
        child_name: {
          type: Type.STRING,
          description: 'Name of the child this entry is for'
        },
        note: {
          type: Type.STRING,
          description: 'Detailed care log note including health data, activities, meals, sleep, mood, etc.'
        },
        tags: {
          type: Type.ARRAY,
          description: 'Tags to categorize the entry (e.g., daily_log, medical, growth_chart, health)',
          items: { type: Type.STRING }
        },
        content_type: {
          type: Type.STRING,
          description: 'Type of content (e.g., daily_log, medical_note, growth_update)',
          enum: ['daily_log', 'medical_note', 'growth_update', 'activity_log', 'mood_log', 'other']
        },
        weather: {
          type: Type.STRING,
          description: 'Weather conditions if relevant to the entry'
        }
      },
      required: ['user_id', 'child_name', 'note']
    }
  },
  {
    name: 'save_to_library',
    description: 'Save content to the user\'s personal library including articles, tips, resources, or important information.',
    parameters: {
      type: Type.OBJECT,
      properties: {
        user_id: {
          type: Type.STRING,
          description: 'The user UUID from get_user_context response'
        },
        content_type: {
          type: Type.STRING,
          description: 'Type of content being saved',
          enum: ['article', 'tip', 'resource', 'advice', 'reminder', 'recipe', 'activity', 'medical_info', 'other']
        },
        title: {
          type: Type.STRING,
          description: 'Title of the content'
        },
        content: {
          type: Type.STRING,
          description: 'The actual content, advice, or information to save'
        },
        metadata: {
          type: Type.OBJECT,
          description: 'Additional metadata about the content (tags, source, category, etc.)',
          properties: {
            tags: { type: Type.ARRAY, items: { type: Type.STRING } },
            source: { type: Type.STRING },
            category: { type: Type.STRING },
            child_related: { type: Type.STRING }
          }
        }
      },
      required: ['user_id', 'content_type', 'title', 'content']
    }
  },
  {
    name: 'save_pre_visit_data',
    description: 'Save medical pre-visit data for upcoming doctor appointments including symptoms, concerns, and questions.',
    parameters: {
      type: Type.OBJECT,
      properties: {
        user_id: {
          type: Type.STRING,
          description: 'The user UUID from get_user_context response'
        },
        child_name: {
          type: Type.STRING,
          description: 'Name of the child for the appointment'
        },
        guardian_name: {
          type: Type.STRING,
          description: 'Name of the guardian/parent'
        },
        contact_phone: {
          type: Type.STRING,
          description: 'Contact phone number'
        },
        visit_type: {
          type: Type.STRING,
          description: 'Type of visit (checkup, sick visit, follow-up, etc.)'
        },
        appointment_date_time: {
          type: Type.STRING,
          description: 'Appointment date and time in ISO format'
        },
        chief_complaint: {
          type: Type.STRING,
          description: 'Main reason for the visit'
        },
        symptoms: {
          type: Type.ARRAY,
          description: 'List of symptoms',
          items: { type: Type.STRING }
        },
        questions: {
          type: Type.ARRAY,
          description: 'Questions to ask the doctor',
          items: { type: Type.STRING }
        },
        height: {
          type: Type.STRING,
          description: 'Current height measurement'
        },
        weight: {
          type: Type.STRING,
          description: 'Current weight measurement'
        },
        temperature: {
          type: Type.STRING,
          description: 'Temperature if relevant'
        },
        medications: {
          type: Type.ARRAY,
          description: 'Current medications',
          items: {
            type: Type.OBJECT,
            properties: {
              name: { type: Type.STRING },
              dosage: { type: Type.STRING },
              frequency: { type: Type.STRING }
            }
          }
        }
      },
      required: ['user_id', 'child_name', 'guardian_name', 'contact_phone']
    }
  },
  {
    name: 'update_family_member',
    description: 'Update existing family member information including medical conditions, allergies, or other details.',
    parameters: {
      type: Type.OBJECT,
      properties: {
        user_id: {
          type: Type.STRING,
          description: 'The user UUID from get_user_context response'
        },
        family_member_name: {
          type: Type.STRING,
          description: 'Name of the family member to update'
        },
        updates: {
          type: Type.OBJECT,
          description: 'Fields to update',
          properties: {
            medical_conditions: { type: Type.ARRAY, items: { type: Type.STRING } },
            allergies: { type: Type.ARRAY, items: { type: Type.STRING } },
            medications: { type: Type.ARRAY, items: { type: Type.OBJECT } },
            additional_notes: { type: Type.STRING },
            date_of_birth: { type: Type.STRING },
            gender: { type: Type.STRING }
          }
        }
      },
      required: ['user_id', 'family_member_name', 'updates']
    }
  },
  {
    name: 'save_conversation_summary',
    description: 'Save a summary of the current conversation to audio_chat_sessions for future reference.',
    parameters: {
      type: Type.OBJECT,
      properties: {
        user_id: {
          type: Type.STRING,
          description: 'The user UUID from get_user_context response'
        },
        session_summary: {
          type: Type.STRING,
          description: 'Summary of the conversation topics and key points'
        },
        key_topics: {
          type: Type.ARRAY,
          description: 'Main topics discussed',
          items: { type: Type.STRING }
        },
        action_items: {
          type: Type.ARRAY,
          description: 'Any action items or follow-ups mentioned',
          items: { type: Type.STRING }
        },
        child_focus: {
          type: Type.STRING,
          description: 'Which child was the main focus of the conversation'
        }
      },
      required: ['user_id', 'session_summary']
    }
  }
]

// Implementation functions
export async function handleSaveCareLogEntry(args: {
  user_id: string
  child_name: string
  note: string
  tags?: string[]
  content_type?: string
  weather?: string
}) {
  try {
    console.log('📝 Mae saving care log entry:', args)

    // Validate UUID
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(args.user_id)) {
      return {
        success: false,
        message: 'Invalid user_id format. Call get_user_context first.',
        error: 'invalid_user_id'
      }
    }

    const response = await fetch('/api/mae-database/care-log', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        user_id: args.user_id,
        child: args.child_name,
        note: args.note,
        tags: args.tags || [],
        content_type: args.content_type || 'daily_log',
        weather: args.weather
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      return {
        success: false,
        message: `Error saving care log entry: ${response.status}`,
        error: errorText
      }
    }

    const result = await response.json()
    return {
      success: true,
      message: `Successfully saved care log entry for ${args.child_name}`,
      entry_id: result.entry_id
    }

  } catch (error) {
    console.error('❌ Error saving care log entry:', error)
    return {
      success: false,
      message: 'Error saving care log entry',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export async function handleSaveToLibrary(args: {
  user_id: string
  content_type: string
  title: string
  content: string
  metadata?: any
}) {
  try {
    console.log('📚 Mae saving to library:', args)

    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    if (!uuidRegex.test(args.user_id)) {
      return {
        success: false,
        message: 'Invalid user_id format. Call get_user_context first.',
        error: 'invalid_user_id'
      }
    }

    const response = await fetch('/api/mae-database/library', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        user_id: args.user_id,
        content_type: args.content_type,
        title: args.title,
        content: args.content,
        metadata: args.metadata || {}
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      return {
        success: false,
        message: `Error saving to library: ${response.status}`,
        error: errorText
      }
    }

    const result = await response.json()
    return {
      success: true,
      message: `Successfully saved "${args.title}" to your library`,
      library_id: result.library_id
    }

  } catch (error) {
    console.error('❌ Error saving to library:', error)
    return {
      success: false,
      message: 'Error saving to library',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}
