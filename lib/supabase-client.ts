import { createClient } from '@supabase/supabase-js'

// Database type definitions
export interface User {
  id: string
  auth_user_id?: string  // New field to link with auth.users
  email: string
  name: string
  role: 'parent' | 'guardian' | 'caregiver'
  phone?: string
  zip?: string
  date_of_birth?: string
  emergency_contact?: {
    name: string
    phone: string
    relationship: string
  }
  preferences?: {
    notifications?: boolean
    language?: string
    [key: string]: any
  }
  onboarding_completed: boolean
  onboarding_step: 'welcome' | 'user_info' | 'family_info' | 'complete'
  created_at: string
  updated_at: string
}

export interface FamilyMember {
  id: string
  user_id: string
  name: string
  date_of_birth: string
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say'
  relationship?: string // Made optional since it doesn't exist in the current database schema
  medical_conditions?: string[]
  allergies?: string[]
  medications?: {
    name: string
    dosage: string
    frequency: string
  }[]
  additional_notes?: string
  avatar?: string
  is_primary?: boolean // Made optional since not returned from database
  created_at: string
  updated_at: string
}

export interface OnboardingSession {
  id: string
  user_id?: string
  session_data: {
    [key: string]: any
  }
  current_step: string
  validation_errors: any[]
  completed_at?: string
  expires_at: string
  created_at: string
  updated_at: string
}

export interface OnboardingProgress {
  user_id: string
  current_step: string
  onboarding_completed: boolean
  family_members_count: number
  session_data: {
    [key: string]: any
  }
}

// Supabase client configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || `https://${process.env.SUPABASE_PROJECT_ID}.supabase.co`
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || ''

if (!supabaseAnonKey) {
  throw new Error('NEXT_PUBLIC_SUPABASE_ANON_KEY is required')
}

// Client for browser usage
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Function to create admin client when needed (server-side only)
function createSupabaseAdmin() {
  // Only allow admin client creation in server environment
  if (typeof window !== 'undefined') {
    throw new Error('Admin client cannot be used in browser environment')
  }

  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY
  
  if (!supabaseServiceKey) {
    throw new Error('SUPABASE_SERVICE_ROLE_KEY is required for admin operations')
  }
  
  return createClient(
    supabaseUrl,
    supabaseServiceKey,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )
}

// Lazy initialization for admin client
let _supabaseAdmin: ReturnType<typeof createClient> | null = null

export const supabaseAdmin = {
  get client() {
    if (!_supabaseAdmin) {
      _supabaseAdmin = createSupabaseAdmin() as ReturnType<typeof createClient>
    }
    return _supabaseAdmin
  }
}

// Helper functions for onboarding operations
export class OnboardingService {
  private get client() {
    // Only allow admin operations in server environment
    if (typeof window !== 'undefined') {
      throw new Error('OnboardingService can only be used in server environment')
    }
    return supabaseAdmin.client
  }

  async createUser(userData: Partial<User>): Promise<{ data: User | null; error: any }> {
    try {
      // Clean userData to avoid foreign key issues and generate UUID
      const { id: _, ...cleanUserData } = userData as any
      
      // First, check if there are existing users to understand the constraint
      const existingUsers = await this.client
        .from('users')
        .select('id')
        .limit(1)
      
      console.log('🔍 Existing users for reference:', existingUsers.data)
      
      const userToInsert = {
        id: crypto.randomUUID(),
        ...cleanUserData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      console.log('🔄 Attempting to create user:', userToInsert.email)

      const { data, error } = await this.client
        .from('users')
        .insert(userToInsert)
        .select()
        .single()

      if (error) {
        console.error('❌ User creation failed:', error.message)
        console.error('❌ Error details:', {
          message: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint,
          full_error: error
        })
        console.error('❌ User data that failed:', userToInsert)
        
        // If it's a foreign key constraint error, try an alternative approach
        if (error.message.includes('users_id_fkey')) {
          console.log('🔧 Foreign key constraint detected, attempting workaround...')
          
          // Try updating an existing user instead of creating new one
          if (existingUsers.data && existingUsers.data.length > 0) {
            const updateResult = await this.client
              .from('users')
              .update({
                ...cleanUserData,
                updated_at: new Date().toISOString()
              })
              .eq('id', existingUsers.data[0].id as string)
              .select()
              .single()
            
            if (updateResult.error) {
              console.error('❌ User update also failed:', updateResult.error.message)
              return { data: null, error: updateResult.error }
            }
            
            console.log('✅ User updated successfully as workaround')
            return { data: updateResult.data as unknown as User, error: null }
          }
        }
      } else {
        console.log('✅ User created successfully:', data?.id)
      }

      return { data: data as unknown as User, error }
    } catch (error) {
      console.error('❌ User creation exception:', error)
      return { data: null, error }
    }
  }

  async updateUser(userId: string, userData: Partial<User>): Promise<{ data: User | null; error: any }> {
    try {
      const { data, error } = await this.client
        .from('users')
        .update({
          ...userData,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)
        .select()
        .single()

      return { data: data as unknown as User, error }
    } catch (error) {
      return { data: null, error }
    }
  }

  async getUserByEmail(email: string): Promise<{ data: User | null; error: any }> {
    try {
      const { data, error } = await this.client
        .from('users')
        .select('*')
        .eq('email', email)
        .single()

      return { data: data as unknown as User, error }
    } catch (error) {
      return { data: null, error }
    }
  }

  async createFamilyMember(memberData: Partial<FamilyMember>): Promise<{ data: FamilyMember | null; error: any }> {
    try {
      // Clean memberData and generate UUID
      const { id: _, ...cleanMemberData } = memberData as any
      
      const memberToInsert = {
        id: crypto.randomUUID(),
        ...cleanMemberData,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }

      console.log('🔄 Attempting to create family member:', memberToInsert.name)

      const { data, error } = await this.client
        .from('family_members')
        .insert(memberToInsert)
        .select()
        .single()

      if (error) {
        console.error('❌ Family member creation failed:', error.message)
        console.error('❌ Family member error details:', {
          message: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint,
          full_error: error
        })
        console.error('❌ Family member data that failed:', memberToInsert)
        
        // If it's a foreign key constraint error, try workaround similar to users
        if (error.message.includes('violates foreign key constraint')) {
          console.log('🔧 Foreign key constraint detected for family member, attempting workaround...')
          
          // Try to find existing family members to update instead
          const existingMembers = await this.client
            .from('family_members')
            .select('id')
            .eq('user_id', cleanMemberData.user_id)
            .limit(1)
          
          if (existingMembers.data && existingMembers.data.length > 0) {
            const updateResult = await this.client
              .from('family_members')
              .update({
                ...cleanMemberData,
                updated_at: new Date().toISOString()
              })
              .eq('id', existingMembers.data[0].id as string)
              .select()
              .single()
            
            if (updateResult.error) {
              console.error('❌ Family member update also failed:', updateResult.error.message)
              return { data: null, error: updateResult.error }
            }
            
            console.log('✅ Family member updated successfully as workaround')
            return { data: updateResult.data as unknown as FamilyMember, error: null }
          }
        }
      } else {
        console.log('✅ Family member created successfully:', data?.id)
      }

      return { data: data as unknown as FamilyMember, error }
    } catch (error) {
      console.error('❌ Family member creation exception:', error)
      return { data: null, error }
    }
  }

  async updateFamilyMember(memberId: string, memberData: Partial<FamilyMember>): Promise<{ data: FamilyMember | null; error: any }> {
    try {
      const { data, error } = await this.client
        .from('family_members')
        .update({
          ...memberData,
          updated_at: new Date().toISOString()
        })
        .eq('id', memberId)
        .select()
        .single()

      return { data: data as unknown as FamilyMember, error }
    } catch (error) {
      return { data: null, error }
    }
  }

  async getFamilyMembers(userId: string): Promise<{ data: FamilyMember[]; error: any }> {
    try {
      const { data, error } = await this.client
        .from('family_members')
        .select('*')
        .eq('user_id', userId)
        .order('created_at', { ascending: true })

      return { data: data as unknown as FamilyMember[], error }
    } catch (error) {
      return { data: [], error }
    }
  }

  async deleteFamilyMember(memberId: string): Promise<{ success: boolean; error?: any }> {
    try {
      const { error } = await this.client
        .from('family_members')
        .delete()
        .eq('id', memberId)

      if (error) {
        console.error('❌ Family member deletion failed:', error.message)
        return { success: false, error }
      }

      console.log('✅ Family member deleted successfully:', memberId)
      return { success: true }
    } catch (error) {
      console.error('❌ Family member deletion exception:', error)
      return { success: false, error }
    }
  }

  async createOnboardingSession(sessionData: Partial<OnboardingSession>): Promise<{ data: OnboardingSession | null; error: any }> {
    try {
      const { data, error } = await this.client
        .from('onboarding_sessions')
        .insert(sessionData)
        .select()
        .single()

      return { data: data as unknown as OnboardingSession, error }
    } catch (error) {
      return { data: null, error }
    }
  }

  async updateOnboardingSession(sessionId: string, sessionData: Partial<OnboardingSession>): Promise<{ data: OnboardingSession | null; error: any }> {
    try {
      const { data, error } = await this.client
        .from('onboarding_sessions')
        .update({
          ...sessionData,
          updated_at: new Date().toISOString()
        })
        .eq('id', sessionId)
        .select()
        .single()

      return { data: data as unknown as OnboardingSession, error }
    } catch (error) {
      return { data: null, error }
    }
  }

  async getOnboardingProgress(userEmail: string): Promise<{ data: OnboardingProgress | null; error: any }> {
    try {
      const { data, error } = await this.client
        .rpc('get_onboarding_progress', { user_email: userEmail })

      if (error) {
        return { data: null, error }
      }

      return { data: data as unknown as OnboardingProgress, error }
    } catch (error) { 
      return { data: null, error }
    }
  }

  async validateFamilyMemberData(
    name: string,
    dateOfBirth: string,
    relationship: string
  ): Promise<{ data: { valid: boolean; errors: string[] } | null; error: any }> {
    try {
      const { data, error } = await this.client
        .rpc('validate_family_member_data', {
          member_name: name,
          member_dob: dateOfBirth,
          member_relationship: relationship
        })

      return { data: data as unknown as { valid: boolean; errors: string[] } || null, error }
    } catch (error) {
      return { data: null, error }
    }
  }

  async completeOnboarding(userId: string, sessionId: string): Promise<{ success: boolean; error?: any }> {
    try {
      // Update user as onboarding completed
      const { error: userError } = await this.client
        .from('users')
        .update({
          onboarding_completed: true,
          onboarding_step: 'complete',
          updated_at: new Date().toISOString()
        })
        .eq('id', userId)

      if (userError) {
        return { success: false, error: userError }
      }

      // Mark onboarding session as completed
      const { error: sessionError } = await this.client
        .from('onboarding_sessions')
        .update({
          completed_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', sessionId)

      if (sessionError) {
        return { success: false, error: sessionError }
      }

      return { success: true }
    } catch (error) {
      return { success: false, error }
    }
  }

  async cleanupExpiredSessions(): Promise<{ success: boolean; error?: any }> {
    try {
      const { error } = await this.client
        .rpc('cleanup_expired_onboarding_sessions')

      return { success: !error, error }
    } catch (error) {
      return { success: false, error }
    }
  }

  // Get direct client access for complex operations (use sparingly)
  getClient() {
    return this.client
  }
}

// Export singleton instance (lazy initialization)
let _onboardingService: OnboardingService | null = null

export const onboardingService = {
  get instance() {
    if (!_onboardingService) {
      _onboardingService = new OnboardingService()
    }
    return _onboardingService
  }
}

// For backward compatibility
export const getOnboardingService = () => onboardingService.instance