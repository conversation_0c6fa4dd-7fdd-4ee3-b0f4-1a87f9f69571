import { createClient } from '@supabase/supabase-js'
import { auth, currentUser } from '@clerk/nextjs/server'

// Create a Supabase client with service role key for server-side operations
export function createServiceRoleClient() {
  return createClient(
    process.env.SUPABASE_URL!,
    process.env.SUPABASE_SERVICE_ROLE_KEY!,
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    }
  )
}


// Get or create Supabase user from Clerk user
export async function getOrCreateSupabaseUser() {
  const clerkUser = await currentUser()
  
  if (!clerkUser) {
    throw new Error('No authenticated user found')
  }

  const supabase = createServiceRoleClient()
  
  // First, try to get the user by clerk_id
  let { data: user, error } = await supabase
    .from('users')
    .select('*')
    .eq('clerk_id', clerkUser.id)
    .single()

  // If user doesn't exist, create them
  if (error && error.code === 'PGRST116') { // Not found
    const primaryEmail = clerkUser.emailAddresses.find(
      email => email.id === clerkUser.primaryEmailAddressId
    )?.emailAddress

    if (!primaryEmail) {
      throw new Error('No primary email found for user')
    }

    const fullName = `${clerkUser.firstName || ''} ${clerkUser.lastName || ''}`.trim() || 'User'
    
    // Check if user exists with this email but no clerk_id (existing user)
    const { data: existingUser, error: existingError } = await supabase
      .from('users')
      .select('*')
      .eq('email', primaryEmail)
      .single()

    if (existingUser && !existingUser.clerk_id) {
      // Update existing user with clerk_id
      const { data: updatedUser, error: updateError } = await supabase
        .from('users')
        .update({ 
          clerk_id: clerkUser.id,
          name: fullName,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingUser.id)
        .select()
        .single()

      if (updateError) throw updateError
      return updatedUser
    } else if (!existingUser) {
      // Create new user
      const { data: newUser, error: createError } = await supabase
        .from('users')
        .insert({
          clerk_id: clerkUser.id,
          email: primaryEmail,
          name: fullName,
          role: 'parent',
          preferences: {},
          onboarding_completed: false,
          onboarding_step: 'welcome'
        })
        .select()
        .single()

      if (createError) throw createError
      return newUser
    }
  } else if (error) {
    throw error
  }

  return user
}

// Get user's family members
export async function getUserFamilyMembers(userId: string) {
  const supabase = createServiceRoleClient()
  
  const { data, error } = await supabase
    .from('family_members')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: true })

  if (error) throw error
  return data || []
}

// Update user profile
export async function updateUserProfile(userId: string, updates: any) {
  const supabase = createServiceRoleClient()
  
  const { data, error } = await supabase
    .from('users')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('id', userId)
    .select()
    .single()

  if (error) throw error
  return data
}

// Add family member
export async function addFamilyMember(userId: string, memberData: any) {
  const supabase = createServiceRoleClient()
  
  const { data, error } = await supabase
    .from('family_members')
    .insert({
      user_id: userId,
      ...memberData
    })
    .select()
    .single()

  if (error) throw error
  return data
}

// Update family member
export async function updateFamilyMember(memberId: string, userId: string, updates: any) {
  const supabase = createServiceRoleClient()
  
  const { data, error } = await supabase
    .from('family_members')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('id', memberId)
    .eq('user_id', userId) // Ensure user owns this family member
    .select()
    .single()

  if (error) throw error
  return data
}

// Delete family member
export async function deleteFamilyMember(memberId: string, userId: string) {
  const supabase = createServiceRoleClient()
  
  const { error } = await supabase
    .from('family_members')
    .delete()
    .eq('id', memberId)
    .eq('user_id', userId) // Ensure user owns this family member

  if (error) throw error
}

// Get or create onboarding session
export async function getOrCreateOnboardingSession(userId: string) {
  const supabase = createServiceRoleClient()
  
  // Check for existing active session
  let { data: session, error } = await supabase
    .from('onboarding_sessions')
    .select('*')
    .eq('user_id', userId)
    .is('completed_at', null)
    .gt('expires_at', new Date().toISOString())
    .single()

  if (error && error.code === 'PGRST116') { // Not found
    // Create new session
    const { data: newSession, error: createError } = await supabase
      .from('onboarding_sessions')
      .insert({
        user_id: userId,
        session_data: {},
        current_step: 'welcome'
      })
      .select()
      .single()

    if (createError) throw createError
    return newSession
  } else if (error) {
    throw error
  }

  return session
}

// Update onboarding session
export async function updateOnboardingSession(sessionId: string, updates: any) {
  const supabase = createServiceRoleClient()
  
  const { data, error } = await supabase
    .from('onboarding_sessions')
    .update({
      ...updates,
      updated_at: new Date().toISOString()
    })
    .eq('id', sessionId)
    .select()
    .single()

  if (error) throw error
  return data
}

// Complete onboarding
export async function completeOnboarding(userId: string, sessionId: string) {
  const supabase = createServiceRoleClient()
  
  // Start a transaction by updating both tables
  const { error: sessionError } = await supabase
    .from('onboarding_sessions')
    .update({
      completed_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    })
    .eq('id', sessionId)

  if (sessionError) throw sessionError

  const { data, error: userError } = await supabase
    .from('users')
    .update({
      onboarding_completed: true,
      onboarding_step: 'complete',
      updated_at: new Date().toISOString()
    })
    .eq('id', userId)
    .select()
    .single()

  if (userError) throw userError
  return data
}