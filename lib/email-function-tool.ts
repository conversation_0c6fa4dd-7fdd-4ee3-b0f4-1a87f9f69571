// Function tool definition for Gemini Live API to send conversation summaries via email
import { Type } from '@google/genai';

export const emailConversationTool = {
  name: 'send_conversation_email',
  description: 'Send a conversation summary via email to the user. This function captures the user\'s question and AI response, then emails it to the specified recipient with a professional format.',
  parameters: {
    type: Type.OBJECT,
    properties: {
      recipient_email: {
        type: Type.STRING,
        description: 'The email address where the conversation summary should be sent. Must be a valid email format.',
        pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$'
      },
      user_question: {
        type: Type.STRING,
        description: 'The original question or input from the user that initiated this conversation.'
      },
      ai_response: {
        type: Type.STRING,
        description: 'The AI model\'s response to the user\'s question. This should be the complete response provided by <PERSON>.'
      },
      subject: {
        type: Type.STRING,
        description: 'Optional custom subject line for the email. If not provided, a default subject will be used.',
        default: 'Your Our Kidz AI Conversation Summary'
      },
      include_context: {
        type: Type.BOOLEAN,
        description: 'Whether to include additional conversation context in the email.',
        default: false
      },
      conversation_context: {
        type: Type.STRING,
        description: 'Optional additional context about the conversation, such as previous messages or session information.'
      },
      sources: {
        type: Type.ARRAY,
        description: 'New optional array of sources for reference links',
        items: {
          type: Type.OBJECT,
          properties: {
            title: {
              type: Type.STRING,
              description: 'Title of the source'
            },
            url: {
              type: Type.STRING,
              description: 'URL of the source'
            }
          },
          required: ['title', 'url']
        }
      }
    },
    required: ['recipient_email', 'user_question', 'ai_response']
  }
}

// Import email confirmation functions
import {
  isEmailConfirmed,
  getConfirmedEmailData,
  clearEmailConfirmationState
} from './email-confirmation-tool'

// Function to handle the email tool call
export async function handleEmailConversation(parameters: {
  recipient_email: string
  user_question: string
  ai_response: string
  subject?: string
  include_context?: boolean
  conversation_context?: string
  sources?: { title: string; url: string }[]
}) {
  try {
    // Validate required parameters
    if (!parameters.recipient_email || !parameters.user_question || !parameters.ai_response) {
      return {
        success: false,
        error: 'Missing required parameters: recipient_email, user_question, or ai_response'
      }
    }

    // Use the parameters directly instead of confirmation system
    console.log('📧 Using direct parameters for email sending:', {
      recipient: parameters.recipient_email,
      hasQuestion: !!parameters.user_question,
      hasResponse: !!parameters.ai_response,
      hasSources: !!(parameters.sources && parameters.sources.length > 0),
      sourcesCount: parameters.sources?.length || 0
    })

    // Generate a proper UUID for session_id
    function generateUUID(): string {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        const r = Math.random() * 16 | 0
        const v = c == 'x' ? r : (r & 0x3 | 0x8)
        return v.toString(16)
      })
    }

    // Use the direct parameters
    const emailData = {
      recipient_email: parameters.recipient_email,
      user_question: parameters.user_question,
      ai_response: parameters.ai_response,
      sources: parameters.sources || [],
      subject: parameters.subject,
      include_context: parameters.include_context,
      conversation_context: parameters.conversation_context,
      timestamp: new Date().toISOString(),
      session_id: generateUUID()
    }

    console.log('📧 Email function called with direct data:', {
      recipient: emailData.recipient_email,
      hasQuestion: !!emailData.user_question,
      hasResponse: !!emailData.ai_response,
      hasSources: !!(emailData.sources && emailData.sources.length > 0),
      sourcesCount: emailData.sources?.length || 0
    })

    // Log full data only in development
    if (process.env.NODE_ENV === 'development') {
      console.log('📧 Full email data being sent:', emailData)
    }

    // Call the email API with proper URL handling for production
    const getBaseUrl = () => {
      // In browser context
      if (typeof window !== 'undefined') {
        return window.location.origin
      }

      // In server context - use environment variables
      if (process.env.VERCEL_URL) {
        return `https://${process.env.VERCEL_URL}`
      }

      if (process.env.NEXT_PUBLIC_VERCEL_URL) {
        return `https://${process.env.NEXT_PUBLIC_VERCEL_URL}`
      }

      // Fallback for local development
      return `http://localhost:${process.env.PORT || 3000}`
    }

    const baseUrl = getBaseUrl()
    const apiUrl = `${baseUrl}/api/send-conversation`

    console.log('📧 Sending email request to:', apiUrl)

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(emailData),
    })

    console.log('📧 Email API response status:', response.status)

    let result
    try {
      result = await response.json()
    } catch (parseError) {
      console.error('Failed to parse response JSON:', parseError)
      return {
        success: false,
        error: `Server response parsing error: ${response.status} ${response.statusText}`
      }
    }

    console.log('Email API response:', { status: response.status, result })

    if (!response.ok) {
      return {
        success: false,
        error: result?.error || `HTTP ${response.status}: ${response.statusText}`
      }
    }

    // No need to clear confirmation state since we're not using it

    return {
      success: true,
      message: `Conversation summary successfully sent to ${emailData.recipient_email}`,
      timestamp: emailData.timestamp,
      messageId: result.messageId
    }

  } catch (error) {
    console.error('Error in handleEmailConversation:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network or unknown error occurred while sending email'
    }
  }
}

// Type definitions for TypeScript
export interface EmailConversationParams {
  recipient_email: string
  user_question: string
  ai_response: string
  subject?: string
  include_context?: boolean
  conversation_context?: string
  sources?: { title: string; url: string }[]
}

export interface EmailConversationResult {
  success: boolean
  message?: string
  error?: string
  timestamp?: string
  messageId?: string
}
