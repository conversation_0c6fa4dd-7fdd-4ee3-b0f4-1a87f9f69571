//--------------------------------------------------------------
// lib/email-confirmation-tool.ts
//--------------------------------------------------------------

// Email confirmation state management
interface EmailConfirmationState {
  pendingEmail?: string
  userQuestion?: string
  aiResponse?: string
  sources?: { title: string; url: string }[]
  conversationContext?: string
  isConfirmed: boolean
  timestamp?: string
}

// Global state for email confirmation (in production, use proper state management)
let emailConfirmationState: EmailConfirmationState = { isConfirmed: false }

// Tool declaration for email confirmation
export const emailConfirmationToolDeclaration = {
  name: 'confirm_email_address',
  description: 'Confirm the email address, but don\'t spell it out. This must be called before send_conversation_email.',
  parameters: {
    type: 'object',
    properties: {
      email_address: {
        type: 'string',
        description: 'The email address to confirm'
      },
      user_question: {
        type: 'string', 
        description: 'The user\'s original question'
      },
      ai_response: {
        type: 'string',
        description: 'The AI response to include in the email'
      },
      sources: {
        type: 'array',
        items: {
          type: 'object',
          properties: {
            title: { type: 'string' },
            url: { type: 'string' }
          },
          required: ['title', 'url']
        },
        description: 'Sources to include in the email'
      },
      conversation_context: {
        type: 'string',
        description: 'Additional conversation context'
      }
    },
    required: ['email_address', 'user_question', 'ai_response']
  }
}

// Function to handle email confirmation
export async function handleEmailConfirmation(parameters: {
  email_address: string
  user_question: string
  ai_response: string
  sources?: { title: string; url: string }[]
  conversation_context?: string
}) {
  try {
    console.log('📧 Email confirmation requested for:', parameters.email_address)
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(parameters.email_address)) {
      return {
        success: false,
        error: 'Invalid email address format. Please provide a valid email address.',
        requiresUserInput: true
      }
    }

    // Store the pending email data
    emailConfirmationState = {
      pendingEmail: parameters.email_address,
      userQuestion: parameters.user_question,
      aiResponse: parameters.ai_response,
      sources: parameters.sources,
      conversationContext: parameters.conversation_context,
      isConfirmed: false,
      timestamp: new Date().toISOString()
    }

    console.log('📧 Email confirmation state set:', {
      email: parameters.email_address,
      hasQuestion: !!parameters.user_question,
      hasResponse: !!parameters.ai_response,
      hasSources: !!(parameters.sources && parameters.sources.length > 0),
      hasContext: !!parameters.conversation_context,
      sourcesCount: parameters.sources?.length || 0,
      contextLength: parameters.conversation_context?.length || 0
    })

    // Warn if no sources provided
    if (!parameters.sources || parameters.sources.length === 0) {
      console.warn('⚠️  WARNING: No sources provided in email confirmation! This may indicate Mae is not extracting grounding metadata properly.');
    }

    // Return confirmation message for Mae to speak
    return {
      success: true,
      message: `I have your email as ${parameters.email_address}. Is that correct?`,
      pendingConfirmation: true,
      emailAddress: parameters.email_address
    }

  } catch (error) {
    console.error('Error in handleEmailConfirmation:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred during email confirmation'
    }
  }
}

// Tool declaration for processing user confirmation
export const processConfirmationToolDeclaration = {
  name: 'process_email_confirmation',
  description: 'Process the user\'s confirmation response (yes/no) for the email address',
  parameters: {
    type: 'object',
    properties: {
      confirmed: {
        type: 'boolean',
        description: 'Whether the user confirmed the email address is correct'
      },
      corrected_email: {
        type: 'string',
        description: 'If user said no, the corrected email address'
      }
    },
    required: ['confirmed']
  }
}

// Function to process user confirmation
export async function handleProcessConfirmation(parameters: {
  confirmed: boolean
  corrected_email?: string
}) {
  try {
    console.log('📧 Processing email confirmation:', parameters)

    if (!emailConfirmationState.pendingEmail) {
      return {
        success: false,
        error: 'No pending email confirmation found. Please provide your email address first.'
      }
    }

    if (parameters.confirmed) {
      // User confirmed the email is correct
      emailConfirmationState.isConfirmed = true
      
      return {
        success: true,
        message: 'Great! I\'ll send your summary to that email address now.',
        readyToSend: true,
        emailData: {
          recipient_email: emailConfirmationState.pendingEmail,
          user_question: emailConfirmationState.userQuestion,
          ai_response: emailConfirmationState.aiResponse,
          sources: emailConfirmationState.sources,
          conversation_context: emailConfirmationState.conversationContext
        }
      }
    } else {
      // User said no - either they want to correct it or cancel
      if (parameters.corrected_email) {
        // User provided a corrected email
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!emailRegex.test(parameters.corrected_email)) {
          return {
            success: false,
            error: 'The corrected email address format is invalid. Please provide a valid email address.',
            requiresUserInput: true
          }
        }

        // Update the pending email
        emailConfirmationState.pendingEmail = parameters.corrected_email
        emailConfirmationState.isConfirmed = false

        return {
          success: true,
          message: `I have your corrected email as ${parameters.corrected_email}. Is that correct?`,
          pendingConfirmation: true,
          emailAddress: parameters.corrected_email
        }
      } else {
        // User said no but didn't provide correction
        return {
          success: true,
          message: 'No problem! What\'s the correct email address?',
          requiresUserInput: true
        }
      }
    }

  } catch (error) {
    console.error('Error in handleProcessConfirmation:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred while processing confirmation'
    }
  }
}


// Function to get current confirmation state
export function getEmailConfirmationState(): EmailConfirmationState {
  return { ...emailConfirmationState }
}

// Function to clear confirmation state
export function clearEmailConfirmationState(): void {
  emailConfirmationState = { isConfirmed: false }
}

// Function to check if email is confirmed and ready to send
export function isEmailConfirmed(): boolean {
  return emailConfirmationState.isConfirmed && !!emailConfirmationState.pendingEmail
}

// Function to get confirmed email data
export function getConfirmedEmailData() {
  if (!isEmailConfirmed()) {
    return null
  }
  
  return {
    recipient_email: emailConfirmationState.pendingEmail!,
    user_question: emailConfirmationState.userQuestion!,
    ai_response: emailConfirmationState.aiResponse!,
    sources: emailConfirmationState.sources,
    conversation_context: emailConfirmationState.conversationContext
  }
}
