// Function tool definition for Gemini Live API to send emails via SMTP
import { Type } from '@google/genai';
import { emailTemplates } from './email-templates';

export const sendEmailTool = {
  name: 'send_email',
  description: 'Send an email via SMTP to a specified recipient. This function sends emails using the configured SMTP service and optionally logs the activity to a webhook for tracking purposes. IMPORTANT: You must ALWAYS include sources for any information provided in the email.',
  parameters: {
    type: Type.OBJECT,
    properties: {
      recipient_email: {
        type: Type.STRING,
        description: 'The email address where the email should be sent. Must be a valid email format.',
        pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$'
      },
      subject: {
        type: Type.STRING,
        description: 'The subject line for the email. Should be descriptive and relevant to the email content.'
      },
      text_content: {
        type: Type.STRING,
        description: 'The plain text content of the email. This is required and will be used as the email body.'
      },
      html_content: {
        type: Type.STRING,
        description: 'Optional HTML content for the email. If provided, the email will support rich formatting, links, and styling.'
      },
      sender_name: {
        type: Type.STRING,
        description: 'Optional custom sender name to display in the "From" field. If not provided, will use the default configured sender name.'
      },
      log_to_webhook: {
        type: Type.BOOLEAN,
        description: 'Whether to log the email activity to the configured webhook. Defaults to true for tracking purposes.',
        default: true
      },
      webhook_url: {
        type: Type.STRING,
        description: 'Optional custom webhook URL for logging. If not provided, will use the default configured webhook URL.'
      },
      sources: {
        type: Type.ARRAY,
        description: 'REQUIRED: Array of sources for any information provided in the email. You must ALWAYS search Google and include sources for any factual information.',
        items: {
          type: Type.OBJECT,
          properties: {
            title: {
              type: Type.STRING,
              description: 'Title of the source'
            },
            url: {
              type: Type.STRING,
              description: 'URL of the source'
            }
          },
          required: ['title', 'url']
        }
      }
    },
    required: ['recipient_email', 'subject', 'text_content', 'sources']
  }
}

export const checkSmtpTool = {
  name: 'check_smtp_connection',
  description: 'Test the SMTP connection to verify that email sending is properly configured. This function checks if the SMTP credentials and settings are working correctly.',
  parameters: {
    type: Type.OBJECT,
    properties: {},
    required: []
  }
}

export const sendWelcomeEmailTool = {
  name: 'send_welcome_email',
  description: 'Send a branded welcome email to new users joining the Our Kidz platform. Uses a pre-designed template with Our Kidz branding. IMPORTANT: You must ALWAYS search Google for current parenting information and include sources in the email.',
  parameters: {
    type: Type.OBJECT,
    properties: {
      recipient_email: {
        type: Type.STRING,
        description: 'The email address of the new user to welcome.',
        pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$'
      },
      recipient_name: {
        type: Type.STRING,
        description: 'The name of the person to welcome (optional, will personalize the greeting).'
      },
      log_to_webhook: {
        type: Type.BOOLEAN,
        description: 'Whether to log the email activity to the webhook. Defaults to true.',
        default: true
      },
      sources: {
        type: Type.ARRAY,
        description: 'REQUIRED: Array of sources from Google search for any parenting information included in the welcome email. You must ALWAYS search Google first.',
        items: {
          type: Type.OBJECT,
          properties: {
            title: {
              type: Type.STRING,
              description: 'Title of the source'
            },
            url: {
              type: Type.STRING,
              description: 'URL of the source'
            }
          },
          required: ['title', 'url']
        }
      }
    },
    required: ['recipient_email', 'sources']
  }
}

export const sendContactConfirmationTool = {
  name: 'send_contact_confirmation',
  description: 'Send a confirmation email to someone who submitted a contact form, thanking them and confirming receipt of their message. IMPORTANT: You must ALWAYS search Google for relevant information to include in the response and include sources.',
  parameters: {
    type: Type.OBJECT,
    properties: {
      recipient_email: {
        type: Type.STRING,
        description: 'The email address of the person who submitted the contact form.',
        pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$'
      },
      recipient_name: {
        type: Type.STRING,
        description: 'The name of the person who submitted the form.'
      },
      user_message: {
        type: Type.STRING,
        description: 'The original message they submitted through the contact form.'
      },
      log_to_webhook: {
        type: Type.BOOLEAN,
        description: 'Whether to log the email activity to the webhook. Defaults to true.',
        default: true
      },
      sources: {
        type: Type.ARRAY,
        description: 'REQUIRED: Array of sources from Google search for any information included in the confirmation email. You must ALWAYS search Google first.',
        items: {
          type: Type.OBJECT,
          properties: {
            title: {
              type: Type.STRING,
              description: 'Title of the source'
            },
            url: {
              type: Type.STRING,
              description: 'URL of the source'
            }
          },
          required: ['title', 'url']
        }
      }
    },
    required: ['recipient_email', 'sources']
  }
}

export const sendAdminNotificationTool = {
  name: 'send_admin_notification',
  description: 'Send a notification email to admin about a new contact form submission or other important events.',
  parameters: {
    type: Type.OBJECT,
    properties: {
      contact_name: {
        type: Type.STRING,
        description: 'Name of the person who submitted the contact form.'
      },
      contact_email: {
        type: Type.STRING,
        description: 'Email address of the person who submitted the contact form.',
        pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$'
      },
      contact_phone: {
        type: Type.STRING,
        description: 'Phone number of the contact (optional).'
      },
      contact_message: {
        type: Type.STRING,
        description: 'The message submitted through the contact form.'
      },
      admin_email: {
        type: Type.STRING,
        description: 'Admin email address to send notification to. Defaults to configured admin email.',
        pattern: '^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$'
      },
      log_to_webhook: {
        type: Type.BOOLEAN,
        description: 'Whether to log the email activity to the webhook. Defaults to true.',
        default: true
      }
    },
    required: ['contact_name', 'contact_email', 'contact_message']
  }
}

// Function to handle the send email tool call
export async function handleSendEmail(parameters: {
  recipient_email: string
  subject: string
  text_content: string
  html_content?: string
  sender_name?: string
  log_to_webhook?: boolean
  webhook_url?: string
  sources?: { title: string; url: string }[]
}) {
  try {
    // Validate required parameters
    if (!parameters.recipient_email || !parameters.subject || !parameters.text_content) {
      return {
        success: false,
        error: 'Missing required parameters: recipient_email, subject, or text_content'
      }
    }

    // Validate sources requirement
    if (!parameters.sources || parameters.sources.length === 0) {
      return {
        success: false,
        error: 'Sources are required for all emails. You must use [googleSearch] and provide sources for any information included in the email.'
      }
    }

    console.log('📧 Send email function called with parameters:', {
      recipient: parameters.recipient_email,
      subject: parameters.subject,
      hasTextContent: !!parameters.text_content,
      hasHtmlContent: !!parameters.html_content,
      senderName: parameters.sender_name,
      logToWebhook: parameters.log_to_webhook,
      hasCustomWebhook: !!parameters.webhook_url,
      sourcesCount: parameters.sources?.length || 0,
      sources: parameters.sources
    })

    // Call the email API
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : ''
    const apiUrl = `${baseUrl}/api/send-email`
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(parameters),
    })

    let result
    try {
      result = await response.json()
    } catch (parseError) {
      console.error('Failed to parse response JSON:', parseError)
      return {
        success: false,
        error: `Server response parsing error: ${response.status} ${response.statusText}`
      }
    }

    console.log('Send email API response:', { status: response.status, result })

    if (!response.ok) {
      return {
        success: false,
        error: result?.error || `HTTP ${response.status}: ${response.statusText}`,
        details: result?.details
      }
    }

    return {
      success: true,
      message: `Email successfully sent to ${parameters.recipient_email}`,
      messageId: result.messageId,
      recipient: result.recipient,
      subject: result.subject,
      timestamp: result.timestamp
    }

  } catch (error) {
    console.error('Error in handleSendEmail:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network or unknown error occurred while sending email'
    }
  }
}

// Function to handle the welcome email tool call
export async function handleSendWelcomeEmail(parameters: {
  recipient_email: string
  recipient_name?: string
  log_to_webhook?: boolean
  sources: { title: string; url: string }[]
}) {
  try {
    if (!parameters.recipient_email || !parameters.sources || parameters.sources.length === 0) {
      return {
        success: false,
        error: 'Missing required parameters: recipient_email and sources. You must use [googleSearch] and provide sources for welcome email content.'
      }
    }

    console.log('🎉 Send welcome email function called:', {
      recipient: parameters.recipient_email,
      name: parameters.recipient_name,
      logToWebhook: parameters.log_to_webhook,
      sourcesCount: parameters.sources.length,
      sources: parameters.sources
    })

    // Generate welcome email using template with sources
    const emailContent = emailTemplates.welcome(parameters.recipient_name || '', parameters.sources);

    // Send the email using the base send email function
    const result = await handleSendEmail({
      recipient_email: parameters.recipient_email,
      subject: emailContent.subject,
      text_content: emailContent.text,
      html_content: emailContent.html,
      sender_name: 'Our Kidz Team',
      log_to_webhook: parameters.log_to_webhook,
      sources: parameters.sources
    });

    return result;

  } catch (error) {
    console.error('Error in handleSendWelcomeEmail:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred while sending welcome email'
    }
  }
}

// Function to handle the contact confirmation email tool call
export async function handleSendContactConfirmation(parameters: {
  recipient_email: string
  recipient_name?: string
  user_message?: string
  log_to_webhook?: boolean
  sources: { title: string; url: string }[]
}) {
  try {
    if (!parameters.recipient_email || !parameters.sources || parameters.sources.length === 0) {
      return {
        success: false,
        error: 'Missing required parameters: recipient_email and sources. You must use [googleSearch] and provide sources for contact confirmation content.'
      }
    }

    console.log('📧 Send contact confirmation function called:', {
      recipient: parameters.recipient_email,
      name: parameters.recipient_name,
      hasMessage: !!parameters.user_message,
      logToWebhook: parameters.log_to_webhook,
      sourcesCount: parameters.sources.length,
      sources: parameters.sources
    })

    // Generate contact confirmation email using template with sources
    const emailContent = emailTemplates.contactConfirmation(
      parameters.recipient_name || '',
      parameters.user_message || '',
      parameters.sources
    );

    // Send the email using the base send email function
    const result = await handleSendEmail({
      recipient_email: parameters.recipient_email,
      subject: emailContent.subject,
      text_content: emailContent.text,
      html_content: emailContent.html,
      sender_name: 'Our Kidz Team',
      log_to_webhook: parameters.log_to_webhook,
      sources: parameters.sources
    });

    return result;

  } catch (error) {
    console.error('Error in handleSendContactConfirmation:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred while sending contact confirmation'
    }
  }
}

// Function to handle the admin notification email tool call
export async function handleSendAdminNotification(parameters: {
  contact_name: string
  contact_email: string
  contact_phone?: string
  contact_message: string
  admin_email?: string
  log_to_webhook?: boolean
}) {
  try {
    if (!parameters.contact_name || !parameters.contact_email || !parameters.contact_message) {
      return {
        success: false,
        error: 'Missing required parameters: contact_name, contact_email, or contact_message'
      }
    }

    const adminEmail = parameters.admin_email || process.env.ADMIN_EMAIL || '<EMAIL>'; // Admin email from env

    console.log('🔔 Send admin notification function called:', {
      contactName: parameters.contact_name,
      contactEmail: parameters.contact_email,
      adminEmail: adminEmail,
      hasPhone: !!parameters.contact_phone,
      logToWebhook: parameters.log_to_webhook
    })

    // Generate admin notification email using template
    const emailContent = emailTemplates.adminNotification({
      name: parameters.contact_name,
      email: parameters.contact_email,
      phone: parameters.contact_phone,
      message: parameters.contact_message
    });

    // Send the email using the base send email function
    const result = await handleSendEmail({
      recipient_email: adminEmail,
      subject: emailContent.subject,
      text_content: emailContent.text,
      html_content: emailContent.html,
      sender_name: 'Our Kidz System',
      log_to_webhook: parameters.log_to_webhook
    });

    return result;

  } catch (error) {
    console.error('Error in handleSendAdminNotification:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred while sending admin notification'
    }
  }
}

// Function to handle the check SMTP tool call
export async function handleCheckSmtp() {
  try {
    console.log('🔧 Check SMTP function called')

    // Call the check SMTP API
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : ''
    const apiUrl = `${baseUrl}/api/check-smtp`
    
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    })

    let result
    try {
      result = await response.json()
    } catch (parseError) {
      console.error('Failed to parse response JSON:', parseError)
      return {
        success: false,
        error: `Server response parsing error: ${response.status} ${response.statusText}`
      }
    }

    console.log('Check SMTP API response:', { status: response.status, result })

    if (!response.ok) {
      return {
        success: false,
        error: result?.error || `HTTP ${response.status}: ${response.statusText}`,
        details: result?.details,
        config: result?.config
      }
    }

    return {
      success: true,
      message: result.message || 'SMTP connection verified successfully',
      config: result.config
    }

  } catch (error) {
    console.error('Error in handleCheckSmtp:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Network or unknown error occurred while checking SMTP'
    }
  }
}

export const emailConversationTool = {
  name: 'send_conversation_email',
  description: 'Sends a summary of the conversation to the user via email. Use this to send detailed information, links, and sources that were discussed. This is the primary tool for sending email summaries from the voice agent.',
  parameters: {
    type: Type.OBJECT,
    properties: {
      recipient_email: {
        type: Type.STRING,
        description: 'The email address of the recipient.',
      },
      user_question: {
        type: Type.STRING,
        description: 'The original question the user asked during the conversation.',
      },
      ai_response: {
        type: Type.STRING,
        description: 'The detailed, comprehensive AI response to be included in the email body. The spoken response should be brief, and this email content should be detailed.',
      },
      subject: {
        type: Type.STRING,
        description: 'The subject line for the email. If not provided, a default will be used.',
      },
      sources: {
        type: Type.ARRAY,
        description: 'An array of source objects, each with a title and URL, for any information provided. This is required if external information was used.',
        items: {
          type: Type.OBJECT,
          properties: {
            title: { type: Type.STRING, description: 'The title of the source.' },
            url: { type: Type.STRING, description: 'The URL of the source.' },
          },
          required: ['title', 'url'],
        },
      },
      conversation_context: {
          type: Type.STRING,
          description: 'Optional additional context from the conversation to include in the email for clarity.'
      }
    },
    required: ['recipient_email', 'user_question', 'ai_response'],
  },
};

// New handler for the conversation summary tool
export async function handleEmailConversation(parameters: {
  recipient_email: string;
  user_question: string;
  ai_response: string;
  subject?: string;
  sources?: { title: string; url:string }[];
  conversation_context?: string;
}) {
  try {
    // Basic validation
    if (!parameters.recipient_email || !parameters.user_question || !parameters.ai_response) {
      const errorMsg = 'Missing required parameters for sending conversation email.';
      console.error(`❌ ${errorMsg}`);
      return { success: false, error: errorMsg };
    }

    console.log('📧 Calling /api/send-conversation with parameters:', parameters);

    const baseUrl = typeof window !== 'undefined' ? window.location.origin : '';
    const apiUrl = `${baseUrl}/api/send-conversation`;

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        ...parameters,
        timestamp: new Date().toISOString(),
        include_context: !!parameters.conversation_context,
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      console.error('❌ API call to send-conversation failed:', { status: response.status, result });
      return { success: false, error: result.error || `API Error: ${response.statusText}` };
    }

    console.log('✅ Conversation email sent successfully via API:', result);
    return { success: true, message: 'Conversation summary sent successfully!', ...result };

  } catch (error) {
    console.error('❌ Unhandled error in handleEmailConversation:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'An unknown error occurred.' 
    };
  }
}

// Type definitions for TypeScript
export interface SendEmailParams {
  recipient_email: string
  subject: string
  text_content: string
  html_content?: string
  sender_name?: string
  log_to_webhook?: boolean
  webhook_url?: string
}

export interface SendEmailResult {
  success: boolean
  message?: string
  error?: string
  details?: string
  messageId?: string
  recipient?: string
  subject?: string
  timestamp?: string
}

export interface CheckSmtpResult {
  success: boolean
  message?: string
  error?: string
  details?: string
  config?: {
    host: string
    port: number
    user: string
    secure: boolean
  }
}
