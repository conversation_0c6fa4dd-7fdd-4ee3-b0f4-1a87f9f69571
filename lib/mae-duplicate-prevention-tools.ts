/**
 * Mae Duplicate Prevention Tools
 * 
 * Function tools that allow <PERSON> to check for and handle duplicate family members
 * during the onboarding process.
 */

import { Type, FunctionDeclaration } from '@google/genai'

export const maeDuplicatePreventionFunctionDeclarations: FunctionDeclaration[] = [
  {
    name: 'check_family_member_duplicate',
    description: 'Check if a family member with the same or similar name already exists before adding them. IMPORTANT: You must call get_user_context first to get the proper user_id (UUID) before using this function. Use this BEFORE adding any family member during onboarding.',
    parameters: {
      type: Type.OBJECT,
      properties: {
        user_id: {
          type: Type.STRING,
          description: 'The user UUID from get_user_context response (must be a valid UUID, not email or Clerk ID)'
        },
        name: {
          type: Type.STRING,
          description: 'The name of the family member to check for duplicates'
        },
        date_of_birth: {
          type: Type.STRING,
          description: 'Date of birth in YYYY-MM-DD format (optional, helps with duplicate analysis)'
        },
        relationship: {
          type: Type.STRING,
          description: 'Relationship to user (child, spouse, etc.)'
        }
      },
      required: ['user_id', 'name']
    }
  },
  {
    name: 'resolve_family_member_duplicate',
    description: 'Handle a detected duplicate by either updating existing record or confirming user wants to add anyway. Use this when check_family_member_duplicate finds duplicates.',
    parameters: {
      type: Type.OBJECT,
      properties: {
        action: {
          type: Type.STRING,
          enum: ['update_existing', 'add_anyway', 'cancel'],
          description: 'Action to take: update_existing (modify the existing record), add_anyway (create new despite duplicate), cancel (don\'t add)'
        },
        existing_member_id: {
          type: Type.STRING,
          description: 'ID of the existing family member to update (required if action is update_existing)'
        },
        updated_data: {
          type: Type.OBJECT,
          description: 'New data for updating existing member (required if action is update_existing)',
          properties: {
            name: { type: Type.STRING },
            date_of_birth: { type: Type.STRING },
            gender: { type: Type.STRING },
            relationship: { type: Type.STRING },
            medical_conditions: { type: Type.ARRAY, items: { type: Type.STRING } },
            allergies: { type: Type.ARRAY, items: { type: Type.STRING } },
            medications: { type: Type.ARRAY, items: { type: Type.STRING } }
          }
        },
        reason: {
          type: Type.STRING,
          description: 'Explanation for the action taken'
        }
      },
      required: ['action', 'reason']
    }
  }
]

export async function handleCheckFamilyMemberDuplicate(args: {
  user_id: string
  name: string
  date_of_birth?: string
  relationship?: string
}) {
  try {
    console.log('🔍 Mae checking for family member duplicates:', args)

    // Validate that user_id is a proper UUID
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i

    if (!uuidRegex.test(args.user_id)) {
      console.log('⚠️ Invalid user_id format:', args.user_id)

      // If it looks like an email, provide specific guidance
      if (args.user_id.includes('@')) {
        return {
          success: false,
          message: 'Mae needs to get your user context first. Please ask Mae to get your user context before adding family members.',
          error: 'invalid_user_id_email',
          action_required: 'get_user_context',
          recommendation: 'Mae should call get_user_context first to get the proper user UUID, then use that UUID for family member operations.'
        }
      }

      return {
        success: false,
        message: 'Invalid user ID format. Mae needs to get your user context first.',
        error: 'invalid_user_id_format',
        action_required: 'get_user_context',
        recommendation: 'Mae should call get_user_context first to get the proper user UUID.'
      }
    }

    const response = await fetch('/api/family-members/check-duplicate', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        userId: args.user_id,
        name: args.name,
        dateOfBirth: args.date_of_birth,
        relationship: args.relationship
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ Duplicate check API error:', response.status, errorText)

      // Handle specific case where user is not found in database
      if (response.status === 404) {
        try {
          const errorData = JSON.parse(errorText)
          if (errorData.action_required === 'sync_user') {
            return {
              success: false,
              message: 'User not found in database. Please ask the user to click the "Sync Profile" button to complete their account setup before adding family members.',
              error: 'user_not_synced',
              action_required: 'sync_user',
              recommendation: 'Guide the user to sync their profile first, then try adding family members again.'
            }
          }
        } catch (parseError) {
          // Fall through to generic error handling
        }
      }

      return {
        success: false,
        message: `Error checking for duplicates: ${response.status}`,
        error: errorText
      }
    }

    const result = await response.json()

    if (!result.success) {
      return {
        success: false,
        message: 'Failed to check for duplicates',
        error: result.error
      }
    }

    if (!result.isDuplicate) {
      return {
        success: true,
        isDuplicate: false,
        message: `No duplicates found for "${args.name}". Safe to add this family member.`,
        canProceed: true
      }
    }

    // Format duplicate information for Mae
    const duplicateDetails = result.matches.map((match: any) => 
      `- ${match.name} (DOB: ${match.dateOfBirth}, Relationship: ${match.relationship}, Added: ${new Date(match.createdAt).toLocaleDateString()})`
    ).join('\n')

    return {
      success: true,
      isDuplicate: true,
      duplicateCount: result.matches.length,
      message: `⚠️  DUPLICATE DETECTED! Found ${result.matches.length} existing family member(s) with similar name "${args.name}":\n\n${duplicateDetails}\n\nPlease ask the user what they would like to do:`,
      matches: result.matches,
      canProceed: false,
      recommendations: [
        'Update the existing record if it\'s the same person with incorrect information',
        'Add anyway if this is actually a different person (like twins or family members with similar names)',
        'Cancel if they don\'t want to add this person'
      ]
    }

  } catch (error) {
    console.error('❌ Error in handleCheckFamilyMemberDuplicate:', error)
    return {
      success: false,
      message: 'Error checking for duplicates',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export async function handleResolveFamilyMemberDuplicate(args: {
  action: 'update_existing' | 'add_anyway' | 'cancel'
  existing_member_id?: string
  updated_data?: any
  reason: string
}) {
  try {
    console.log('🔧 Mae resolving family member duplicate:', args)

    switch (args.action) {
      case 'update_existing':
        if (!args.existing_member_id || !args.updated_data) {
          return {
            success: false,
            message: 'Missing required data for updating existing family member',
            error: 'existing_member_id and updated_data are required for update_existing action'
          }
        }

        // TODO: Implement update existing family member API call
        return {
          success: true,
          action: 'updated',
          message: `Updated existing family member record. ${args.reason}`,
          recommendation: 'Continue with onboarding process.'
        }

      case 'add_anyway':
        return {
          success: true,
          action: 'proceed',
          message: `Proceeding to add family member despite duplicate detection. ${args.reason}`,
          recommendation: 'Continue with adding the new family member as requested.'
        }

      case 'cancel':
        return {
          success: true,
          action: 'cancelled',
          message: `Cancelled adding family member. ${args.reason}`,
          recommendation: 'Move on to next step in onboarding or ask for different family member information.'
        }

      default:
        return {
          success: false,
          message: 'Invalid action specified',
          error: 'Action must be update_existing, add_anyway, or cancel'
        }
    }

  } catch (error) {
    console.error('❌ Error in handleResolveFamilyMemberDuplicate:', error)
    return {
      success: false,
      message: 'Error resolving duplicate',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}