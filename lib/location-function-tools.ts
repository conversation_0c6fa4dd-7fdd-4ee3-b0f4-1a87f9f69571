// Location-based function tools for <PERSON> to help parents find healthcare facilities
import { Type, FunctionDeclaration } from '@google/genai';

// Tool for finding nearby pediatricians
export const findPediatriciansToolDeclaration: FunctionDeclaration = {
  name: 'find_pediatricians',
  description: 'Find nearby pediatricians based on user location, insurance, and preferences.',
  parameters: {
    type: Type.OBJECT,
    properties: {
      location: {
        type: Type.STRING,
        description: 'User location (address, city, or zip code)'
      },
      radius: {
        type: Type.NUMBER,
        description: 'Search radius in miles (default: 10)',
        default: 10
      },
      insurance: {
        type: Type.STRING,
        description: 'Insurance provider name (optional)'
      },
      specialization: {
        type: Type.STRING,
        description: 'Pediatric specialization if needed (e.g., cardiology, neurology)'
      },
      rating_threshold: {
        type: Type.NUMBER,
        description: 'Minimum rating threshold (1-5 stars)',
        default: 3.5
      }
    },
    required: ['location']
  }
};

// Tool for finding nearby hospitals with pediatric services
export const findHospitalsToolDeclaration: FunctionDeclaration = {
  name: 'find_hospitals',
  description: 'Find nearby hospitals with pediatric departments and emergency services.',
  parameters: {
    type: Type.OBJECT,
    properties: {
      location: {
        type: Type.STRING,
        description: 'User location (address, city, or zip code)'
      },
      radius: {
        type: Type.NUMBER,
        description: 'Search radius in miles (default: 25)',
        default: 25
      },
      emergency_only: {
        type: Type.BOOLEAN,
        description: 'Filter for hospitals with 24/7 emergency services',
        default: false
      },
      pediatric_level: {
        type: Type.STRING,
        description: 'Required pediatric care level (basic, intermediate, advanced)',
        default: 'basic'
      },
      insurance: {
        type: Type.STRING,
        description: 'Insurance provider name (optional)'
      }
    },
    required: ['location']
  }
};

// Tool for finding urgent care centers
export const findUrgentCareToolDeclaration: FunctionDeclaration = {
  name: 'find_urgent_care',
  description: 'Find nearby urgent care centers that accept pediatric patients.',
  parameters: {
    type: Type.OBJECT,
    properties: {
      location: {
        type: Type.STRING,
        description: 'User location (address, city, or zip code)'
      },
      radius: {
        type: Type.NUMBER,
        description: 'Search radius in miles (default: 15)',
        default: 15
      },
      accepts_children: {
        type: Type.BOOLEAN,
        description: 'Filter for centers that specifically accept pediatric patients',
        default: true
      },
      current_hours: {
        type: Type.BOOLEAN,
        description: 'Filter for centers currently open',
        default: false
      },
      insurance: {
        type: Type.STRING,
        description: 'Insurance provider name (optional)'
      }
    },
    required: ['location']
  }
};

// Tool for finding pharmacies with pediatric medications
export const findPharmaciesToolDeclaration: FunctionDeclaration = {
  name: 'find_pharmacies',
  description: 'Find nearby pharmacies that stock pediatric medications and offer services for children.',
  parameters: {
    type: Type.OBJECT,
    properties: {
      location: {
        type: Type.STRING,
        description: 'User location (address, city, or zip code)'
      },
      radius: {
        type: Type.NUMBER,
        description: 'Search radius in miles (default: 10)',
        default: 10
      },
      medication_type: {
        type: Type.STRING,
        description: 'Specific medication or type needed (optional)'
      },
      services: {
        type: Type.ARRAY,
        description: 'Required services (immunizations, compounding, delivery)',
        items: {
          type: Type.STRING
        }
      },
      insurance: {
        type: Type.STRING,
        description: 'Insurance/prescription plan name (optional)'
      },
      open_now: {
        type: Type.BOOLEAN,
        description: 'Filter for pharmacies currently open',
        default: false
      }
    },
    required: ['location']
  }
};

// Interface definitions for the function responses
export interface HealthcareProvider {
  name: string;
  address: string;
  phone: string;
  website?: string;
  rating: number;
  reviews_count: number;
  distance: number;
  coordinates: {
    lat: number;
    lng: number;
  };
  insurance_accepted?: string[];
  specializations?: string[];
  hours?: {
    [day: string]: string;
  };
  emergency_services?: boolean;
  pediatric_services?: string[];
}

export interface LocationSearchResult {
  success: boolean;
  providers: HealthcareProvider[];
  search_location: string;
  total_found: number;
  error?: string;
}

// Dynamic geocoding function using Google's Geocoding API
async function getLocationCoordinates(location: string): Promise<{ lat: number; lng: number }> {
  try {
    const apiKey = process.env.GOOGLE_MAPS_API_KEY;
    if (!apiKey) {
      console.warn('Google Maps API key not found, using fallback coordinates');
      return { lat: 39.8283, lng: -98.5795 }; // US center fallback
    }

    const encodedLocation = encodeURIComponent(location);
    const geocodeUrl = `https://maps.googleapis.com/maps/api/geocode/json?address=${encodedLocation}&key=${apiKey}`;
    
    const response = await fetch(geocodeUrl);
    const data = await response.json();
    
    if (data.status === 'OK' && data.results && data.results.length > 0) {
      const result = data.results[0];
      const coordinates = {
        lat: result.geometry.location.lat,
        lng: result.geometry.location.lng
      };
      
      console.log(`🌍 Geocoded "${location}" to:`, coordinates);
      return coordinates;
    } else {
      console.warn(`Geocoding failed for "${location}":`, data.status);
      return { lat: 39.8283, lng: -98.5795 }; // US center fallback
    }
  } catch (error) {
    console.error('Geocoding error:', error);
    return { lat: 39.8283, lng: -98.5795 }; // US center fallback
  }
}

// Function to search for real providers using our internal API endpoint
async function searchRealProviders(query: string, location: string, radius: number): Promise<HealthcareProvider[]> {
  try {
    console.log(`🔍 Searching for real providers: "${query}" near "${location}" within ${radius} miles`);
    
    const response = await fetch('/api/search-providers', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query,
        location,
        radius
      })
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ API request failed: ${response.status} ${response.statusText}`, errorText);
      throw new Error(`API request failed: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    
    if (data.error) {
      console.error(`❌ API returned error:`, data.error);
      throw new Error(data.error);
    }

    console.log(`✅ Found ${data.providers.length} real providers`);
    return data.providers;
  } catch (error) {
    console.error('❌ Error searching for real providers:', error);
    
    // Log detailed error information for debugging
    if (error instanceof TypeError && error.message.includes('fetch')) {
      console.error('❌ Network error: Failed to connect to API endpoint');
    } else if (error instanceof Error) {
      console.error('❌ API Error details:', error.message);
    }
    
    // As a fallback, provide helpful guidance instead of empty results
    console.warn('⚠️ Returning guidance message instead of empty results');
    return [{
      name: '🔧 Google Maps Setup Required',
      address: 'Please enable Geocoding API and Places API in Google Cloud Console',
      phone: 'Setup Instructions Available',
      rating: 0,
      distance: 0,
      coordinates: { lat: 0, lng: 0 },
      website: 'https://console.cloud.google.com/apis/library',
      reviews_count: 0,
      hours: {
        'Setup Steps': '1. Enable Geocoding API, 2. Enable Places API, 3. Check API key restrictions, 4. Verify billing is enabled'
      }
    }];
  }
}

// Mock providers for when API fails - THIS SHOULD NEVER BE USED IN PRODUCTION
function generateMockProviders(location: string, query: string, coordinates: { lat: number; lng: number }): HealthcareProvider[] {
  console.warn('⚠️  WARNING: generateMockProviders should never be used in production!');
  return [
    {
      name: `${query.split(' ')[0].charAt(0).toUpperCase() + query.split(' ')[0].slice(1)} Medical Center`,
      address: `123 Healthcare Way, ${location}`,
      phone: '(*************',
      rating: 4.2,
      reviews_count: 89,
      distance: 2.1,
      coordinates: {
        lat: coordinates.lat + (Math.random() - 0.5) * 0.02,
        lng: coordinates.lng + (Math.random() - 0.5) * 0.02
      },
      specializations: ['Pediatrics', 'Family Medicine']
    },
    {
      name: `Family Health Services`,
      address: `456 Medical Dr, ${location}`,
      phone: '(*************',
      rating: 4.6,
      reviews_count: 156,
      distance: 3.4,
      coordinates: {
        lat: coordinates.lat + (Math.random() - 0.5) * 0.03,
        lng: coordinates.lng + (Math.random() - 0.5) * 0.03
      },
      specializations: ['Pediatrics', 'General Practice']
    },
    {
      name: `Children's Clinic of ${location}`,
      address: `789 Kids Blvd, ${location}`,
      phone: '(*************',
      rating: 4.8,
      reviews_count: 203,
      distance: 1.7,
      coordinates: {
        lat: coordinates.lat + (Math.random() - 0.5) * 0.025,
        lng: coordinates.lng + (Math.random() - 0.5) * 0.025
      },
      specializations: ['Pediatrics', 'Adolescent Medicine']
    }
  ];
}

// Function handlers for each location tool
export async function handleFindPediatricians(parameters: {
  location: string;
  radius?: number;
  insurance?: string;
  specialization?: string;
  rating_threshold?: number;
}): Promise<LocationSearchResult> {
  try {
    const query = parameters.specialization 
      ? `pediatric ${parameters.specialization}` 
      : 'pediatrician children doctor';
    
    const providers = await searchRealProviders(query, parameters.location, parameters.radius || 10);
    
    const filteredProviders = providers.filter(
      provider => provider.rating >= (parameters.rating_threshold || 3.5)
    );

    return {
      success: true,
      providers: filteredProviders,
      search_location: parameters.location,
      total_found: filteredProviders.length
    };
  } catch (error) {
    console.error('Error in handleFindPediatricians:', error);
    return {
      success: false,
      providers: [],
      search_location: parameters.location,
      total_found: 0,
      error: error instanceof Error ? error.message : 'Unable to search for pediatricians at this time'
    };
  }
}

export async function handleFindHospitals(parameters: {
  location: string;
  radius?: number;
  emergency_only?: boolean;
  pediatric_level?: string;
  insurance?: string;
}): Promise<LocationSearchResult> {
  try {
    const query = parameters.emergency_only 
      ? 'emergency room hospital pediatric' 
      : 'hospital pediatric children medical center';
    
    const providers = await searchRealProviders(query, parameters.location, parameters.radius || 25);

    return {
      success: true,
      providers,
      search_location: parameters.location,
      total_found: providers.length
    };
  } catch (error) {
    console.error('Error in handleFindHospitals:', error);
    return {
      success: false,
      providers: [],
      search_location: parameters.location,
      total_found: 0,
      error: error instanceof Error ? error.message : 'Unable to search for hospitals at this time'
    };
  }
}

export async function handleFindUrgentCare(parameters: {
  location: string;
  radius?: number;
  accepts_children?: boolean;
  current_hours?: boolean;
  insurance?: string;
}): Promise<LocationSearchResult> {
  try {
    const query = parameters.accepts_children 
      ? 'urgent care pediatric children family' 
      : 'urgent care walk in clinic';
    
    const providers = await searchRealProviders(query, parameters.location, parameters.radius || 15);

    return {
      success: true,
      providers,
      search_location: parameters.location,
      total_found: providers.length
    };
  } catch (error) {
    console.error('Error in handleFindUrgentCare:', error);
    return {
      success: false,
      providers: [],
      search_location: parameters.location,
      total_found: 0,
      error: error instanceof Error ? error.message : 'Unable to search for urgent care at this time'
    };
  }
}

export async function handleFindPharmacies(parameters: {
  location: string;
  radius?: number;
  medication_type?: string;
  services?: string[];
  insurance?: string;
  open_now?: boolean;
}): Promise<LocationSearchResult> {
  try {
    let query = 'pharmacy';
    if (parameters.medication_type) {
      query += ` ${parameters.medication_type}`;
    }
    if (parameters.services?.includes('compounding')) {
      query += ' compounding';
    }
    
    const providers = await searchRealProviders(query, parameters.location, parameters.radius || 10);

    return {
      success: true,
      providers,
      search_location: parameters.location,
      total_found: providers.length
    };
  } catch (error) {
    console.error('Error in handleFindPharmacies:', error);
    return {
      success: false,
      providers: [],
      search_location: parameters.location,
      total_found: 0,
      error: error instanceof Error ? error.message : 'Unable to search for pharmacies at this time'
    };
  }
}