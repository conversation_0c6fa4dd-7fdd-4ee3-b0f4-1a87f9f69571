/**
 * Email Templates for Our Kidz Platform
 * Reusable email templates with consistent branding
 */

export interface EmailTemplateData {
  recipientName?: string
  content: string
  additionalContent?: string
  ctaText?: string
  ctaUrl?: string
  footerText?: string
  sources?: { title: string; url: string }[]
}

/**
 * Base HTML template with Our Kidz branding
 */
export function createBaseEmailTemplate(data: EmailTemplateData): string {
  const {
    recipientName = '',
    content,
    additionalContent = '',
    ctaText = '',
    ctaUrl = '',
    footerText = 'Your journey to health freedom starts here!',
    sources = []
  } = data;

  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Our Kidz</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
        }
        .container {
            background-color: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            border-bottom: 2px solid #00bba7;
            padding-bottom: 20px;
            margin-bottom: 30px;
            position: relative;
        }
        .logo {
            color: #00bba7;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .tagline {
            color: #666;
            font-size: 14px;
            margin-bottom: 15px;
        }
        .header-actions {
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            margin-top: 10px;
        }
        .new-button {
            background-color: #3b82f6;
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            text-decoration: none;
        }
        .invite-link {
            background-color: #14b8a6;
            color: white;
            padding: 6px 12px;
            border-radius: 6px;
            font-size: 12px;
            font-weight: 500;
            text-decoration: none;
        }
        .logo-image {
            width: 48px;
            height: 48px;
            margin: 0 auto 10px auto;
            display: block;
        }
        .greeting {
            font-size: 18px;
            color: #333;
            margin-bottom: 20px;
        }
        .content-section {
            margin-bottom: 25px;
            padding: 20px;
            border-radius: 8px;
            background-color: #f0fdf4;
            border-left: 4px solid #00bba7;
        }
        .additional-content {
            margin-bottom: 25px;
            padding: 20px;
            border-radius: 8px;
            background-color: #f0f9ff;
            border-left: 4px solid #0ea5e9;
        }
        .cta-button {
            display: inline-block;
            background-color: #00bba7;
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin: 20px 0;
        }
        .cta-button:hover {
            background-color: #009688;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e5e7eb;
            color: #666;
            font-size: 14px;
        }
        .footer a {
            color: #00bba7;
            text-decoration: none;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        .sources-section {
            margin-bottom: 25px;
            padding: 20px;
            border-radius: 8px;
            background-color: #ecfdf5;
            border-left: 4px solid #14b8a6;
        }
        .sources-section h3 {
            color: #14b8a6;
            margin-bottom: 15px;
        }
        .sources-section ul {
            margin: 0;
            padding-left: 20px;
        }
        .sources-section li {
            margin-bottom: 8px;
        }
        .sources-section a {
            color: #14b8a6;
            text-decoration: none;
        }
        .sources-section a:hover {
            text-decoration: underline;
        }
        .founders-huddle {
            background-color: #f0fdf4;
            border: 1px solid #14b8a6;
            border-radius: 8px;
            padding: 20px;
            margin: 25px 0;
        }
        .founders-huddle h3 {
            color: #1f2937;
            margin: 0 0 10px 0;
            font-size: 18px;
        }
        .founders-huddle p {
            color: #666;
            margin: 0 0 15px 0;
            line-height: 1.5;
        }
        .book-button {
            background-color: #14b8a6;
            color: white;
            padding: 10px 20px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            display: inline-block;
        }
        .calendar-section {
            background-color: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 15px;
            margin: 20px 0;
        }
        .calendar-section p {
            margin: 0 0 8px 0;
            font-weight: 500;
        }
        .calendar-section a {
            color: #3b82f6;
            text-decoration: none;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="cid:ourkidz-logo" alt="Our Kidz Logo" class="logo-image" />
            <div class="logo">Our Kidz</div>
            <div class="tagline">The Smart, Agentic Companion Built for Today's Modern Families</div>
        </div>
        
        ${recipientName ? `<div class="greeting">Hello ${recipientName}!</div>` : ''}
        
        <div class="content-section">
            ${content}
        </div>
        
        ${additionalContent ? `
        <div class="additional-content">
            ${additionalContent}
        </div>
        ` : ''}
        
        ${ctaText && ctaUrl ? `
        <div style="text-align: center;">
            <a href="${ctaUrl}" class="cta-button">${ctaText}</a>
        </div>
        ` : ''}

        ${sources && sources.length > 0 ? `
        <div class="sources-section">
            <h3>📚 Sources & References</h3>
            <p>This information is based on the following trusted sources:</p>
            <ul>
                ${sources.map(source => `<li><a href="${source.url}" target="_blank" rel="noopener noreferrer">${source.title || source.url}</a></li>`).join('')}
            </ul>
            <p><em>Mae always researches current information to provide you with accurate, up-to-date guidance.</em></p>
        </div>
        ` : ''}

        <div class="founders-huddle">
            <h3>Founders 15-Minute Huddle</h3>
            <p>Secure a personal, 15-minute call with Our Kidz Founder. Share your chance to share feedback and influence what we build next.</p>
            <a href="https://cal.read.ai/our-kids/15-min" class="book-button" target="_blank" rel="noopener noreferrer">Book a time</a>
        </div>

        <div class="calendar-section">
            <p><strong>Calendar Link:</strong></p>
            <a href="https://cal.read.ai/our-kids/15-min" target="_blank" rel="noopener noreferrer">https://cal.read.ai/our-kids/15-min</a>
        </div>

        <div class="footer">
            <p>${footerText}</p>
            <p>Visit us at <a href="https://app.our-kidz.com">our-kidz.com</a></p>
            <p>Contact us: <a href="mailto:<EMAIL>"><EMAIL></a></p>
        </div>
    </div>
</body>
</html>`.trim();
}

/**
 * Welcome Email Template
 */
export function createWelcomeEmail(
  recipientName: string = '',
  sources: { title: string; url: string }[] = []
): { subject: string; text: string; html: string } {
  const subject = 'Welcome to Our Kidz Community! 🌟';
  
  const text = `
Welcome to Our Kidz!

Hello ${recipientName ? recipientName : 'there'}!

We're thrilled to have you join the Our Kidz community! Our platform is designed to support parents like you with AI-powered guidance and resources for your parenting journey.

What you can expect:
• Personalized parenting advice from Mae, our AI assistant
• Evidence-based information on child development
• A supportive community of parents
• Resources for health, nutrition, and wellness

Getting Started:
Visit our platform at app.our-kidz.com to start exploring all the features we have to offer.

If you have any questions, don't hesitate to reach out to <NAME_EMAIL>.

Welcome aboard!

${sources.length > 0 ? `
Sources & References:
${sources.map(source => `• ${source.title}: ${source.url}`).join('\n')}

This information is based on current research to provide you with accurate guidance.
` : ''}

The Our Kidz Team
Your journey to health freedom starts here!
  `.trim();

  const html = createBaseEmailTemplate({
    recipientName,
    content: `
      <h2>Welcome to Our Kidz Community! 🌟</h2>
      <p>We're thrilled to have you join the Our Kidz community! Our platform is designed to support parents like you with AI-powered guidance and resources for your parenting journey.</p>
      
      <h3>What you can expect:</h3>
      <ul>
        <li><strong>Personalized parenting advice</strong> from Mae, our AI assistant</li>
        <li><strong>Evidence-based information</strong> on child development</li>
        <li><strong>A supportive community</strong> of parents</li>
        <li><strong>Resources for health, nutrition, and wellness</strong></li>
      </ul>
    `,
    additionalContent: `
      <h3>Getting Started:</h3>
      <p>Visit our platform to start exploring all the features we have to offer. Mae is ready to help answer your parenting questions!</p>
    `,
    ctaText: 'Explore Our Platform',
    ctaUrl: 'https://app.our-kidz.com',
    sources: sources
  });

  return { subject, text, html };
}

/**
 * Contact Form Confirmation Email Template
 */
export function createContactConfirmationEmail(
  recipientName: string = '',
  userMessage: string = '',
  sources: { title: string; url: string }[] = []
): { subject: string; text: string; html: string } {
  const subject = 'Thank you for contacting Our Kidz!';
  
  const text = `
Thank you for contacting Our Kidz!

Hello ${recipientName ? recipientName : 'there'}!

Thank you for reaching out to Our Kidz! We have received your message and will get back to you within 24 hours.

Your message:
"${userMessage}"

Our team is excited to help you on your parenting journey. In the meantime, feel free to explore our platform and chat with Mae, our AI assistant.

If you have any urgent questions, please call us at (555) 123-KIDZ.

${sources.length > 0 ? `
Sources & References:
${sources.map(source => `• ${source.title}: ${source.url}`).join('\n')}

Our responses are based on current research and trusted sources.
` : ''}

Best regards,
The Our Kidz Team
Your journey to health freedom starts here!
  `.trim();

  const html = createBaseEmailTemplate({
    recipientName,
    content: `
      <h2>Thank you for contacting Our Kidz!</h2>
      <p>Thank you for reaching out to Our Kidz! We have received your message and will get back to you within 24 hours.</p>
    `,
    additionalContent: userMessage ? `
      <h3>Your message:</h3>
      <div class="highlight">
        "${userMessage}"
      </div>
      <p>Our team is excited to help you on your parenting journey. In the meantime, feel free to explore our platform and chat with Mae, our AI assistant.</p>
    ` : `
      <p>Our team is excited to help you on your parenting journey. In the meantime, feel free to explore our platform and chat with Mae, our AI assistant.</p>
    `,
    ctaText: 'Chat with Mae',
    ctaUrl: 'https://app.our-kidz.com',
    footerText: 'If you have any urgent questions, please call us at 1-800-OUR-KIDZ.',
    sources: sources
  });

  return { subject, text, html };
}

/**
 * Admin Notification Email Template
 */
export function createAdminNotificationEmail(contactData: {
  name: string;
  email: string;
  phone?: string;
  message: string;
}): { subject: string; text: string; html: string } {
  const subject = `New Contact Form Submission - ${contactData.name}`;
  
  const text = `
New Contact Form Submission

Contact Details:
Name: ${contactData.name}
Email: ${contactData.email}
Phone: ${contactData.phone || 'Not provided'}

Message:
${contactData.message}

Please respond to this inquiry within 24 hours.

This notification was automatically generated from the Our Kidz contact form.
  `.trim();

  const html = createBaseEmailTemplate({
    content: `
      <h2>New Contact Form Submission</h2>
      <p>A new contact form has been submitted on the Our Kidz platform.</p>
      
      <table style="width: 100%; border-collapse: collapse; margin: 20px 0;">
        <tr>
          <td style="padding: 10px; border: 1px solid #ddd; background-color: #f9f9f9; font-weight: bold;">Name:</td>
          <td style="padding: 10px; border: 1px solid #ddd;">${contactData.name}</td>
        </tr>
        <tr>
          <td style="padding: 10px; border: 1px solid #ddd; background-color: #f9f9f9; font-weight: bold;">Email:</td>
          <td style="padding: 10px; border: 1px solid #ddd;">${contactData.email}</td>
        </tr>
        <tr>
          <td style="padding: 10px; border: 1px solid #ddd; background-color: #f9f9f9; font-weight: bold;">Phone:</td>
          <td style="padding: 10px; border: 1px solid #ddd;">${contactData.phone || 'Not provided'}</td>
        </tr>
      </table>
    `,
    additionalContent: `
      <h3>Message:</h3>
      <div class="highlight">
        ${contactData.message}
      </div>
      <p><strong>Action Required:</strong> Please respond to this inquiry within 24 hours.</p>
    `,
    ctaText: 'Reply to Contact',
    ctaUrl: `mailto:${contactData.email}?subject=Re: Your inquiry to Our Kidz`,
    footerText: 'This notification was automatically generated from the Our Kidz contact form.'
  });

  return { subject, text, html };
}

/**
 * Invite Email Template
 */
export function createInviteEmail(
  recipientEmail: string,
  personalMessage: string = '',
  sources: { title: string; url: string }[] = []
): { subject: string; text: string; html: string } {
  const subject = "You're Invited to Join Our Kidz - The Smart Parenting Platform! 🎉";

  const text = `
You're Invited to Our Kidz! 🎉

A friend thought you'd love Our Kidz - an AI-powered platform that helps parents navigate everything from diaper changes to teen drama with confidence.

${personalMessage ? `Personal Message: "${personalMessage}"\n` : ''}

🎁 Special Welcome Offer:
• 1 Free Month when you sign up through this invitation
• 24/7 AI Pediatric Assistant - Meet Mae, your personal parenting expert
• Evidence-Based Advice - Get current, research-backed guidance
• Community Support - Connect with other parents on similar journeys
• Expert Resources - Access to pediatric specialists and founders

Join Our Kidz: https://app.our-kidz.com?ref=invite&email=${encodeURIComponent(recipientEmail)}

Why Our Kidz?
Whether you're dealing with sleepless nights, picky eaters, or teenage challenges, Our Kidz provides instant, reliable support when you need it most. Our AI assistant Mae searches current medical research to give you evidence-based answers, not just generic advice.

${sources.length > 0 ? `
Sources & References:
${sources.map(source => `• ${source.title}: ${source.url}`).join('\n')}

This information is based on current research to provide you with accurate guidance.
` : ''}

This invitation was sent to ${recipientEmail}
Visit us at https://app.our-kidz.com
Your journey to confident parenting starts here!
  `.trim();

  const html = createBaseEmailTemplate({
    content: `
      <h2>🎉 Join the Future of Parenting</h2>
      <p>A friend thought you'd love Our Kidz - an AI-powered platform that helps parents navigate everything from diaper changes to teen drama with confidence.</p>

      ${personalMessage ? `
      <div class="highlight">
        <h4>Personal Message:</h4>
        <p><em>"${personalMessage}"</em></p>
      </div>
      ` : ''}

      <h3>🎁 Special Welcome Offer</h3>
      <ul>
        <li><strong>1 Free Month</strong> when you sign up through this invitation</li>
        <li><strong>24/7 AI Pediatric Assistant</strong> - Meet Mae, your personal parenting expert</li>
        <li><strong>Evidence-Based Advice</strong> - Get current, research-backed guidance</li>
        <li><strong>Community Support</strong> - Connect with other parents on similar journeys</li>
        <li><strong>Expert Resources</strong> - Access to pediatric specialists and founders</li>
      </ul>
    `,
    additionalContent: `
      <h3>Why Our Kidz?</h3>
      <p>Whether you're dealing with sleepless nights, picky eaters, or teenage challenges, Our Kidz provides instant, reliable support when you need it most. Our AI assistant Mae searches current medical research to give you evidence-based answers, not just generic advice.</p>
    `,
    ctaText: 'Join Our Kidz - Get 1 Free Month',
    ctaUrl: `https://app.our-kidz.com?ref=invite&email=${encodeURIComponent(recipientEmail)}`,
    footerText: `This invitation was sent to ${recipientEmail}. Your journey to confident parenting starts here!`,
    sources: sources
  });

  return { subject, text, html };
}

/**
 * Newsletter Welcome Email Template
 */
export function createNewsletterWelcomeEmail(
  recipientEmail: string
): { subject: string; text: string; html: string } {
  const subject = 'Welcome to Our Kidz Newsletter! 📧';

  const text = `
Welcome to Our Kidz Newsletter!

Hello there!

Thank you for subscribing to the Our Kidz newsletter! We're excited to have you join our community of parents who are passionate about providing the best care for their children.

What to expect from our newsletter:
• Latest insights on pediatric health and development
• Tips and advice from Mae, our AI parenting assistant
• Updates on new features and platform improvements
• Evidence-based parenting resources and research
• Community highlights and success stories

You'll receive our newsletter weekly, packed with valuable information to support your parenting journey. Each edition is carefully curated to provide you with actionable insights and trusted guidance.

Getting Started:
Visit our platform at app.our-kidz.com to explore all the features we have to offer. Chat with Mae, our AI assistant, for personalized parenting advice anytime!

If you have any questions or feedback, feel free to reach out to <NAME_EMAIL>.

Welcome to the Our Kidz family!

The Our Kidz Team
Your journey to health freedom starts here!
  `.trim();

  const html = createBaseEmailTemplate({
    content: `
      <h2>Welcome to Our Kidz Newsletter! 📧</h2>
      <p>Thank you for subscribing to the Our Kidz newsletter! We're excited to have you join our community of parents who are passionate about providing the best care for their children.</p>

      <h3>What to expect from our newsletter:</h3>
      <ul>
        <li><strong>Latest insights</strong> on pediatric health and development</li>
        <li><strong>Tips and advice</strong> from Mae, our AI parenting assistant</li>
        <li><strong>Updates</strong> on new features and platform improvements</li>
        <li><strong>Evidence-based parenting resources</strong> and research</li>
        <li><strong>Community highlights</strong> and success stories</li>
      </ul>

      <p>You'll receive our newsletter weekly, packed with valuable information to support your parenting journey. Each edition is carefully curated to provide you with actionable insights and trusted guidance.</p>
    `,
    additionalContent: `
      <h3>Getting Started:</h3>
      <p>Visit our platform to explore all the features we have to offer. Chat with Mae, our AI assistant, for personalized parenting advice anytime!</p>
    `,
    ctaText: 'Explore Our Platform',
    ctaUrl: 'https://app.our-kidz.com',
    footerText: 'Welcome to the Our Kidz family!'
  });

  return { subject, text, html };
}

/**
 * Newsletter Email Template
 */
export function createNewsletterEmail(
  recipientName: string = '',
  highlights: string[] = [],
  customContent: string = ''
): { subject: string; text: string; html: string } {
  const currentDate = new Date().toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'long', 
    day: 'numeric' 
  });
  
  const subject = `Our Kidz Weekly Update - ${currentDate}`;
  
  const text = `
Our Kidz Weekly Update

Hello ${recipientName ? recipientName : 'there'}!

Here are this week's highlights from Our Kidz:

${highlights.map(highlight => `• ${highlight}`).join('\n')}

${customContent}

Thank you for being part of the Our Kidz family!

Best regards,
The Our Kidz Team
Your journey to health freedom starts here!
  `.trim();

  const html = createBaseEmailTemplate({
    recipientName,
    content: `
      <h2>Weekly Update - ${currentDate}</h2>
      <p>Here are this week's highlights from Our Kidz:</p>
      
      <ul>
        ${highlights.map(highlight => `<li>${highlight}</li>`).join('')}
      </ul>
      
      ${customContent ? `<p>${customContent}</p>` : ''}
    `,
    additionalContent: `
      <p>Thank you for being part of the Our Kidz family! We're here to support you every step of the way.</p>
    `,
    ctaText: 'Visit Our Platform',
    ctaUrl: 'https://app.our-kidz.com'
  });

  return { subject, text, html };
}

// Export all template functions
export const emailTemplates = {
  welcome: createWelcomeEmail,
  contactConfirmation: createContactConfirmationEmail,
  adminNotification: createAdminNotificationEmail,
  invite: createInviteEmail,
  newsletter: createNewsletterEmail,
  newsletterWelcome: createNewsletterWelcomeEmail,
  base: createBaseEmailTemplate
};
