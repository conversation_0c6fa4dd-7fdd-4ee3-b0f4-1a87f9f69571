-- Supabase schema for Our Kidz voice-driven onboarding system
-- This file contains the database schema for user onboarding via <PERSON>'s voice interface

-- Enable UUID extension for generating unique IDs
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret-here';

-- Users table for parent/guardian information
CREATE TABLE IF NOT EXISTS public.users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    role VARCHAR(50) DEFAULT 'parent', -- parent, guardian, caregiver
    phone VARCHAR(20),
    zip VARCHAR(10),
    date_of_birth DATE,
    emergency_contact JSONB, -- {name: string, phone: string, relationship: string}
    preferences JSONB DEFAULT '{}', -- {notifications: boolean, language: string, etc.}
    onboarding_completed BOOLEAN DEFAULT FALSE,
    onboarding_step VARCHAR(50) DEFAULT 'welcome', -- welcome, user_info, family_info, complete
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Family members table for children and other family members
CREATE TABLE IF NOT EXISTS public.family_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    date_of_birth DATE NOT NULL,
    gender VARCHAR(20), -- male, female, other, prefer_not_to_say
    relationship VARCHAR(50) NOT NULL, -- child, stepchild, grandchild, etc.
    medical_conditions TEXT[], -- array of medical conditions
    allergies TEXT[], -- array of allergies
    medications JSONB DEFAULT '[]', -- [{name: string, dosage: string, frequency: string}]
    additional_notes TEXT,
    avatar VARCHAR(255), -- URL to avatar image
    is_primary BOOLEAN DEFAULT FALSE, -- marks primary child for quick access
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Onboarding sessions table to track progress and validation
CREATE TABLE IF NOT EXISTS public.onboarding_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
    session_data JSONB DEFAULT '{}', -- stores form data during onboarding
    current_step VARCHAR(50) DEFAULT 'welcome',
    validation_errors JSONB DEFAULT '[]',
    completed_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '24 hours'),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);
CREATE INDEX IF NOT EXISTS idx_family_members_user_id ON public.family_members(user_id);
CREATE INDEX IF NOT EXISTS idx_onboarding_sessions_user_id ON public.onboarding_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_onboarding_sessions_expires_at ON public.onboarding_sessions(expires_at);

-- Triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_users_updated_at 
    BEFORE UPDATE ON public.users 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_family_members_updated_at 
    BEFORE UPDATE ON public.family_members 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_onboarding_sessions_updated_at 
    BEFORE UPDATE ON public.onboarding_sessions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.family_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.onboarding_sessions ENABLE ROW LEVEL SECURITY;

-- Policies for users table
CREATE POLICY "Users can view their own profile" ON public.users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert their own profile" ON public.users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Policies for family_members table
CREATE POLICY "Users can view their own family members" ON public.family_members
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own family members" ON public.family_members
    FOR ALL USING (user_id = auth.uid());

-- Policies for onboarding_sessions table
CREATE POLICY "Users can view their own onboarding sessions" ON public.onboarding_sessions
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can manage their own onboarding sessions" ON public.onboarding_sessions
    FOR ALL USING (user_id = auth.uid());

-- Function to clean up expired onboarding sessions
CREATE OR REPLACE FUNCTION cleanup_expired_onboarding_sessions()
RETURNS void AS $$
BEGIN
    DELETE FROM public.onboarding_sessions 
    WHERE expires_at < NOW() AND completed_at IS NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to get user's onboarding progress
CREATE OR REPLACE FUNCTION get_onboarding_progress(user_email TEXT)
RETURNS TABLE (
    user_id UUID,
    current_step VARCHAR(50),
    onboarding_completed BOOLEAN,
    family_members_count INTEGER,
    session_data JSONB
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.id,
        u.onboarding_step,
        u.onboarding_completed,
        COALESCE(fm.count, 0)::INTEGER as family_members_count,
        COALESCE(os.session_data, '{}'::jsonb)
    FROM public.users u
    LEFT JOIN (
        SELECT user_id, COUNT(*) as count 
        FROM public.family_members 
        GROUP BY user_id
    ) fm ON u.id = fm.user_id
    LEFT JOIN public.onboarding_sessions os ON u.id = os.user_id 
        AND os.completed_at IS NULL 
        AND os.expires_at > NOW()
    WHERE u.email = user_email;
END;
$$ LANGUAGE plpgsql;

-- Function to validate family member data
CREATE OR REPLACE FUNCTION validate_family_member_data(
    member_name TEXT,
    member_dob DATE,
    member_relationship TEXT
) RETURNS JSONB AS $$
DECLARE
    errors JSONB := '[]'::jsonb;
    age_years INTEGER;
BEGIN
    -- Validate name
    IF member_name IS NULL OR LENGTH(TRIM(member_name)) = 0 THEN
        errors := errors || '["Name is required"]'::jsonb;
    END IF;
    
    -- Validate date of birth
    IF member_dob IS NULL THEN
        errors := errors || '["Date of birth is required"]'::jsonb;
    ELSIF member_dob > CURRENT_DATE THEN
        errors := errors || '["Date of birth cannot be in the future"]'::jsonb;
    ELSE
        age_years := DATE_PART('year', AGE(member_dob));
        IF age_years > 120 THEN
            errors := errors || '["Date of birth seems unrealistic"]'::jsonb;
        END IF;
    END IF;
    
    -- Validate relationship
    IF member_relationship IS NULL OR LENGTH(TRIM(member_relationship)) = 0 THEN
        errors := errors || '["Relationship is required"]'::jsonb;
    END IF;
    
    RETURN jsonb_build_object(
        'valid', jsonb_array_length(errors) = 0,
        'errors', errors
    );
END;
$$ LANGUAGE plpgsql;

-- Sample data for testing (comment out in production)
-- INSERT INTO public.users (id, email, name, role, phone, zip) VALUES 
-- ('f47ac10b-58cc-4372-a567-0e02b2c3d479', '<EMAIL>', 'Test Parent', 'parent', '555-0123', '12345');

COMMENT ON TABLE public.users IS 'Stores parent/guardian information for Our Kidz platform';
COMMENT ON TABLE public.family_members IS 'Stores information about children and family members';
COMMENT ON TABLE public.onboarding_sessions IS 'Tracks onboarding progress and temporary session data';