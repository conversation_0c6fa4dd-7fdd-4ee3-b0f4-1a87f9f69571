'use client'

import { createContext, useContext, useEffect, useState } from 'react'
import { User as AuthUser } from '@supabase/supabase-js'
import { supabase } from './supabase-client'

interface AuthContextType {
  user: AuthUser | null
  loading: boolean
  signUp: (email: string, password: string) => Promise<{ user: AuthUser | null; error: any }>
  signIn: (email: string, password: string) => Promise<{ user: AuthUser | null; error: any }>
  signInWithGoogle: () => Promise<{ user: AuthUser | null; error: any }>
  signOut: () => Promise<{ error: any }>
  resetPassword: (email: string) => Promise<{ error: any }>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

/**
 * Safe variant of useAuth that never throws. If the provider is missing,
 * returns no-op auth methods and `providerAvailable: false` so pages can
 * gracefully render in environments where the provider isn’t mounted
 * (e.g. debug pages or during static prerendering).
 */
export function useAuthSafe(): (AuthContextType & { providerAvailable: boolean }) {
  const context = useContext(AuthContext)
  if (context === undefined) {
    return {
      user: null,
      loading: false,
      signUp: async () => ({ user: null, error: 'provider_unavailable' as any }),
      signIn: async () => ({ user: null, error: 'provider_unavailable' as any }),
      signInWithGoogle: async () => ({ user: null, error: 'provider_unavailable' as any }),
      signOut: async () => ({ error: 'provider_unavailable' as any }),
      resetPassword: async () => ({ error: 'provider_unavailable' as any }),
      providerAvailable: false,
    } as unknown as (AuthContextType & { providerAvailable: boolean })
  }
  return { ...context, providerAvailable: true }
}

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    const getSession = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        setUser(session?.user ?? null)
      } catch (error) {
        console.error('Error getting session:', error)
      } finally {
        setLoading(false)
      }
    }

    getSession()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email ?? 'null')
        setUser(session?.user ?? null)
        setLoading(false)

        // Handle post-auth user creation
        if (event === 'SIGNED_IN' && session?.user) {
          try {
            // Check if user exists in our users table
            const { data: existingUser, error: checkError } = await supabase
              .from('users')
              .select('id, auth_user_id')
              .eq('auth_user_id', session.user.id)
              .maybeSingle()

            if (checkError && checkError.code !== 'PGRST116') {
              console.error('Error checking for existing user:', checkError)
            }

            if (!existingUser) {
              // Also check if user exists by email (for migration from old system)
              const { data: userByEmail, error: emailError } = await supabase
                .from('users')
                .select('id, auth_user_id')
                .eq('email', session.user.email!)
                .maybeSingle()

              if (userByEmail && !userByEmail.auth_user_id) {
                // Update existing user with auth_user_id
                const { error: updateError } = await supabase
                  .from('users')
                  .update({ auth_user_id: session.user.id })
                  .eq('id', userByEmail.id)

                if (updateError) {
                  console.error('Error linking existing user to auth:', updateError)
                } else {
                  console.log('Successfully linked existing user to auth account')
                }
              } else if (!userByEmail) {
                // Create new user record in our users table
                const { error: insertError } = await supabase
                  .from('users')
                  .insert({
                    auth_user_id: session.user.id,
                    email: session.user.email!,
                    name: session.user.user_metadata?.full_name || session.user.email!.split('@')[0],
                    role: 'parent',
                    onboarding_completed: false,
                    onboarding_step: 'authentication'
                  })

                if (insertError) {
                  console.error('Error creating user record:', insertError)
                } else {
                  console.log('User record created successfully for:', session.user.email)
                }
              }
            } else {
              console.log('User record already exists for:', session.user.email)
            }
          } catch (error) {
            console.error('Error handling post-auth user creation:', error)
          }
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const signUp = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          emailRedirectTo: `${window.location.origin}/auth/callback`
        }
      })

      if (error) {
        return { user: null, error }
      }

      const { data: { session } } = await supabase.auth.getSession()
      const user = session?.user

      return { user: user, error: null }
    } catch (error) {
      return { user: null, error }
    }
  }

  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        return { user: null, error }
      }

      const { data: { session } } = await supabase.auth.getSession()
      const user = session?.user

      return { user: user, error: null }
    } catch (error) {
      return { user: null, error }
    }
  }

  const signInWithGoogle = async () => {
    try {
      const { data, error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/auth/callback`
        }
      })

      if (error) {
        return { user: null, error }
      }

      const { data: { session } } = await supabase.auth.getSession()
      const user = session?.user

      return { user: user, error: null }
    } catch (error) {
      return { user: null, error }
    }
  }

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut()
      return { error }
    } catch (error) {
      return { error }
    }
  }

  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`
      })
      return { error }
    } catch (error) {
      return { error }
    }
  }

  const value = {
    user,
    loading,
    signUp,
    signIn,
    signInWithGoogle,
    signOut,
    resetPassword
  }

  return <AuthContext.Provider value={value as AuthContextType}>{children}</AuthContext.Provider>
}