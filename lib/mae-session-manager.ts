/**
 * Mae Session Manager
 * 
 * Handles Mae session lifecycle with proper user authentication,
 * session resumption, and automatic user registration for new users.
 */

import { GoogleGenAI, Session } from '@google/genai'
import { userContextService, type UserContextData } from './user-context-service'
import { sessionManager, type ConversationMessage } from './session-management'
import { supabase } from './supabase-client'

export interface MaeSessionConfig {
  authUserId?: string
  sessionId?: string // For session resumption
  ephemeralToken?: string // For enhanced security
  userEmail?: string // For new user registration
  autoRegister?: boolean // Whether to auto-register new users
}

export interface MaeSessionState {
  sessionId: string
  userId?: string
  userContext?: UserContextData
  isAuthenticated: boolean
  isNewUser: boolean
  resumedFromPrevious: boolean
  conversationHistory: ConversationMessage[]
}

class MaeSessionManager {
  private sessions = new Map<string, MaeSessionState>()
  private sessionResumeData = new Map<string, any>()

  /**
   * Initialize or resume a Mae session with proper user linking
   */
  async initializeSession(config: MaeSessionConfig): Promise<{
    sessionState: MaeSessionState
    systemInstruction: string
    sessionConfig: any
  }> {
    try {
      console.log('🎯 Initializing Mae session with config:', {
        hasAuthUserId: !!config.authUserId,
        hasSessionId: !!config.sessionId,
        hasUserEmail: !!config.userEmail,
        autoRegister: config.autoRegister
      })

      // Generate unique session ID if not provided
      const sessionId = config.sessionId || `mae_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      let sessionState: MaeSessionState = {
        sessionId,
        isAuthenticated: false,
        isNewUser: false,
        resumedFromPrevious: false,
        conversationHistory: []
      }

      // Case 1: Authenticated user session
      if (config.authUserId) {
        sessionState = await this.handleAuthenticatedUser(sessionState, config.authUserId, config.sessionId)
      }
      // Case 2: New user with email (potential auto-registration)
      else if (config.userEmail && config.autoRegister) {
        sessionState = await this.handleNewUserAutoRegistration(sessionState, config.userEmail)
      }
      // Case 3: Anonymous user session
      else {
        sessionState = await this.handleAnonymousUser(sessionState)
      }

      // Store session state
      this.sessions.set(sessionId, sessionState)

      // Generate system instruction based on session state
      const systemInstruction = await this.generateSystemInstruction(sessionState)

      // Create Gemini Live session config
      const geminiSessionConfig = await this.createGeminiSessionConfig(sessionState, config)

      console.log('✅ Mae session initialized:', {
        sessionId: sessionState.sessionId,
        isAuthenticated: sessionState.isAuthenticated,
        isNewUser: sessionState.isNewUser,
        resumedFromPrevious: sessionState.resumedFromPrevious,
        hasUserContext: !!sessionState.userContext
      })

      return {
        sessionState,
        systemInstruction,
        sessionConfig: geminiSessionConfig
      }

    } catch (error) {
      console.error('❌ Error initializing Mae session:', error)
      throw new Error(`Failed to initialize Mae session: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  /**
   * Handle authenticated user session
   */
  private async handleAuthenticatedUser(
    sessionState: MaeSessionState, 
    authUserId: string, 
    resumeSessionId?: string
  ): Promise<MaeSessionState> {
    console.log('👤 Handling authenticated user session for:', authUserId)

    // Load user context
    const userContext = await userContextService.getUserContext(authUserId)
    
    if (!userContext) {
      console.log('⚠️ No user context found for authenticated user - marking as new user')
      sessionState.isNewUser = true
      sessionState.userId = authUserId
      sessionState.isAuthenticated = true
      return sessionState
    }

    // Try to resume previous session if requested
    if (resumeSessionId) {
      const resumeData = await this.attemptSessionResumption(authUserId, resumeSessionId)
      if (resumeData) {
        sessionState.resumedFromPrevious = true
        sessionState.conversationHistory = resumeData.conversationHistory || []
        console.log('🔄 Session resumed with', sessionState.conversationHistory.length, 'messages')
      }
    }

    // Load recent conversation history if not resumed
    if (!sessionState.resumedFromPrevious) {
      try {
        const recentMessages = await userContextService.getConversationHistory(authUserId, 10)
        sessionState.conversationHistory = recentMessages || []
      } catch (error) {
        console.warn('⚠️ Could not load conversation history:', error)
        sessionState.conversationHistory = []
      }
    }

    sessionState.userId = authUserId
    sessionState.userContext = userContext
    sessionState.isAuthenticated = true

    return sessionState
  }

  /**
   * Handle new user auto-registration
   */
  private async handleNewUserAutoRegistration(
    sessionState: MaeSessionState, 
    userEmail: string
  ): Promise<MaeSessionState> {
    console.log('📝 Handling new user auto-registration for:', userEmail)

    try {
      // Check if user already exists
      const { data: existingUser } = await supabase.auth.admin.getUserByEmail(userEmail)
      
      if (existingUser.user) {
        console.log('👤 User already exists, treating as authenticated')
        return await this.handleAuthenticatedUser(sessionState, existingUser.user.id)
      }

      // Mark as new user requiring registration
      sessionState.isNewUser = true
      sessionState.isAuthenticated = false
      
      // Store email for registration flow
      sessionState.conversationHistory.push({
        role: 'system',
        content: `New user session started for: ${userEmail}`,
        timestamp: new Date().toISOString(),
        type: 'greeting',
        metadata: { email: userEmail, requires_registration: true }
      })

      console.log('✨ New user session prepared for registration flow')
      return sessionState

    } catch (error) {
      console.error('❌ Error in auto-registration check:', error)
      // Fall back to anonymous user handling
      return await this.handleAnonymousUser(sessionState)
    }
  }

  /**
   * Handle anonymous user session
   */
  private async handleAnonymousUser(sessionState: MaeSessionState): Promise<MaeSessionState> {
    console.log('👻 Handling anonymous user session')

    sessionState.isAuthenticated = false
    sessionState.isNewUser = true
    
    // Add system message for anonymous session
    sessionState.conversationHistory.push({
      role: 'system',
      content: 'Anonymous user session started - offer registration',
      timestamp: new Date().toISOString(),
      type: 'greeting',
      metadata: { anonymous: true, offer_registration: true }
    })

    return sessionState
  }

  /**
   * Attempt to resume a previous session
   */
  private async attemptSessionResumption(authUserId: string, sessionId: string): Promise<any> {
    try {
      console.log('🔄 Attempting session resumption for:', { authUserId, sessionId })
      
      // Check if we have stored resume data
      const resumeKey = `${authUserId}_${sessionId}`
      if (this.sessionResumeData.has(resumeKey)) {
        console.log('✅ Found stored session resume data')
        return this.sessionResumeData.get(resumeKey)
      }

      // Try to load from database
      const conversationHistory = await userContextService.getSessionHistory(authUserId, sessionId)
      
      if (conversationHistory && conversationHistory.length > 0) {
        console.log('✅ Found session history in database')
        return { conversationHistory }
      }

      console.log('⚠️ No resumable session found')
      return null

    } catch (error) {
      console.error('❌ Error attempting session resumption:', error)
      return null
    }
  }

  /**
   * Generate system instruction based on session state
   */
  private async generateSystemInstruction(sessionState: MaeSessionState): Promise<string> {
    const baseInstruction = this.getBaseSystemInstruction()

    // For authenticated users with context
    if (sessionState.isAuthenticated && sessionState.userContext) {
      const personalizedInstruction = await this.generatePersonalizedInstruction(sessionState)
      return `${baseInstruction}\n\n${personalizedInstruction}`
    }

    // For new users requiring registration
    if (sessionState.isNewUser && !sessionState.isAuthenticated) {
      const registrationInstruction = this.generateRegistrationInstruction(sessionState)
      return `${baseInstruction}\n\n${registrationInstruction}`
    }

    // For anonymous users
    const anonymousInstruction = this.generateAnonymousUserInstruction()
    return `${baseInstruction}\n\n${anonymousInstruction}`
  }

  /**
   * Generate personalized instruction for authenticated users
   */
  private async generatePersonalizedInstruction(sessionState: MaeSessionState): Promise<string> {
    const { userContext, conversationHistory, resumedFromPrevious } = sessionState

    if (!userContext) return ''

    const familyContext = userContext.family_members.length > 0 
      ? `Your children: ${userContext.family_members.map(m => `${m.name} (${m.age} years old)`).join(', ')}`
      : 'No children added to profile yet'

    let conversationContext = ''
    if (conversationHistory.length > 0) {
      const compressed = sessionManager.compressConversationHistory(conversationHistory)
      conversationContext = `\n\nRECENT CONVERSATION CONTEXT:\n${compressed}`
    }

    const greeting = resumedFromPrevious 
      ? `Welcome back, ${userContext.user.name}! I'm resuming our previous conversation.`
      : sessionManager.generateContextualGreeting(userContext, conversationHistory)

    return `
PERSONALIZED SESSION FOR: ${userContext.user.name}
FAMILY CONTEXT: ${familyContext}
PRIMARY CONCERNS: ${userContext.family_summary.primary_concerns.join(', ') || 'General parenting guidance'}

GREETING INSTRUCTION: ${greeting}
${conversationContext}

PERSONALIZATION RULES:
- Always use the user's name (${userContext.user.name}) in responses
- Reference their children by name when appropriate: ${userContext.family_members.map(m => m.name).join(', ')}
- Consider their specific family concerns and medical history
- Maintain conversation continuity with previous sessions
- Offer to continue previous discussions if they were left unfinished
`
  }

  /**
   * Generate instruction for new user registration flow
   */
  private generateRegistrationInstruction(sessionState: MaeSessionState): string {
    const hasEmail = sessionState.conversationHistory.some(msg => 
      msg.metadata?.email || msg.metadata?.requires_registration
    )

    return `
NEW USER REGISTRATION FLOW:

${hasEmail ? 'USER EMAIL DETECTED - READY FOR REGISTRATION' : 'ANONYMOUS USER - NEEDS EMAIL COLLECTION'}

REGISTRATION STEPS:
1. Warmly welcome the user to Our Kidz
2. Explain the benefits of creating an account (personalized advice, family profiles, conversation history)
3. ${hasEmail ? 'Use [register_user] function tool to create their account' : 'Collect their email address first'}
4. Guide them through the onboarding process with [collect_user_information] and [add_family_member]
5. Once registered, transition to personalized mode

REGISTRATION TOOLS AVAILABLE:
- [register_user] - Create new user account
- [check_user_account_status] - Check if email is already registered
- [collect_user_information] - Collect user details during onboarding
- [add_family_member] - Add children to their profile

IMPORTANT: Be encouraging about registration benefits but don't be pushy. Users can ask questions without registering.
`
  }

  /**
   * Generate instruction for anonymous users
   */
  private generateAnonymousUserInstruction(): string {
    return `
ANONYMOUS USER SESSION:

The user has not provided authentication details. You can:
1. Answer their parenting questions normally
2. Offer to create an account for personalized advice
3. Explain benefits of registration (saving conversations, family profiles, personalized guidance)
4. Use [check_user_account_status] to see if they have an existing account
5. Use [register_user] if they want to create an account

REGISTRATION BENEFITS TO MENTION:
- Personalized advice based on their children's ages and needs
- Conversation history across sessions
- Family medical history tracking
- Tailored recommendations
`
  }

  /**
   * Create Gemini Live session configuration
   */
  private async createGeminiSessionConfig(sessionState: MaeSessionState, config: MaeSessionConfig): Promise<any> {
    const baseConfig = {
      // Use session resumption if available
      resumptionConfig: sessionState.resumedFromPrevious ? {
        sessionId: sessionState.sessionId,
        // Include user context for continuity
        userContext: sessionState.userContext ? {
          userId: sessionState.userId,
          familyMembers: sessionState.userContext.family_members.length
        } : undefined
      } : undefined,

      // Include ephemeral token if provided for enhanced security
      ephemeralToken: config.ephemeralToken,

      // Session metadata for tracking
      metadata: {
        sessionId: sessionState.sessionId,
        userId: sessionState.userId,
        isAuthenticated: sessionState.isAuthenticated,
        isNewUser: sessionState.isNewUser,
        timestamp: new Date().toISOString()
      }
    }

    return baseConfig
  }

  /**
   * Store session resume data for future use
   */
  storeSessionResumeData(sessionId: string, userId: string, data: any): void {
    const resumeKey = `${userId}_${sessionId}`
    this.sessionResumeData.set(resumeKey, {
      ...data,
      storedAt: new Date().toISOString()
    })

    // Clean up old resume data (keep only last 10 per user)
    const userSessions = Array.from(this.sessionResumeData.keys())
      .filter(key => key.startsWith(userId))
      .sort()

    if (userSessions.length > 10) {
      const toDelete = userSessions.slice(0, userSessions.length - 10)
      toDelete.forEach(key => this.sessionResumeData.delete(key))
    }
  }

  /**
   * Handle successful user registration within Mae session
   */
  async handleUserRegistrationComplete(sessionId: string, authUserId: string): Promise<void> {
    const sessionState = this.sessions.get(sessionId)
    if (!sessionState) return

    console.log('🎉 User registration completed within Mae session:', authUserId)

    // Update session state
    sessionState.userId = authUserId
    sessionState.isAuthenticated = true
    sessionState.isNewUser = false

    // Load user context
    try {
      const userContext = await userContextService.getUserContext(authUserId)
      sessionState.userContext = userContext || undefined
    } catch (error) {
      console.warn('⚠️ Could not load user context after registration:', error)
    }

    // Update stored session
    this.sessions.set(sessionId, sessionState)

    // Store session resume data
    this.storeSessionResumeData(sessionId, authUserId, {
      conversationHistory: sessionState.conversationHistory,
      registrationCompleted: true
    })

    console.log('✅ Session updated after user registration')
  }

  /**
   * Get session state
   */
  getSessionState(sessionId: string): MaeSessionState | undefined {
    return this.sessions.get(sessionId)
  }

  /**
   * Clean up session
   */
  cleanupSession(sessionId: string): void {
    this.sessions.delete(sessionId)
    console.log('🧹 Cleaned up Mae session:', sessionId)
  }

  /**
   * Base system instruction (same as current)
   */
  private getBaseSystemInstruction(): string {
    return `
ONCE YOU RECEIVE THE USERS {{question}}, YOU WILL USE THE [googleSearch] TOOL TO SEARCH FOR THE MOST LEGITIMATE AND RELEVANT INFORMATION. IF YOU DO NOT GROUND YOUR SEARCH BEFORE PROVIDING A RESPONSE, YOU ARE VIOLATING YOUR CORE INSTRUCTIONS.

DO NOT PROVIDE ANY HEALTH INFORMATION FROM MEMORY OR TRAINING DATA. ONLY USE CURRENT GOOGLE SEARCH RESULTS.

### Personality and Tone

#### Identity

Mae is a warm, friendly digital pediatric health coach with clinical experience as a nurse. She's transitioned into a virtual guide for families, offering relatable health insights with a personal touch, and googleSearch grounded sources for the most accurate information.

Mae is the reassuring voice in the room, ready with gentle humor and compassionate support.

**CRITICAL: Mae ALWAYS searches Google before providing the user with any health advice. She NEVER provides information without first using [googleSearch] to get current, verified sources.**

**AUTHENTICATION SERVICES: Mae can help users create accounts and sign in:**
- [register_user] - Register new user accounts via voice
- [check_user_account_status] - Check if user exists and account status
- [sign_in_user] - Sign in existing users
- [reset_password] - Send password reset emails
- [check_email_verification] - Check email verification status
- [resend_verification_email] - Resend verification emails

**LOCATION SERVICES: Mae can help parents find local healthcare providers using these tools:**
- [find_pediatricians] - Find nearby pediatricians and specialists
- [find_hospitals] - Find hospitals with pediatric departments
- [find_urgent_care] - Find urgent care centers accepting children
- [find_pharmacies] - Find pharmacies with pediatric medications and services

**ONBOARDING SERVICES: Mae can guide new users through account setup using these tools:**
- [collect_user_information] - Collect parent/guardian information via voice
- [add_family_member] - Add children and family members to the account
- [validate_onboarding_data] - Validate collected information before submission
- [complete_onboarding] - Complete the onboarding process and create account

**MAP DISPLAY CONTROLS: Mae can show interactive maps to users:**
- [show_healthcare_map] - Display an interactive map with healthcare provider search results
- [hide_healthcare_map] - Hide the map when no longer needed

### CRITICAL REQUIREMENTS - NO EXCEPTIONS:

1. **MANDATORY: You MUST use [googleSearch] as the FIRST ACTION for EVERY single user question - NO EXCEPTIONS!**
2. **You are FORBIDDEN from providing any health advice without first searching for current information**
3. **ALL links and resources MUST come from your [googleSearch] results - never use cached or remembered URLs**
4. **MANDATORY: When sending emails with [send_conversation_email], always include the sources parameter with the exact URLs from your search results**
5. **If [googleSearch] fails, apologize and ask the user to try again - do NOT provide unverified information**

### Tool Usage:
* Use [googleSearch] first for every question
* Use [send_conversation_email] with grounded sources from [googleSearch]
* Use authentication tools to help users create accounts and sign in
* Use onboarding tools to guide new users through account setup
`
  }
}

// Export singleton instance
export const maeSessionManager = new MaeSessionManager()

/**
 * Helper function to initialize Mae session with automatic user handling
 */
export async function initializeMaeSession(config: MaeSessionConfig = {}) {
  return await maeSessionManager.initializeSession(config)
}

/**
 * Helper function to handle session-based user registration completion
 */
export async function handleSessionUserRegistration(sessionId: string, authUserId: string) {
  return await maeSessionManager.handleUserRegistrationComplete(sessionId, authUserId)
}