/**
 * Mae Navigation Handler
 * 
 * This module handles navigation events from <PERSON> while preserving voice sessions.
 * It prevents hard page reloads that would destroy WebSocket connections.
 */

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

// Global navigation handler that preserves voice sessions
export function useMaeNavigationHandler() {
  const router = useRouter()

  useEffect(() => {
    const handleMaeNavigation = (event: CustomEvent) => {
      console.log('🚀 Mae navigation event received:', event.detail)
      
      const { message, preserveSession } = event.detail
      
      if (preserveSession) {
        console.log('🎤 Preserving voice session during navigation')
        
        // Store additional context for seamless continuation
        sessionStorage.setItem('mae-session-preservation', JSON.stringify({
          preservedAt: Date.now(),
          navigationReason: message || 'Onboarding navigation',
          preserveSession: true
        }))
        
        // Use Next.js router for client-side navigation (no page reload)
        router.push('/onboarding')
        
        // Dispatch continuation event after navigation completes
        setTimeout(() => {
          console.log('🎤 Dispatching session continuation event')
          window.dispatchEvent(new CustomEvent('mae-continue-session', {
            detail: {
              preservedSession: true,
              navigationMessage: message
            }
          }))
        }, 100)
      } else {
        // Fallback to hard navigation if session preservation not requested
        window.location.href = '/onboarding'
      }
    }

    // Listen for Mae navigation events
    window.addEventListener('mae-navigate-to-onboarding', handleMaeNavigation as EventListener)
    
    return () => {
      window.removeEventListener('mae-navigate-to-onboarding', handleMaeNavigation as EventListener)
    }
  }, [router])
}

// Enhanced session continuation handler for the onboarding page
export function useMaeSessionContinuation() {
  useEffect(() => {
    const handleSessionContinuation = (event: CustomEvent) => {
      console.log('🎤 Mae session continuation event received:', event.detail)
      
      const preservationData = sessionStorage.getItem('mae-session-preservation')
      const conversationContext = sessionStorage.getItem('mae-conversation-context')
      
      if (preservationData && conversationContext) {
        const preservation = JSON.parse(preservationData)
        const context = JSON.parse(conversationContext)
        
        console.log('📥 Restoring Mae session context:', { preservation, context })
        
        // Dispatch event to restart Mae with context
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('mae-restart-with-context', {
            detail: {
              preservedSession: true,
              context: context,
              preservation: preservation,
              message: context.message || 'Continuing our conversation about onboarding...'
            }
          }))
        }, 500)
        
        // Clean up session storage
        sessionStorage.removeItem('mae-session-preservation')
        sessionStorage.removeItem('mae-conversation-context')
      }
    }

    // Listen for session continuation events
    window.addEventListener('mae-continue-session', handleSessionContinuation as EventListener)
    
    return () => {
      window.removeEventListener('mae-continue-session', handleSessionContinuation as EventListener)
    }
  }, [])
}

// Voice interface continuation handler
export function useMaeVoiceInterfaceContinuation(voiceInterfaceRef: React.RefObject<any>) {
  useEffect(() => {
    const handleVoiceRestart = (event: CustomEvent) => {
      console.log('🎤 Mae voice interface restart event received:', event.detail)
      
      const { preservedSession, context, message } = event.detail
      
      if (preservedSession && voiceInterfaceRef.current) {
        console.log('🎤 Restarting Mae voice interface with preserved context')
        
        // Restart the voice interface with context
        if (typeof voiceInterfaceRef.current.restartWithContext === 'function') {
          voiceInterfaceRef.current.restartWithContext(context, message)
        } else if (typeof voiceInterfaceRef.current.restartSession === 'function') {
          // Fallback to basic restart
          voiceInterfaceRef.current.restartSession(message)
        } else {
          console.warn('🎤 Voice interface does not support context restart')
        }
      }
    }

    // Listen for voice interface restart events
    window.addEventListener('mae-restart-with-context', handleVoiceRestart as EventListener)
    
    return () => {
      window.removeEventListener('mae-restart-with-context', handleVoiceRestart as EventListener)
    }
  }, [voiceInterfaceRef])
}

// Export convenience function for initializing all handlers
export function initializeMaeNavigationHandlers() {
  console.log('🚀 Initializing Mae navigation handlers')
  
  // Set up global flag to indicate navigation handlers are active
  if (typeof window !== 'undefined') {
    (window as any).__mae_navigation_handlers_active = true
  }
}

// Check if navigation handlers are active
export function isMaeNavigationHandlersActive(): boolean {
  if (typeof window !== 'undefined') {
    return (window as any).__mae_navigation_handlers_active === true
  }
  return false
}