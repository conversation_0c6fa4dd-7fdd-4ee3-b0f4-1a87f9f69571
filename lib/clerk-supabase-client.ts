import { createClient } from '@supabase/supabase-js'

// Create a Supabase client for client-side operations
export function createBrowserClient() {
  return createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL!,
    process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
  )
}

// Client-side helper to fetch user by clerk_id
export async function fetchUserByClerkId(clerkId: string) {
  const supabase = createBrowserClient()
  
  const { data, error } = await supabase
    .from('users')
    .select('*')
    .eq('clerk_id', clerkId)
    .single()

  if (error && error.code !== 'PGRST116') {
    throw error
  }

  return data
}

// Client-side helper to fetch user's family members
export async function fetchFamilyMembers(userId: string) {
  const supabase = createBrowserClient()
  
  const { data, error } = await supabase
    .from('family_members')
    .select('*')
    .eq('user_id', userId)
    .order('created_at', { ascending: true })

  if (error) throw error
  return data || []
}