/**
 * Mae Authentication Function Tools
 * 
 * This module provides function tools that enable <PERSON> to handle user registration,
 * email verification, and authentication via voice interactions and MCP integration.
 */

import { Type, FunctionDeclaration } from '@google/genai'

// User registration function tool declaration
export const registerUserToolDeclaration: FunctionDeclaration = {
  name: 'register_user',
  description: 'Register a new user account with email and password via Mae voice interaction',
  parameters: {
    type: Type.OBJECT,
    properties: {
      email: {
        type: Type.STRING,
        description: 'User email address for registration'
      },
      password: {
        type: Type.STRING,
        description: 'User password (will be securely handled)'
      },
      full_name: {
        type: Type.STRING,
        description: 'User full name'
      },
      confirm_password: {
        type: Type.STRING,
        description: 'Password confirmation to ensure accuracy'
      }
    },
    required: ['email', 'password', 'full_name']
  }
}

// Email verification status check tool declaration
export const checkEmailVerificationToolDeclaration: FunctionDeclaration = {
  name: 'check_email_verification',
  description: 'Check if a user has verified their email address',
  parameters: {
    type: Type.OBJECT,
    properties: {
      email: {
        type: Type.STRING,
        description: 'Email address to check verification status for'
      }
    },
    required: ['email']
  }
}

// Resend verification email tool declaration
export const resendVerificationEmailToolDeclaration: FunctionDeclaration = {
  name: 'resend_verification_email',
  description: 'Resend email verification link to user',
  parameters: {
    type: Type.OBJECT,
    properties: {
      email: {
        type: Type.STRING,
        description: 'Email address to resend verification to'
      }
    },
    required: ['email']
  }
}

// User sign in tool declaration
export const signInUserToolDeclaration: FunctionDeclaration = {
  name: 'sign_in_user',
  description: 'Sign in an existing user with email and password',
  parameters: {
    type: Type.OBJECT,
    properties: {
      email: {
        type: Type.STRING,
        description: 'User email address'
      },
      password: {
        type: Type.STRING,
        description: 'User password'
      }
    },
    required: ['email', 'password']
  }
}

// Password reset tool declaration
export const resetPasswordToolDeclaration: FunctionDeclaration = {
  name: 'reset_password',
  description: 'Send password reset email to user',
  parameters: {
    type: Type.OBJECT,
    properties: {
      email: {
        type: Type.STRING,
        description: 'Email address to send reset link to'
      }
    },
    required: ['email']
  }
}

// Check user account status tool declaration
export const checkUserAccountStatusToolDeclaration: FunctionDeclaration = {
  name: 'check_user_account_status',
  description: 'Check comprehensive user account status including verification, profile completion, etc.',
  parameters: {
    type: Type.OBJECT,
    properties: {
      email: {
        type: Type.STRING,
        description: 'Email address to check status for'
      }
    },
    required: ['email']
  }
}

// Function tool handlers
export async function handleRegisterUser(parameters: {
  email: string
  password: string
  full_name: string
  confirm_password?: string
}) {
  try {
    console.log('🔐 Registering new user via Mae:', parameters.email)

    // Dispatch activity event for Mae status tracking
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('mae-function-call', {
        detail: {
          functionName: 'register_user',
          parameters: { ...parameters, password: '[REDACTED]' },
          status: 'active'
        }
      }))
    }

    // Validate password confirmation if provided
    if (parameters.confirm_password && parameters.password !== parameters.confirm_password) {
      return {
        success: false,
        error: 'Password confirmation does not match. Please try again.',
        field: 'password_confirmation'
      }
    }

    // Validate password strength
    if (parameters.password.length < 6) {
      return {
        success: false,
        error: 'Password must be at least 6 characters long.',
        field: 'password'
      }
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(parameters.email)) {
      return {
        success: false,
        error: 'Please provide a valid email address.',
        field: 'email'
      }
    }

    // Register user via API endpoint
    const baseUrl = typeof window !== 'undefined' ? window.location.origin : process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
    
    const registrationResponse = await fetch(`${baseUrl}/api/mae-auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: parameters.email,
        password: parameters.password,
        full_name: parameters.full_name
      })
    })

    if (!registrationResponse.ok) {
      const errorData = await registrationResponse.json()
      
      if (registrationResponse.status === 429) {
        return {
          success: false,
          error: 'Too many registration attempts. Please wait a moment and try again.',
          field: 'rate_limit'
        }
      }

      return {
        success: false,
        error: errorData.error || 'Registration failed. Please try again.',
        field: errorData.field || 'general'
      }
    }

    const registrationData = await registrationResponse.json()

    // Dispatch success event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('mae-function-call', {
        detail: {
          functionName: 'register_user',
          parameters: { ...parameters, password: '[REDACTED]' },
          status: 'completed'
        }
      }))

      // Also dispatch auth state change for UI updates
      window.dispatchEvent(new CustomEvent('mae-auth-state-change', {
        detail: {
          type: 'user_registered',
          email: parameters.email,
          verification_required: true
        }
      }))
    }

    return {
      success: true,
      message: `Account created successfully for ${parameters.email}! Please check your email for a verification link.`,
      email: parameters.email,
      verification_required: true,
      user_id: registrationData.user_id
    }

  } catch (error) {
    console.error('Error in handleRegisterUser:', error)
    
    // Dispatch error event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('mae-function-call', {
        detail: {
          functionName: 'register_user',
          parameters: { ...parameters, password: '[REDACTED]' },
          status: 'error'
        }
      }))
    }

    return {
      success: false,
      error: 'Registration failed due to a technical error. Please try again.',
      field: 'general'
    }
  }
}

export async function handleCheckEmailVerification(parameters: {
  email: string
}) {
  try {
    console.log('📧 Checking email verification status for:', parameters.email)

    const baseUrl = typeof window !== 'undefined' ? window.location.origin : process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
    
    const verificationResponse = await fetch(`${baseUrl}/api/mae-auth/verify-status?email=${encodeURIComponent(parameters.email)}`)

    if (!verificationResponse.ok) {
      if (verificationResponse.status === 404) {
        return {
          success: true,
          message: 'No account found with that email address.',
          verified: false,
          user_exists: false
        }
      }

      const errorData = await verificationResponse.json()
      return {
        success: false,
        error: errorData.error || 'Failed to check verification status'
      }
    }

    const verificationData = await verificationResponse.json()

    return {
      success: true,
      message: verificationData.verified 
        ? 'Email address is verified and ready to use!'
        : 'Email address is not yet verified. Please check your email for the verification link.',
      verified: verificationData.verified,
      user_exists: true,
      last_sign_in: verificationData.last_sign_in_at,
      created_at: verificationData.created_at
    }

  } catch (error) {
    console.error('Error in handleCheckEmailVerification:', error)
    return {
      success: false,
      error: 'Failed to check email verification status due to a technical error.'
    }
  }
}

export async function handleResendVerificationEmail(parameters: {
  email: string
}) {
  try {
    console.log('📬 Resending verification email to:', parameters.email)

    const baseUrl = typeof window !== 'undefined' ? window.location.origin : process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
    
    const resendResponse = await fetch(`${baseUrl}/api/mae-auth/resend-verification`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: parameters.email
      })
    })

    if (!resendResponse.ok) {
      const errorData = await resendResponse.json()
      
      if (resendResponse.status === 429) {
        return {
          success: false,
          error: 'Too many verification emails sent. Please wait a few minutes before requesting another.'
        }
      }

      return {
        success: false,
        error: errorData.error || 'Failed to resend verification email'
      }
    }

    const resendData = await resendResponse.json()

    return {
      success: true,
      message: `Verification email has been resent to ${parameters.email}. Please check your inbox and spam folder.`,
      email: parameters.email,
      resent_at: new Date().toISOString()
    }

  } catch (error) {
    console.error('Error in handleResendVerificationEmail:', error)
    return {
      success: false,
      error: 'Failed to resend verification email due to a technical error.'
    }
  }
}

export async function handleSignInUser(parameters: {
  email: string
  password: string
}) {
  try {
    console.log('🔑 Signing in user via Mae:', parameters.email)

    // Dispatch activity event for Mae status tracking
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('mae-function-call', {
        detail: {
          functionName: 'sign_in_user',
          parameters: { ...parameters, password: '[REDACTED]' },
          status: 'active'
        }
      }))
    }

    const baseUrl = typeof window !== 'undefined' ? window.location.origin : process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
    
    const signInResponse = await fetch(`${baseUrl}/api/mae-auth/sign-in`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: parameters.email,
        password: parameters.password
      })
    })

    if (!signInResponse.ok) {
      const errorData = await signInResponse.json()
      
      if (signInResponse.status === 429) {
        return {
          success: false,
          error: 'Too many sign-in attempts. Please wait a moment and try again.',
          field: 'rate_limit'
        }
      }

      // Dispatch error event
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('mae-function-call', {
          detail: {
            functionName: 'sign_in_user',
            parameters: { ...parameters, password: '[REDACTED]' },
            status: 'error'
          }
        }))
      }

      return {
        success: false,
        error: errorData.error || 'Sign in failed. Please check your email and password.',
        field: errorData.field || 'credentials'
      }
    }

    const signInData = await signInResponse.json()

    // Dispatch success event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('mae-function-call', {
        detail: {
          functionName: 'sign_in_user',
          parameters: { ...parameters, password: '[REDACTED]' },
          status: 'completed'
        }
      }))

      // Also dispatch auth state change for UI updates
      window.dispatchEvent(new CustomEvent('mae-auth-state-change', {
        detail: {
          type: 'user_signed_in',
          email: parameters.email,
          user: signInData.user
        }
      }))
    }

    return {
      success: true,
      message: `Welcome back! You're now signed in as ${parameters.email}.`,
      email: parameters.email,
      user: signInData.user,
      session: signInData.session
    }

  } catch (error) {
    console.error('Error in handleSignInUser:', error)
    
    // Dispatch error event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('mae-function-call', {
        detail: {
          functionName: 'sign_in_user',
          parameters: { ...parameters, password: '[REDACTED]' },
          status: 'error'
        }
      }))
    }

    return {
      success: false,
      error: 'Sign in failed due to a technical error. Please try again.',
      field: 'general'
    }
  }
}

export async function handleResetPassword(parameters: {
  email: string
}) {
  try {
    console.log('🔄 Initiating password reset for:', parameters.email)

    const baseUrl = typeof window !== 'undefined' ? window.location.origin : process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
    
    const resetResponse = await fetch(`${baseUrl}/api/mae-auth/reset-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        email: parameters.email
      })
    })

    if (!resetResponse.ok) {
      const errorData = await resetResponse.json()
      
      if (resetResponse.status === 429) {
        return {
          success: false,
          error: 'Too many password reset requests. Please wait a few minutes before trying again.'
        }
      }

      return {
        success: false,
        error: errorData.error || 'Failed to send password reset email'
      }
    }

    const resetData = await resetResponse.json()

    return {
      success: true,
      message: `Password reset email has been sent to ${parameters.email}. Please check your inbox for reset instructions.`,
      email: parameters.email,
      reset_sent_at: new Date().toISOString()
    }

  } catch (error) {
    console.error('Error in handleResetPassword:', error)
    return {
      success: false,
      error: 'Failed to send password reset email due to a technical error.'
    }
  }
}

export async function handleCheckUserAccountStatus(parameters: {
  email: string
}) {
  try {
    console.log('👤 Checking comprehensive account status for:', parameters.email)

    const baseUrl = typeof window !== 'undefined' ? window.location.origin : process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'
    
    const statusResponse = await fetch(`${baseUrl}/api/mae-auth/account-status?email=${encodeURIComponent(parameters.email)}`)

    if (!statusResponse.ok) {
      if (statusResponse.status === 404) {
        return {
          success: true,
          message: 'No account found with that email address. Would you like to create one?',
          account_exists: false,
          can_register: true
        }
      }

      const errorData = await statusResponse.json()
      return {
        success: false,
        error: errorData.error || 'Failed to check account status'
      }
    }

    const statusData = await statusResponse.json()

    return {
      success: true,
      message: 'Account status retrieved successfully',
      account_exists: true,
      ...statusData
    }

  } catch (error) {
    console.error('Error in handleCheckUserAccountStatus:', error)
    return {
      success: false,
      error: 'Failed to check account status due to a technical error.'
    }
  }
}

// Export all function declarations for easy import
export const maeAuthFunctionDeclarations: FunctionDeclaration[] = [
  registerUserToolDeclaration,
  checkEmailVerificationToolDeclaration,
  resendVerificationEmailToolDeclaration,
  signInUserToolDeclaration,
  resetPasswordToolDeclaration,
  checkUserAccountStatusToolDeclaration
]