/**
 * Mae User Context Tools
 * 
 * Function tools that allow <PERSON> to access user information and family context
 * when users are authenticated.
 */

import { Type, FunctionDeclaration } from '@google/genai'

export const maeUserContextFunctionDeclarations: FunctionDeclaration[] = [
  {
    name: 'get_user_context',
    description: 'Get comprehensive user context including family data, conversation history, care logs, library content, medical data, and usage analytics. This provides <PERSON> with rich context about the user and their family for deeply personalized assistance. Includes audio chat sessions, care log entries, user library, pre-visit medical data, and session analytics.',
    parameters: {
      type: Type.OBJECT,
      properties: {},
      required: []
    }
  },
  {
    name: 'check_authentication_status',
    description: 'Check if the current user is authenticated via Clerk and has an account in our database. Use this to determine if you can access personalized information.',
    parameters: {
      type: Type.OBJECT,
      properties: {},
      required: []
    }
  }
]

export async function handleGetUserContext(args: {} = {}) {
  try {
    console.log('🔍 Mae requesting user context for currently authenticated user')

    const response = await fetch('/api/mae-user-context', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({})
    })

    if (!response.ok) {
      const errorText = await response.text()
      console.error('❌ API response error:', response.status, errorText)
      return {
        success: false,
        message: `API error: ${response.status}`,
        error: errorText
      }
    }

    let result
    try {
      result = await response.json()
    } catch (parseError) {
      console.error('❌ JSON parse error:', parseError)
      const responseText = await response.text()
      return {
        success: false,
        message: 'Invalid response format from server',
        error: `JSON parse failed: ${responseText.substring(0, 200)}...`
      }
    }

    if (!result.success) {
      return {
        success: false,
        message: 'Unable to retrieve user context',
        error: result.error
      }
    }

    if (!result.user_authenticated) {
      return {
        success: true,
        user_authenticated: false,
        user_found: false,
        message: 'User not authenticated via Clerk. They need to sign in first.',
        action_required: 'redirect_to_auth',
        recommendation: 'Ask the user to sign in or sign up using the authentication page.'
      }
    }

    if (!result.user_in_database) {
      return {
        success: true,
        user_authenticated: true,
        user_found: false,
        message: 'User authenticated via Clerk but not found in our database. This appears to be a new user.',
        action_required: 'redirect_to_onboarding',
        recommendation: 'Guide them through the onboarding process to complete their profile setup.'
      }
    }

    // Format user context in a way that's helpful for Mae
    const contextSummary = `
**User Profile:**
- Name: ${result.user.name}
- Email: ${result.user.email}
- Role: ${result.user.role}
- Onboarding completed: ${result.user.onboarding_completed ? 'Yes' : 'No'}
- Member since: ${new Date(result.user.created_at).toLocaleDateString()}

**Family Information:**
- Total children: ${result.family_summary.total_children}
- Recent sessions: ${result.recent_sessions_count || 0}
- Family members: ${result.family_members.map((m: any) => {
  const age = m.date_of_birth ? Math.floor((Date.now() - new Date(m.date_of_birth).getTime()) / (365.25 * 24 * 60 * 60 * 1000)) : 'unknown'
  const medicalInfo = []
  if (m.medical_conditions && m.medical_conditions.length > 0) {
    medicalInfo.push(`Conditions: ${m.medical_conditions.join(', ')}`)
  }
  if (m.allergies && m.allergies.length > 0) {
    medicalInfo.push(`Allergies: ${m.allergies.join(', ')}`)
  }
  const medicalText = medicalInfo.length > 0 ? ` [${medicalInfo.join('; ')}]` : ''
  return `${m.name} (${m.relationship || 'child'}, ${age} years)${medicalText}`
}).join(', ') || 'None added yet'}

**Recent Conversations:**
${result.family_summary.recent_conversations.length > 0
  ? result.family_summary.recent_conversations.map((c: any) => `- ${c.last_topic} (${new Date(c.date).toLocaleDateString()})${c.key_points && c.key_points.length > 0 ? ': ' + c.key_points.join(', ') : ''}`).join('\n')
  : '- No recent conversations found'
}
`

    // Generate personalized conversation starters and context
    const conversationContext = generateConversationContext(result)

    return {
      success: true,
      user_authenticated: true,
      user_found: true,
      user_id: result.user.id, // CRITICAL: Provide the UUID for Mae to use in family member operations
      clerk_id: result.user.clerk_id || result.user.auth_user_id, // Provide Clerk ID for reference
      onboarding_completed: result.user.onboarding_completed,
      context_summary: contextSummary,
      conversation_context: conversationContext,
      user_data: result.user,
      family_data: result.family_members,
      personalization_data: {
        children_by_name: result.family_members.reduce((acc: any, child: any) => {
          acc[child.name] = {
            age: child.date_of_birth ? Math.floor((Date.now() - new Date(child.date_of_birth).getTime()) / (365.25 * 24 * 60 * 60 * 1000)) : null,
            medical_conditions: child.medical_conditions || [],
            allergies: child.allergies || [],
            relationship: child.relationship || 'child'
          }
          return acc
        }, {}),
        recent_topics: result.family_summary.recent_conversations.map((conv: any) => conv.last_topic),
        health_concerns: result.family_members.flatMap((m: any) => [...(m.medical_conditions || []), ...(m.allergies || [])]),
        last_interaction: result.recent_sessions_count > 0 ? result.family_summary.recent_conversations[0]?.date : null
      },
      // Enhanced context data for Mae
      enhanced_context: {
        audio_chat_sessions: result.audio_chat_sessions || [],
        care_log_entries: result.care_log_entries || [],
        my_library: result.my_library || [],
        pre_visit_data: result.pre_visit_data || [],
        user_sessions: result.user_sessions || [],
        context_analytics: result.context_analytics || {}
      },
      message: result.user.onboarding_completed
        ? `MAGICAL PERSONALIZATION ACTIVATED! Welcome back ${result.user.name}! You have rich family context available for deeply personalized conversations. Your user ID is ${result.user.id}.`
        : 'User found but onboarding not completed. Guide them to complete their family profile setup.',
      action_required: result.user.onboarding_completed ? 'provide_magical_personalized_assistance' : 'complete_onboarding'
    }

  } catch (error) {
    console.error('❌ Error in handleGetUserContext:', error)
    return {
      success: false,
      message: 'Error retrieving user context',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

// Helper function to generate magical conversation context
function generateConversationContext(result: any): string {
  const user = result.user
  const familyMembers = result.family_members
  const recentConversations = result.family_summary.recent_conversations

  let context = `\n**MAGICAL PERSONALIZATION CONTEXT FOR ${user.name.toUpperCase()}:**\n\n`

  // Children context with ages and medical info
  if (familyMembers.length > 0) {
    context += `**CHILDREN TO REFERENCE BY NAME:**\n`
    familyMembers.forEach((child: any) => {
      const age = child.date_of_birth ? Math.floor((Date.now() - new Date(child.date_of_birth).getTime()) / (365.25 * 24 * 60 * 60 * 1000)) : 'unknown'
      context += `• ${child.name} (${age} years old, ${child.relationship || 'child'})\n`

      if (child.medical_conditions && child.medical_conditions.length > 0) {
        context += `  - Medical conditions: ${child.medical_conditions.join(', ')}\n`
      }
      if (child.allergies && child.allergies.length > 0) {
        context += `  - Allergies: ${child.allergies.join(', ')}\n`
      }
    })
    context += `\n`
  }

  // Recent conversation context
  if (recentConversations.length > 0) {
    context += `**CONVERSATION HISTORY TO REFERENCE:**\n`
    recentConversations.slice(0, 3).forEach((conv: any, index: number) => {
      const date = new Date(conv.date).toLocaleDateString()
      context += `• ${date}: "${conv.last_topic}"`
      if (conv.key_points && conv.key_points.length > 0) {
        context += ` (Key points: ${conv.key_points.join(', ')})`
      }
      context += `\n`
    })
    context += `\n`
  }

  // Personalization instructions
  context += `**PERSONALIZATION INSTRUCTIONS:**\n`
  context += `• ALWAYS use children's names (${familyMembers.map((c: any) => c.name).join(', ')}) in conversations\n`
  context += `• Reference their ages and developmental stages naturally\n`
  context += `• Show awareness of medical conditions and allergies\n`
  context += `• Build on previous conversations when relevant\n`
  context += `• Make it feel like you genuinely know and care about this family\n`

  if (user.zip) {
    context += `• User location: ${user.zip} (use for local healthcare provider searches)\n`
  }

  return context
}

export async function handleCheckAuthenticationStatus() {
  try {
    console.log('🔐 Mae checking Clerk authentication status')

    // Call the same API endpoint to check authentication status
    const response = await fetch('/api/mae-user-context', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({})
    })

    if (!response.ok) {
      return {
        success: false,
        message: 'Error checking authentication status',
        authenticated: false,
        recommendation: 'Unable to verify authentication. Please try again.'
      }
    }

    const result = await response.json()

    if (!result.user_authenticated) {
      return {
        success: true,
        message: 'User is not currently authenticated via Clerk.',
        authenticated: false,
        recommendation: 'Direct the user to sign in or sign up using the authentication page.'
      }
    }

    if (!result.user_in_database) {
      return {
        success: true,
        message: 'User is authenticated via Clerk but needs to complete onboarding.',
        authenticated: true,
        user_in_database: false,
        recommendation: 'Guide the user through the onboarding process to set up their profile.'
      }
    }

    return {
      success: true,
      message: 'User is fully authenticated and has a complete profile.',
      authenticated: true,
      user_in_database: true,
      onboarding_completed: result.user?.onboarding_completed || false,
      recommendation: 'You can provide personalized assistance using get_user_context.'
    }

  } catch (error) {
    console.error('❌ Error in handleCheckAuthenticationStatus:', error)
    return {
      success: false,
      message: 'Error checking authentication status',
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}