/**
 * Mae Auth UI Function Tools
 * Functions for <PERSON> to display authentication UI elements
 */

import { Type, FunctionDeclaration } from '@google/genai'

// Function declarations for <PERSON> to trigger auth UI
export const maeAuthUIFunctionDeclarations: FunctionDeclaration[] = [
  {
    name: 'show_auth_modal',
    description: 'Display the authentication modal for user signup or login',
    parameters: {
      type: Type.OBJECT,
      properties: {
        mode: {
          type: Type.STRING,
          enum: ['signup', 'login', 'both'],
          description: 'Authentication mode to display'
        },
        message: {
          type: Type.STRING,
          description: 'Optional message to display to the user explaining why auth is needed'
        },
        required_for: {
          type: Type.STRING,
          description: 'What the authentication is required for (e.g., "saving your information", "creating your profile")'
        },
        email: {
          type: Type.STRING,
          description: 'Pre-fill email if known'
        }
      },
      required: ['mode']
    }
  },
  {
    name: 'hide_auth_modal',
    description: 'Hide the authentication modal',
    parameters: {
      type: Type.OBJECT,
      properties: {},
      required: []
    }
  }
]

// Function to handle showing auth modal
export async function handleShowAuthModal(parameters: {
  mode: 'signup' | 'login' | 'both'
  message?: string
  required_for?: string
  email?: string
}) {
  try {
    console.log('🔐 Mae requesting auth modal display:', parameters)
    console.log('🔐 Window available:', typeof window !== 'undefined')
    
    // Dispatch event to show auth modal
    if (typeof window !== 'undefined') {
      const event = new CustomEvent('mae-show-auth', {
        detail: {
          mode: parameters.mode,
          message: parameters.message || 'Please sign up or log in to continue with your personalized experience.',
          required_for: parameters.required_for || 'continuing',
          email: parameters.email
        }
      })
      
      console.log('🔐 Dispatching mae-show-auth event:', event.detail)
      window.dispatchEvent(event)
      
      // Also try a backup method with a slight delay
      setTimeout(() => {
        console.log('🔐 Dispatching backup mae-show-auth event')
        window.dispatchEvent(new CustomEvent('mae-show-auth', {
          detail: {
            mode: parameters.mode,
            message: parameters.message || 'Please sign up or log in to continue with your personalized experience.',
            required_for: parameters.required_for || 'continuing',
            email: parameters.email
          }
        }))
      }, 100)
    } else {
      console.error('🔐 Window not available - cannot dispatch auth modal event')
    }
    
    return {
      success: true,
      message: `Authentication ${parameters.mode} modal displayed`,
      action_taken: 'auth_modal_shown'
    }
  } catch (error) {
    console.error('Error showing auth modal:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to show auth modal'
    }
  }
}

// Function to handle hiding auth modal
export async function handleHideAuthModal() {
  try {
    console.log('🔐 Mae requesting auth modal hide')
    
    // Dispatch event to hide auth modal
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('mae-hide-auth'))
    }
    
    return {
      success: true,
      message: 'Authentication modal hidden',
      action_taken: 'auth_modal_hidden'
    }
  } catch (error) {
    console.error('Error hiding auth modal:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Failed to hide auth modal'
    }
  }
}