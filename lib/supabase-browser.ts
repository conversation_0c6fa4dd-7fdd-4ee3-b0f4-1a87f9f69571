import { createClient } from '@supabase/supabase-js'

// Browser-safe Supabase client configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || `https://${process.env.SUPABASE_PROJECT_ID}.supabase.co`
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseAnonKey) {
  throw new Error('NEXT_PUBLIC_SUPABASE_ANON_KEY is required')
}

// Client for browser usage
export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Browser-safe exports (no server-side dependencies)
export { supabaseUrl, supabaseAnonKey }