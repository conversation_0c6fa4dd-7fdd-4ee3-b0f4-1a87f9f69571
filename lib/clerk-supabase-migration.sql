-- Migration to add Clerk integration to existing Supabase schema
-- This migration adds clerk_id to users table and updates RLS policies

-- Add clerk_id column to users table if it doesn't exist
ALTER TABLE public.users 
ADD COLUMN IF NOT EXISTS clerk_id VARCHAR(255) UNIQUE;

-- Create index for clerk_id for better performance
CREATE INDEX IF NOT EXISTS idx_users_clerk_id ON public.users(clerk_id);

-- Update the existing user with clerk_id if you have it
-- You'll need to replace 'user_xxx' with the actual Clerk user ID
-- UPDATE public.users 
-- SET clerk_id = 'user_xxx' 
-- WHERE email = '<EMAIL>';

-- Drop existing RLS policies that use auth.uid()
DROP POLICY IF EXISTS "Users can view their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can insert their own profile" ON public.users;

-- Create new RLS policies that work with service role for Clerk webhook
-- These will be called from our API with service role key
CREATE POLICY "Service role can manage all users" ON public.users
    FOR ALL 
    TO service_role
    USING (true);

-- Allow authenticated users to read their own data based on clerk_id
-- This will be used when we verify the Clerk session
CREATE POLICY "Users can view their own profile via clerk_id" ON public.users
    FOR SELECT 
    TO authenticated, anon
    USING (true); -- We'll handle auth in the application layer with Clerk

-- Function to upsert user from Clerk webhook
CREATE OR REPLACE FUNCTION upsert_user_from_clerk(
    p_clerk_id VARCHAR(255),
    p_email VARCHAR(255),
    p_name VARCHAR(100),
    p_phone VARCHAR(20) DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
    v_user_id UUID;
BEGIN
    -- Insert or update user based on clerk_id
    INSERT INTO public.users (clerk_id, email, name, phone)
    VALUES (p_clerk_id, p_email, p_name, p_phone)
    ON CONFLICT (clerk_id) 
    DO UPDATE SET 
        email = EXCLUDED.email,
        name = EXCLUDED.name,
        phone = COALESCE(EXCLUDED.phone, users.phone),
        updated_at = NOW()
    RETURNING id INTO v_user_id;
    
    RETURN v_user_id;
END;
$$ LANGUAGE plpgsql;

-- Function to get user by clerk_id
CREATE OR REPLACE FUNCTION get_user_by_clerk_id(p_clerk_id VARCHAR(255))
RETURNS TABLE (
    id UUID,
    clerk_id VARCHAR(255),
    email VARCHAR(255),
    name VARCHAR(100),
    role VARCHAR(50),
    phone VARCHAR(20),
    zip VARCHAR(10),
    date_of_birth DATE,
    emergency_contact JSONB,
    preferences JSONB,
    onboarding_completed BOOLEAN,
    onboarding_step VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.id,
        u.clerk_id,
        u.email,
        u.name,
        u.role,
        u.phone,
        u.zip,
        u.date_of_birth,
        u.emergency_contact,
        u.preferences,
        u.onboarding_completed,
        u.onboarding_step,
        u.created_at,
        u.updated_at
    FROM public.users u
    WHERE u.clerk_id = p_clerk_id;
END;
$$ LANGUAGE plpgsql;

-- Update family_members policies to work with clerk_id
DROP POLICY IF EXISTS "Users can view their own family members" ON public.family_members;
DROP POLICY IF EXISTS "Users can manage their own family members" ON public.family_members;

-- New policy for family members
CREATE POLICY "Service role can manage all family members" ON public.family_members
    FOR ALL 
    TO service_role
    USING (true);

-- Update onboarding_sessions policies
DROP POLICY IF EXISTS "Users can view their own onboarding sessions" ON public.onboarding_sessions;
DROP POLICY IF EXISTS "Users can manage their own onboarding sessions" ON public.onboarding_sessions;

CREATE POLICY "Service role can manage all onboarding sessions" ON public.onboarding_sessions
    FOR ALL 
    TO service_role
    USING (true);

-- Add comment about the migration
COMMENT ON COLUMN public.users.clerk_id IS 'Clerk user ID for authentication integration';