import { createServiceRoleClient } from './clerk-supabase-server'

export interface UserSyncData {
  clerkId: string
  email: string
  fullName?: string
  firstName?: string
  lastName?: string
  phone?: string
  imageUrl?: string
}

export async function syncUserWithSupabase(userData: UserSyncData) {
  const supabase = createServiceRoleClient()
  
  console.log('🔄 Starting user sync:', userData.clerkId, userData.email)

  try {
    // First, check if there's an existing auth.users record with this email
    const { data: authUser, error: authError } = await supabase
      .from('auth.users')
      .select('id, email, raw_user_meta_data')
      .eq('email', userData.email)
      .single()

    let authUserId: string | null = null

    if (!authError && authUser) {
      authUserId = authUser.id
      console.log('📧 Found existing auth.users record:', authUserId)

      // Update the auth.users metadata with clerk_id
      const updatedMetadata = {
        ...authUser.raw_user_meta_data,
        clerk_id: userData.clerkId,
        full_name: userData.fullName,
        avatar_url: userData.imageUrl
      }

      await supabase
        .from('auth.users')
        .update({ raw_user_meta_data: updatedMetadata })
        .eq('id', authUserId)

      console.log('✅ Updated auth.users metadata')
    } else {
      console.log('ℹ️ No existing auth.users record found')
    }

    // Now handle the public.users record
    const { data: existingUser, error: fetchError } = await supabase
      .from('users')
      .select('id, email, clerk_id, auth_user_id')
      .eq('email', userData.email)
      .single()

    if (fetchError && fetchError.code !== 'PGRST116') {
      throw fetchError
    }

    let result
    
    if (existingUser) {
      // User exists in public.users, update it
      const updateData: any = {
        clerk_id: userData.clerkId,
        name: userData.fullName || `${userData.firstName || ''} ${userData.lastName || ''}`.trim() || 'User',
        phone: userData.phone,
        updated_at: new Date().toISOString()
      }

      // Link to auth.users if we found one
      if (authUserId && !existingUser.auth_user_id) {
        updateData.auth_user_id = authUserId
      }

      const { data, error: updateError } = await supabase
        .from('users')
        .update(updateData)
        .eq('id', existingUser.id)
        .select()
        .single()

      if (updateError) throw updateError

      result = {
        status: 'updated',
        message: `Updated existing user ${userData.email}`,
        user: data,
        linkedToAuth: !!authUserId
      }
    } else {
      // Create new user in public.users
      const insertData: any = {
        id: crypto.randomUUID(), // Generate explicit UUID
        clerk_id: userData.clerkId,
        email: userData.email,
        name: userData.fullName || `${userData.firstName || ''} ${userData.lastName || ''}`.trim() || 'User',
        phone: userData.phone,
        role: 'parent',
        preferences: {},
        onboarding_completed: false,
        onboarding_step: 'welcome'
      }

      // Link to auth.users if we found one
      if (authUserId) {
        insertData.auth_user_id = authUserId
      }

      const { data, error: insertError } = await supabase
        .from('users')
        .insert(insertData)
        .select()
        .single()

      if (insertError) throw insertError

      result = {
        status: 'created',
        message: `Created new user: ${userData.email}`,
        user: data,
        linkedToAuth: !!authUserId
      }
    }

    console.log('✅ User sync complete:', result.status)
    return result

  } catch (error) {
    console.error('❌ User sync failed:', error)
    throw error
  }
}

// Helper function to create or get auth.users record
export async function createAuthUserIfNeeded(email: string, password: string = 'temp-password') {
  const supabase = createServiceRoleClient()

  try {
    // Check if auth user exists
    const { data: existingAuth } = await supabase
      .from('auth.users')
      .select('id')
      .eq('email', email)
      .single()

    if (existingAuth) {
      return existingAuth.id
    }

    // Create auth user (this is typically done through Supabase Auth, not directly)
    // For Clerk integration, we'll typically link to existing auth users
    // or let Supabase handle auth user creation through their normal flow
    
    return null
  } catch (error) {
    console.error('Error checking/creating auth user:', error)
    return null
  }
}