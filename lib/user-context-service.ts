/**
 * User Context Service for Personalized Mae Sessions
 * 
 * This service retrieves user and family data from Supabase to provide <PERSON>
 * with personalized context for each authenticated user's conversation.
 */

import { createClient } from '@supabase/supabase-js'

// Types for user context data
export interface UserContextData {
  user: {
    id: string
    auth_user_id: string
    email: string
    name: string
    role: string
    phone?: string
    zip?: string
    preferences?: any
    onboarding_completed?: boolean
    created_at: string
  }
  family_members: FamilyMember[]
  family_summary: {
    total_children: number
    age_ranges: string[]
    primary_concerns: string[]
    recent_conversations: ConversationSummary[]
  }
}

export interface FamilyMember {
  id: string
  name: string
  date_of_birth: string
  age: number
  gender?: string
  relationship: string
  medical_conditions?: string[]
  allergies?: string[]
  medications?: any[]
  additional_notes?: string
  is_primary?: boolean
}

export interface ConversationSummary {
  session_id: string
  last_topic: string
  created_at: string
  key_points: string[]
}

class UserContextService {
  private supabase: any

  constructor() {
    // Initialize Supabase client with service role key for server-side access
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
    const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY
    
    if (!supabaseUrl || !supabaseServiceKey) {
      console.error('❌ Missing Supabase configuration for user context service')
      return
    }

    this.supabase = createClient(supabaseUrl, supabaseServiceKey)
  }

  /**
   * Get comprehensive user context for Mae personalization
   * Now accepts either Clerk user ID or email to find the user
   */
  async getUserContext(clerkUserId: string, email?: string): Promise<UserContextData | null> {
    try {
      console.log('🔍 Fetching user context for Clerk user:', clerkUserId, 'email:', email)

      // First try to get user by clerk_id (updated column name)
      let { data: user, error: userError } = await this.supabase
        .from('users')
        .select('*')
        .eq('clerk_id', clerkUserId)
        .single()

      // If not found by clerk_id, try by email
      if (userError && email) {
        console.log('⚠️ User not found by clerk_id, trying by email:', email)
        const emailResult = await this.supabase
          .from('users')
          .select('*')
          .eq('email', email)
          .single()
        
        if (!emailResult.error) {
          user = emailResult.data
          userError = null
          
          // Update the user with the Clerk ID for future queries
          await this.supabase
            .from('users')
            .update({ clerk_id: clerkUserId })
            .eq('id', user.id)
          
          console.log('✅ Updated user with Clerk ID')
        }
      }

      if (userError) {
        console.error('❌ Error fetching user data:', userError)
        return null
      }

      if (!user) {
        console.log('⚠️ No user found for clerk_id:', clerkUserId)
        return null
      }

      console.log('✅ User found:', user.name)

      // Get family members
      const { data: familyMembers, error: familyError } = await this.supabase
        .from('family_members')
        .select('*')
        .eq('user_id', user.id)
        .order('is_primary', { ascending: false })
        .order('date_of_birth', { ascending: false })

      if (familyError) {
        console.error('❌ Error fetching family members:', familyError)
      }

      // Calculate ages and process family member data
      const processedFamilyMembers: FamilyMember[] = (familyMembers || []).map(member => ({
        ...member,
        age: this.calculateAge(member.date_of_birth),
        medical_conditions: member.medical_conditions || [],
        allergies: member.allergies || [],
        medications: member.medications || []
      }))

      // Get recent conversation summaries
      const { data: recentSessions, error: sessionsError } = await this.supabase
        .from('audio_chat_sessions')
        .select('session_id, history, created_at, metadata')
        .eq('user_email', user.email)
        .order('created_at', { ascending: false })
        .limit(5)

      if (sessionsError) {
        console.error('❌ Error fetching recent sessions:', sessionsError)
      }

      const recentConversations: ConversationSummary[] = (recentSessions || []).map(session => ({
        session_id: session.session_id,
        last_topic: this.extractTopicFromHistory(session.history),
        created_at: session.created_at,
        key_points: this.extractKeyPoints(session.history)
      }))

      // Build family summary
      const familySummary = {
        total_children: processedFamilyMembers.length,
        age_ranges: this.getAgeRanges(processedFamilyMembers),
        primary_concerns: this.extractPrimaryConcerns(processedFamilyMembers),
        recent_conversations: recentConversations
      }

      const userContext: UserContextData = {
        user,
        family_members: processedFamilyMembers,
        family_summary: familySummary
      }

      console.log('✅ User context assembled:', {
        user: user.name,
        familyMembers: processedFamilyMembers.length,
        recentConversations: recentConversations.length
      })

      return userContext

    } catch (error) {
      console.error('❌ Error in getUserContext:', error)
      return null
    }
  }

  /**
   * Save conversation session with user context
   */
  async saveConversationSession(
    authUserId: string, 
    sessionId: string, 
    messages: any[], 
    metadata: any = {}
  ): Promise<boolean> {
    try {
      // Get user email for session linking
      const { data: user } = await this.supabase
        .from('users')
        .select('email')
        .eq('auth_user_id', authUserId)
        .single()

      if (!user) {
        console.error('❌ User not found for conversation save')
        return false
      }

      // Save to audio_chat_sessions table
      const { error } = await this.supabase
        .from('audio_chat_sessions')
        .upsert({
          session_id: sessionId,
          user_email: user.email,
          history: messages,
          metadata: {
            ...metadata,
            auth_user_id: authUserId,
            personalized_session: true,
            saved_at: new Date().toISOString()
          }
        }, {
          onConflict: 'session_id'
        })

      if (error) {
        console.error('❌ Error saving conversation session:', error)
        return false
      }

      console.log('✅ Conversation session saved:', sessionId)
      return true

    } catch (error) {
      console.error('❌ Error in saveConversationSession:', error)
      return false
    }
  }

  /**
   * Get conversation history for session resumption
   */
  async getConversationHistory(authUserId: string, limit: number = 10): Promise<any[]> {
    try {
      const { data: user } = await this.supabase
        .from('users')
        .select('email')
        .eq('auth_user_id', authUserId)
        .single()

      if (!user) return []

      const { data: sessions, error } = await this.supabase
        .from('audio_chat_sessions')
        .select('history, created_at, metadata')
        .eq('user_email', user.email)
        .order('created_at', { ascending: false })
        .limit(limit)

      if (error) {
        console.error('❌ Error fetching conversation history:', error)
        return []
      }

      // Flatten and return conversation messages
      const allMessages = []
      for (const session of sessions || []) {
        if (session.history && Array.isArray(session.history)) {
          allMessages.push(...session.history.map(msg => ({
            ...msg,
            session_created_at: session.created_at
          })))
        }
      }

      return allMessages.slice(0, limit * 2) // Return up to double the session limit in messages

    } catch (error) {
      console.error('❌ Error in getConversationHistory:', error)
      return []
    }
  }

  // Helper methods
  private calculateAge(dateOfBirth: string): number {
    const birth = new Date(dateOfBirth)
    const today = new Date()
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }
    
    return age
  }

  private getAgeRanges(familyMembers: FamilyMember[]): string[] {
    const ranges = []
    const ages = familyMembers.map(m => m.age)
    
    if (ages.some(age => age < 2)) ranges.push('infant')
    if (ages.some(age => age >= 2 && age < 5)) ranges.push('toddler')
    if (ages.some(age => age >= 5 && age < 13)) ranges.push('child')
    if (ages.some(age => age >= 13 && age < 18)) ranges.push('teen')
    if (ages.some(age => age >= 18)) ranges.push('adult')
    
    return ranges
  }

  private extractPrimaryConcerns(familyMembers: FamilyMember[]): string[] {
    const concerns = []
    
    for (const member of familyMembers) {
      if (member.medical_conditions?.length) {
        concerns.push(...member.medical_conditions)
      }
      if (member.allergies?.length) {
        concerns.push(...member.allergies.map(a => `${a} allergy`))
      }
    }
    
    // Remove duplicates and return top concerns
    return [...new Set(concerns)].slice(0, 5)
  }

  private extractTopicFromHistory(history: any[]): string {
    if (!history || !Array.isArray(history) || history.length === 0) {
      return 'General conversation'
    }

    // Get the first user message to determine topic
    const userMessage = history.find(msg => msg.role === 'user')
    if (userMessage?.content) {
      // Extract key topic words
      const content = userMessage.content.toLowerCase()
      if (content.includes('sleep')) return 'Sleep issues'
      if (content.includes('eat') || content.includes('food')) return 'Feeding/nutrition'
      if (content.includes('sick') || content.includes('fever')) return 'Health concerns'
      if (content.includes('behavior') || content.includes('tantrum')) return 'Behavior'
      if (content.includes('development') || content.includes('milestone')) return 'Development'
      if (content.includes('doctor') || content.includes('appointment')) return 'Medical care'
      return 'Parenting advice'
    }

    return 'General conversation'
  }

  private extractKeyPoints(history: any[]): string[] {
    if (!history || !Array.isArray(history)) return []

    const keyPoints = []
    
    // Extract key points from assistant responses
    for (const message of history) {
      if (message.role === 'assistant' && message.content) {
        // Look for key phrases or recommendations
        const content = message.content
        if (content.includes('recommend') || content.includes('suggest')) {
          keyPoints.push('Received recommendations')
        }
        if (content.includes('doctor') || content.includes('pediatrician')) {
          keyPoints.push('Medical consultation discussed')
        }
        if (content.includes('normal') || content.includes('typical')) {
          keyPoints.push('Developmental normalcy addressed')
        }
      }
    }

    return keyPoints.slice(0, 3) // Top 3 key points
  }
}

// Singleton instance
export const userContextService = new UserContextService()

/**
 * Generate personalized system instruction for Mae based on user context
 */
export function generatePersonalizedSystemInstruction(userContext: UserContextData): string {
  const { user, family_members, family_summary } = userContext

  const familyInfo = family_members.map(member => {
    const age = member.age
    const ageDescription = age < 1 ? `${Math.floor(age * 12)} months old` : `${age} years old`
    
    let memberInfo = `${member.name} (${ageDescription}, ${member.relationship})`
    
    if (member.medical_conditions?.length) {
      memberInfo += ` - Medical conditions: ${member.medical_conditions.join(', ')}`
    }
    if (member.allergies?.length) {
      memberInfo += ` - Allergies: ${member.allergies.join(', ')}`
    }
    
    return memberInfo
  }).join('\n')

  const recentTopics = family_summary.recent_conversations
    .map(conv => conv.last_topic)
    .slice(0, 3)
    .join(', ')

  return `
PERSONALIZED CONTEXT FOR ${user.name}:

USER PROFILE:
- Name: ${user.name}
- Role: ${user.role}
- Email: ${user.email}
- Location: ${user.zip || 'Not specified'}
- Member since: ${new Date(user.created_at).toLocaleDateString()}

FAMILY MEMBERS:
${familyInfo || 'No family members added yet'}

FAMILY SUMMARY:
- Total children: ${family_summary.total_children}
- Age ranges: ${family_summary.age_ranges.join(', ')}
- Primary concerns: ${family_summary.primary_concerns.join(', ') || 'None specified'}

RECENT CONVERSATION TOPICS:
${recentTopics || 'First conversation'}

PERSONALIZATION INSTRUCTIONS:
- Address the user by name: ${user.name}
- Reference their children by name when relevant: ${family_members.map(m => m.name).join(', ')}
- Be aware of any medical conditions or allergies mentioned above
- Consider the age ranges when providing advice
- Reference previous conversations when contextually relevant
- Remember that this is a returning user, not a first-time visitor

When providing health advice, always consider the specific ages and any medical conditions of their children. Use their names when appropriate to make the conversation more personal and engaging.
`
}