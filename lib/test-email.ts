// Test utility for the email conversation functionality

export async function testEmailConversation(recipientEmail: string) {
  const testData = {
    recipient_email: recipientEmail,
    user_question: "My 3-year-old has been having trouble sleeping lately. What can I do to help them get better rest?",
    ai_response: `Hello! I understand your concern about your 3-year-old's sleep troubles. This is a common issue many parents face.

Here are some strategies that can help improve your child's sleep:

1. **Establish a consistent bedtime routine**: Create a calming sequence of activities 30-60 minutes before bedtime (bath, story, quiet play).

2. **Check the sleep environment**: Ensure the room is dark, quiet, and at a comfortable temperature (around 65-70°F).

3. **Limit screen time**: Avoid screens at least 1 hour before bedtime as blue light can interfere with melatonin production.

4. **Consider physical activity**: Make sure your child gets enough physical activity during the day, but not too close to bedtime.

5. **Monitor diet**: Avoid caffeine, sugar, and large meals close to bedtime.

If sleep issues persist for more than 2 weeks or if you notice other concerning symptoms, please consult with your pediatrician to rule out any underlying medical conditions.

Remember, every child is different, and it may take some time to find what works best for your little one.`,
    subject: "Sleep Solutions for Your 3-Year-Old - Our Kidz AI Consultation",
    include_context: true,
    conversation_context: "This consultation was part of an initial assessment about childhood sleep patterns and parental concerns about establishing healthy sleep habits.",
    timestamp: new Date().toISOString(),
    session_id: `test_session_${Date.now()}`
  }

  try {
    const response = await fetch('/api/send-conversation', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData),
    })

    const result = await response.json()

    if (!response.ok) {
      throw new Error(result.error || 'Failed to send test email')
    }

    console.log('Test email sent successfully:', result)
    return result

  } catch (error) {
    console.error('Test email failed:', error)
    throw error
  }
}

// Function to validate email functionality
export function validateEmailSetup() {
  const requiredEnvVars = [
    'SMTP_HOST',
    'SMTP_PORT', 
    'SMTP_USER',
    'SMTP_PASS',
    'FROM_EMAIL',
    'FROM_NAME'
  ]

  const missing = requiredEnvVars.filter(envVar => !process.env[envVar])
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`)
  }

  return true
}