// Session persistence utilities for Mae continuity across pages
import { type MaeSessionConfig } from './mae-session-manager'

const SESSION_STORAGE_KEY = 'mae_session_continuity'

export interface SessionContinuityData {
  sessionId?: string
  ephemeralToken?: string
  userEmail?: string
  conversationHistory?: any[]
  sessionStartTime?: number
  lastActivity?: number
}

// Save session data for cross-page continuity
export function saveSessionContinuity(data: SessionContinuityData): void {
  try {
    if (typeof window !== 'undefined') {
      const sessionData = {
        ...data,
        lastActivity: Date.now()
      }
      localStorage.setItem(SESSION_STORAGE_KEY, JSON.stringify(sessionData))
      console.log('💾 Session continuity data saved:', { sessionId: data.sessionId })
    }
  } catch (error) {
    console.warn('Could not save session continuity:', error)
  }
}

// Load session data for continuation
export function loadSessionContinuity(): SessionContinuityData | null {
  try {
    if (typeof window !== 'undefined') {
      const saved = localStorage.getItem(SESSION_STORAGE_KEY)
      if (saved) {
        const data: SessionContinuityData = JSON.parse(saved)
        
        // Check if session is still valid (within 24 hours)
        const maxAge = 24 * 60 * 60 * 1000 // 24 hours
        if (data.lastActivity && (Date.now() - data.lastActivity) > maxAge) {
          console.log('🕒 Session continuity data expired, clearing')
          clearSessionContinuity()
          return null
        }
        
        console.log('📥 Session continuity data loaded:', { sessionId: data.sessionId })
        return data
      }
    }
  } catch (error) {
    console.warn('Could not load session continuity:', error)
  }
  return null
}

// Clear session data
export function clearSessionContinuity(): void {
  try {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(SESSION_STORAGE_KEY)
      console.log('🗑️ Session continuity data cleared')
    }
  } catch (error) {
    console.warn('Could not clear session continuity:', error)
  }
}

// Convert session continuity to Mae session config
export function sessionContinuityToMaeConfig(data: SessionContinuityData): MaeSessionConfig {
  return {
    sessionId: data.sessionId,
    ephemeralToken: data.ephemeralToken,
    userEmail: data.userEmail,
    autoRegister: true
  }
}