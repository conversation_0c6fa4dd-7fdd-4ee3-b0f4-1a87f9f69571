<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>'s AI Onboarding - Our Kidz</title>
    
    <!-- External Libraries -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Theme CSS -->
    <link rel="stylesheet" href="theme_1.css">
    
    <style>
        /* Custom CSS overrides */
        * {
            font-family: 'Poppins', sans-serif !important;
        }
        
        body {
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%) !important;
            height: 100vh !important;
            overflow: hidden !important;
            margin: 0 !important;
            padding: 0 !important;
        }
        
        .mae-gradient {
            background: linear-gradient(135deg, #00bba7 0%, #14b8a6 100%) !important;
        }
        
        .mae-card {
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px) !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            box-shadow: 0 8px 32px rgba(0, 187, 167, 0.1) !important;
        }
        
        .mae-voice-pulse {
            background: radial-gradient(circle, rgba(0, 187, 167, 0.3) 0%, rgba(0, 187, 167, 0) 70%) !important;
            animation: voicePulse 2s ease-in-out infinite !important;
        }
        
        .mae-typing {
            animation: typing 1.4s ease-in-out infinite !important;
        }
        
        .family-card {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        }
        
        .family-card:hover {
            transform: translateY(-4px) scale(1.02) !important;
            box-shadow: 0 12px 40px rgba(0, 187, 167, 0.15) !important;
        }
        
        .insight-item {
            animation: slideInRight 0.6s ease-out forwards !important;
            opacity: 0 !important;
            transform: translateX(20px) !important;
        }
        
        .progress-step {
            transition: all 0.4s ease-out !important;
        }
        
        .progress-step.active {
            transform: scale(1.1) !important;
            background: #00bba7 !important;
            color: white !important;
        }
        
        .progress-step.completed {
            background: #10b981 !important;
            color: white !important;
        }
        
        /* Animations */
        @keyframes voicePulse {
            0%, 100% { transform: scale(1); opacity: 0.7; }
            50% { transform: scale(1.2); opacity: 1; }
        }
        
        @keyframes typing {
            0%, 60% { transform: translateY(0); }
            30% { transform: translateY(-8px); }
        }
        
        @keyframes slideInRight {
            to {
                opacity: 1 !important;
                transform: translateX(0) !important;
            }
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
            40%, 43% { transform: translateY(-8px); }
            70% { transform: translateY(-4px); }
        }
        
        .fade-in-up {
            animation: fadeInUp 0.6s ease-out forwards;
        }
        
        .bounce-in {
            animation: bounce 0.8s ease-out;
        }
        
        .stagger-delay-1 { animation-delay: 0.2s; }
        .stagger-delay-2 { animation-delay: 0.4s; }
        .stagger-delay-3 { animation-delay: 0.6s; }
        .stagger-delay-4 { animation-delay: 0.8s; }
        
        /* Mae Avatar Styling */
        .mae-avatar {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #00bba7, #14b8a6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow: 0 4px 20px rgba(0, 187, 167, 0.3);
        }
        
        .mae-avatar::before {
            content: '';
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            background: inherit;
            filter: blur(20px);
            opacity: 0.6;
            z-index: -1;
        }
        
        /* Voice Recording UI */
        .voice-recording {
            background: rgba(239, 68, 68, 0.1);
            border: 2px solid #ef4444;
            animation: recordingPulse 1s ease-in-out infinite;
        }
        
        @keyframes recordingPulse {
            0%, 100% { border-color: #ef4444; }
            50% { border-color: #dc2626; box-shadow: 0 0 20px rgba(239, 68, 68, 0.3); }
        }
        
        /* Viewport Layout */
        .main-container {
            height: 100vh !important;
            display: flex !important;
            flex-direction: column !important;
            overflow: hidden !important;
        }
        
        .content-grid {
            flex: 1 !important;
            display: grid !important;
            grid-template-columns: 40% 60% !important;
            overflow: hidden !important;
        }
        
        .left-panel {
            padding: 1.5rem !important;
            overflow-y: auto !important;
            max-height: calc(100vh - 180px) !important;
        }
        
        .right-panel {
            padding: 1.5rem !important;
            overflow-y: auto !important;
            max-height: calc(100vh - 180px) !important;
            border-left: 1px solid rgba(229, 231, 235, 0.5) !important;
        }
        
        .bottom-bar {
            height: 80px !important;
            flex-shrink: 0 !important;
            border-top: 1px solid rgba(229, 231, 235, 0.5) !important;
            background: rgba(255, 255, 255, 0.95) !important;
            backdrop-filter: blur(10px) !important;
        }
        
        /* Header adjustments */
        .header-container {
            height: 100px !important;
            flex-shrink: 0 !important;
        }
        
        /* Mobile Responsiveness */
        @media (max-width: 768px) {
            .content-grid { grid-template-columns: 1fr !important; }
            .right-panel { border-left: none !important; border-top: 1px solid rgba(229, 231, 235, 0.5) !important; }
            .left-panel, .right-panel { max-height: calc(50vh - 90px) !important; }
            .mobile-stack { flex-direction: column !important; }
            .mobile-full { width: 100% !important; }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-cyan-50">
    <div class="main-container">
        <!-- Header -->
        <header class="header-container relative overflow-hidden">
            <div class="absolute inset-0 mae-gradient opacity-5"></div>
            <div class="relative h-full flex items-center px-6">
                <div class="flex items-center justify-between w-full">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 mae-gradient rounded-lg flex items-center justify-center">
                            <i data-lucide="heart" class="w-6 h-6 text-white"></i>
                        </div>
                        <span class="text-2xl font-bold text-gray-900">Our Kidz</span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="progress-step completed rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium">
                            <i data-lucide="check" class="w-4 h-4"></i>
                        </div>
                        <div class="progress-step active rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium">2</div>
                        <div class="progress-step bg-gray-200 text-gray-600 rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium">3</div>
                        <div class="progress-step bg-gray-200 text-gray-600 rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium">4</div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content Grid -->
        <div class="content-grid">
            <!-- Left Panel: Mae Control Hub -->
            <div class="left-panel">
                <h2 class="text-xl font-bold text-gray-900 mb-4 flex items-center">
                    <i data-lucide="sparkles" class="w-5 h-5 mr-2 text-teal-600"></i>
                    Mae Control Hub
                </h2>
                <div class="space-y-4">
                <!-- Mae's Introduction Card -->
                <div class="mae-card rounded-2xl p-6 fade-in-up">
                    <div class="flex items-start space-x-4">
                        <div class="mae-avatar flex-shrink-0">
                            <i data-lucide="sparkles" class="w-8 h-8 text-white"></i>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <h3 class="text-lg font-semibold text-gray-900">Mae</h3>
                                <div class="flex space-x-1">
                                    <div class="w-2 h-2 bg-teal-500 rounded-full mae-typing"></div>
                                    <div class="w-2 h-2 bg-teal-500 rounded-full mae-typing" style="animation-delay: 0.2s;"></div>
                                    <div class="w-2 h-2 bg-teal-500 rounded-full mae-typing" style="animation-delay: 0.4s;"></div>
                                </div>
                            </div>
                            <p class="text-gray-600 text-sm leading-relaxed">
                                Hi! I'm Mae, your AI parenting companion. I'll help create a personalized family profile just by talking with you. Tell me about your children - their names, ages, personalities, anything you'd like to share!
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Voice Interaction Panel -->
                <div class="mae-card rounded-2xl p-6 fade-in-up stagger-delay-1">
                    <h4 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                        <i data-lucide="mic" class="w-5 h-5 mr-2 text-teal-600"></i>
                        Talk to Mae
                    </h4>
                    
                    <div class="space-y-4">
                        <!-- Voice Recording Button -->
                        <div class="flex justify-center">
                            <button id="voiceBtn" class="relative w-20 h-20 rounded-full mae-gradient flex items-center justify-center transition-all duration-300 hover:scale-110 focus:outline-none focus:ring-4 focus:ring-teal-200">
                                <i data-lucide="mic" class="w-8 h-8 text-white"></i>
                                <div class="absolute inset-0 rounded-full mae-voice-pulse"></div>
                            </button>
                        </div>
                        
                        <!-- Voice Status -->
                        <div id="voiceStatus" class="text-center">
                            <p class="text-gray-600 text-sm">Tap to start talking</p>
                        </div>
                        
                        <!-- Alternative Text Input -->
                        <div class="border-t pt-4">
                            <div class="relative">
                                <input type="text" placeholder="Or type your message here..." class="w-full px-4 py-3 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-teal-500 focus:border-transparent">
                                <button class="absolute right-2 top-1/2 transform -translate-y-1/2 p-2 text-teal-600 hover:bg-teal-50 rounded-lg transition-colors">
                                    <i data-lucide="send" class="w-4 h-4"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Mae's Conversation Starters -->
                <div class="mae-card rounded-xl p-4 fade-in-up stagger-delay-2">
                    <h4 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                        <i data-lucide="message-circle" class="w-4 h-4 mr-2 text-teal-600"></i>
                        Conversation Starters
                    </h4>
                    <div class="space-y-2">
                        <button class="w-full text-left p-3 bg-gray-50 hover:bg-teal-50 rounded-lg transition-colors text-sm text-gray-700 hover:text-teal-700 conversation-starter">
                            "I have a 3-year-old daughter named Emma"
                        </button>
                        <button class="w-full text-left p-3 bg-gray-50 hover:bg-teal-50 rounded-lg transition-colors text-sm text-gray-700 hover:text-teal-700 conversation-starter">
                            "My son is 6 years old and loves soccer"
                        </button>
                        <button class="w-full text-left p-3 bg-gray-50 hover:bg-teal-50 rounded-lg transition-colors text-sm text-gray-700 hover:text-teal-700 conversation-starter">
                            "Tell me about family routines"
                        </button>
                    </div>
                </div>
            </div>

            </div>
            
            <!-- Right Panel: Mae's Family Profile Display -->
            <div class="right-panel">
                <h2 class="text-xl font-bold text-gray-900 mb-4 flex items-center">
                    <i data-lucide="sparkles" class="w-5 h-5 mr-2 text-teal-600"></i>
                    Mae's Family Profile
                </h2>
                
                <!-- Mae's Analysis Status -->
                <div class="mae-card rounded-xl p-4 mb-4">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-8 h-8 mae-gradient rounded-full flex items-center justify-center">
                            <i data-lucide="brain" class="w-4 h-4 text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900">Mae is building your family profile</h3>
                            <p class="text-sm text-gray-600">As we talk, I'll create and update family member cards</p>
                        </div>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div class="bg-gradient-to-r from-teal-500 to-cyan-500 h-2 rounded-full transition-all duration-700" style="width: 30%"></div>
                    </div>
                    <p class="text-xs text-gray-500 mt-2">Mae is listening and learning about your family...</p>
                </div>
                
                <!-- Family Members Created by Mae -->
                <div id="family-members" class="space-y-3 mb-6">
                    <!-- Empty state - Mae will populate these through conversation -->
                    <div class="mae-card rounded-xl p-6 border-2 border-dashed border-gray-200">
                        <div class="text-center text-gray-500">
                            <i data-lucide="users" class="w-8 h-8 mx-auto mb-3 text-gray-300"></i>
                            <p class="text-sm font-medium">Family member cards will appear here</p>
                            <p class="text-xs mt-1">Start talking to Mae about your children</p>
                        </div>
                    </div>
                </div>
                
                <!-- Mae's Insights Panel -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                        <i data-lucide="lightbulb" class="w-4 h-4 mr-2 text-teal-600"></i>
                        Mae's Insights
                    </h3>
                    
                    <div id="insights-container" class="space-y-3">
                        <!-- Insights will be dynamically added by Mae -->
                        <div class="mae-card rounded-xl p-4 border-2 border-dashed border-gray-200">
                            <div class="text-center text-gray-500">
                                <i data-lucide="brain" class="w-6 h-6 mx-auto mb-2 text-gray-300"></i>
                                <p class="text-sm font-medium">Mae's insights will appear here</p>
                                <p class="text-xs mt-1">Based on our conversation</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Insights Bar -->
        <div class="bottom-bar flex items-center px-6">
            <div class="flex items-center justify-between w-full">
                <div class="flex items-center space-x-4">
                    <button class="flex items-center space-x-2 text-gray-600 hover:text-gray-900 transition-colors">
                        <i data-lucide="arrow-left" class="w-4 h-4"></i>
                        <span class="text-sm">Previous</span>
                    </button>
                    <div class="text-sm text-gray-500">
                        Step 2 of 4 • Mae's Family Interview
                    </div>
                </div>
                
                <div class="flex items-center space-x-3">
                    <div class="flex items-center space-x-2 text-sm text-gray-600">
                        <i data-lucide="ear" class="w-4 h-4 text-teal-600"></i>
                        <span>Mae is listening and learning about your family</span>
                    </div>
                    <button class="px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors text-sm">
                        Save & Exit
                    </button>
                    <button class="px-4 py-2 mae-gradient text-white font-medium rounded-lg hover:opacity-90 transition-opacity text-sm">
                        Continue
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Initialize Lucide icons
        lucide.createIcons();
        
        // Voice interaction
        let isRecording = false;
        const voiceBtn = document.getElementById('voiceBtn');
        const voiceStatus = document.getElementById('voiceStatus');
        
        voiceBtn.addEventListener('click', function() {
            if (!isRecording) {
                startRecording();
            } else {
                stopRecording();
            }
        });
        
        function startRecording() {
            isRecording = true;
            voiceBtn.classList.add('voice-recording');
            voiceBtn.innerHTML = '<i data-lucide="square" class="w-8 h-8 text-white"></i>';
            voiceStatus.innerHTML = '<p class="text-red-600 text-sm font-medium">🔴 Recording... Tap to stop</p>';
            lucide.createIcons();
            
            // Simulate recording feedback
            setTimeout(() => {
                if (isRecording) {
                    voiceStatus.innerHTML = '<p class="text-red-600 text-sm font-medium">🔴 "Tell me about Emma\'s bedtime routine..."</p>';
                }
            }, 2000);
        }
        
        function stopRecording() {
            isRecording = false;
            voiceBtn.classList.remove('voice-recording');
            voiceBtn.innerHTML = '<i data-lucide="mic" class="w-8 h-8 text-white"></i>';
            voiceStatus.innerHTML = '<p class="text-teal-600 text-sm font-medium">Processing... Mae is thinking!</p>';
            lucide.createIcons();
            
            // Simulate Mae's response
            setTimeout(() => {
                voiceStatus.innerHTML = '<p class="text-gray-600 text-sm">Great! I\'ve added bedtime details for Emma. Tap to record again.</p>';
                
                // Trigger Mae's typing animation
                const maeCard = document.querySelector('.mae-card');
                maeCard.classList.add('bounce-in');
                
                // Mae will handle insights through conversation
                setTimeout(() => {
                    // Simulate Mae's learning process
                    const statusText = document.querySelector('.mae-card p');
                    statusText.textContent = 'Mae is processing your voice input...';
                }, 1000);
            }, 2000);
        }
        
        function createFamilyMemberCard() {
            const familyMembersContainer = document.getElementById('family-members');
            
            // Replace empty state with actual family member
            familyMembersContainer.innerHTML = `
                <div class="family-card mae-card rounded-xl p-4" style="opacity: 0; transform: translateY(20px);">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-10 h-10 bg-pink-100 rounded-full flex items-center justify-center">
                            <i data-lucide="baby" class="w-5 h-5 text-pink-600"></i>
                        </div>
                        <div>
                            <h3 class="font-medium text-gray-900">Emma</h3>
                            <p class="text-sm text-gray-600">3 years old</p>
                        </div>
                        <div class="ml-auto">
                            <div class="w-6 h-6 bg-teal-100 rounded-full flex items-center justify-center">
                                <i data-lucide="check" class="w-3 h-3 text-teal-600"></i>
                            </div>
                        </div>
                    </div>
                    <div class="text-xs text-gray-500">
                        <i data-lucide="sparkles" class="w-3 h-3 inline mr-1 text-teal-500"></i>
                        Created by Mae from our conversation
                    </div>
                </div>
            `;
            
            lucide.createIcons();
            
            // Animate in
            setTimeout(() => {
                const card = familyMembersContainer.querySelector('.family-card');
                card.style.opacity = '1';
                card.style.transform = 'translateY(0)';
                card.style.transition = 'all 0.6s ease-out';
            }, 100);
        }
        
        function addMaeInsight() {
            const insightsContainer = document.getElementById('insights-container');
            
            // Replace empty state with actual insight
            insightsContainer.innerHTML = `
                <div class="mae-card rounded-xl p-4" style="opacity: 0; transform: translateX(20px);">
                    <div class="flex items-start space-x-3">
                        <div class="w-6 h-6 bg-teal-100 rounded-lg flex items-center justify-center flex-shrink-0">
                            <i data-lucide="target" class="w-3 h-3 text-teal-600"></i>
                        </div>
                        <div>
                            <h4 class="font-medium text-gray-900 mb-1">Perfect Age for Development</h4>
                            <p class="text-sm text-gray-600">3-year-olds are at an ideal stage for creative exploration and structured activities.</p>
                        </div>
                    </div>
                </div>
            `;
            
            lucide.createIcons();
            
            // Animate in
            setTimeout(() => {
                const insight = insightsContainer.querySelector('.mae-card');
                insight.style.opacity = '1';
                insight.style.transform = 'translateX(0)';
                insight.style.transition = 'all 0.6s ease-out';
            }, 100);
        }
        
        function updateMaeProgress() {
            const progressBar = document.querySelector('.bg-gradient-to-r.from-teal-500');
            progressBar.style.width = '60%';
            
            const statusText = document.querySelector('.mae-card p');
            statusText.textContent = 'Mae is learning more about Emma...';
        }
        
        // Stagger animation triggers
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        }, observerOptions);
        
        // Observe all fade-in elements
        document.querySelectorAll('.fade-in-up').forEach(el => {
            el.style.opacity = '0';
            el.style.transform = 'translateY(20px)';
            observer.observe(el);
        });
        
        // Insight items animation delay
        setTimeout(() => {
            document.querySelectorAll('.insight-item').forEach((item, index) => {
                setTimeout(() => {
                    item.style.opacity = '1';
                    item.style.transform = 'translateX(0)';
                }, index * 200);
            });
        }, 1000);
        
        // Remove family card interactions since they're now Mae-controlled
        // Cards will be created dynamically by Mae
        
        // Conversation starter buttons interaction
        document.querySelectorAll('.conversation-starter').forEach(btn => {
            btn.addEventListener('click', function() {
                // Simulate Mae processing the conversation starter
                const maeTyping = document.querySelector('.mae-typing').parentElement;
                maeTyping.style.display = 'flex';
                
                // Show the statement in voice status
                const voiceStatus = document.getElementById('voiceStatus');
                voiceStatus.innerHTML = `<p class="text-teal-600 text-sm font-medium">Mae heard: "${this.textContent.replace(/"/g, '')}"</p>`;
                
                setTimeout(() => {
                    // Mae creates a family member card
                    createFamilyMemberCard();
                    
                    // Mae adds an insight
                    addMaeInsight();
                    
                    // Update progress
                    updateMaeProgress();
                    
                    // Hide typing indicator
                    setTimeout(() => {
                        maeTyping.style.display = 'none';
                        voiceStatus.innerHTML = '<p class="text-gray-600 text-sm">Great! I\'ve added that to your family profile. Tell me more!</p>';
                    }, 1000);
                }, 2000);
            });
        });
        
        // Progress updates
        function updateProgress() {
            const progressBar = document.querySelector('.bg-gradient-to-r.from-teal-500');
            const currentWidth = parseInt(progressBar.style.width) || 75;
            const newWidth = Math.min(currentWidth + 5, 100);
            
            progressBar.style.width = newWidth + '%';
            
            const progressText = document.querySelector('.flex.justify-between span:last-child');
            progressText.textContent = newWidth + '%';
        }
        
        // Auto-update progress on interactions
        let interactionCount = 0;
        document.addEventListener('click', function(e) {
            if (e.target.closest('button') && interactionCount < 5) {
                interactionCount++;
                setTimeout(updateProgress, 500);
            }
        });
        
        // Mobile responsive adjustments
        function handleMobileLayout() {
            if (window.innerWidth < 768) {
                document.body.classList.add('mobile-layout');
            } else {
                document.body.classList.remove('mobile-layout');
            }
        }
        
        window.addEventListener('resize', handleMobileLayout);
        handleMobileLayout();
        
        // Smooth scrolling for mobile
        if (window.innerWidth < 768) {
            document.documentElement.style.scrollBehavior = 'smooth';
        }
    </script>
</body>
</html>