/* <PERSON> AI Chat Theme - Our Kidz Brand */
:root {
  /* Our Kidz Brand Colors */
  --primary: #00bba7; /* Our Kidz Teal */
  --primary-light: #33c9b3;
  --primary-dark: #008c7a;
  --primary-foreground: #ffffff;
  
  /* Warm Family-Friendly Palette */
  --background: #fefefe;
  --foreground: #2a2a2a;
  --card: #ffffff;
  --card-foreground: #2a2a2a;
  
  /* <PERSON>'s AI Persona Colors */
  --mae-gradient: linear-gradient(135deg, #00bba7 0%, #33c9b3 50%, #66d5c4 100%);
  --mae-message-bg: #f0fffe;
  --mae-message-border: #e0f9f7;
  
  /* User Message Colors */
  --user-message-bg: #f8f9fa;
  --user-message-border: #e9ecef;
  --user-accent: #6c757d;
  
  /* Semantic Colors */
  --success: #28a745;
  --warning: #ffc107;
  --error: #dc3545;
  --info: #17a2b8;
  
  /* Neutral Grays */
  --muted: #f8f9fa;
  --muted-foreground: #6c757d;
  --border: #e9ecef;
  --input: #ffffff;
  --input-border: #ced4da;
  --ring: #00bba7;
  
  /* Typography - Poppins Font Family */
  --font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-sans: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: 'JetBrains Mono', 'Fira Code', monospace;
  
  /* Font Weights */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  
  /* Spacing System - Organic, flowing spacing */
  --space-xs: 0.375rem;   /* 6px */
  --space-sm: 0.5rem;     /* 8px */
  --space-md: 0.75rem;    /* 12px */
  --space-lg: 1rem;       /* 16px */
  --space-xl: 1.5rem;     /* 24px */
  --space-2xl: 2rem;      /* 32px */
  --space-3xl: 3rem;      /* 48px */
  
  /* Border Radius - Soft, friendly curves */
  --radius-sm: 0.5rem;    /* 8px */
  --radius-md: 0.75rem;   /* 12px */
  --radius-lg: 1rem;      /* 16px */
  --radius-xl: 1.5rem;    /* 24px */
  --radius-2xl: 2rem;     /* 32px */
  --radius-full: 9999px;  /* Full circle */
  
  /* Shadows - Soft, organic depth */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Mae-specific Shadows with Teal Glow */
  --mae-glow: 0 0 20px rgba(0, 187, 167, 0.15);
  --mae-glow-strong: 0 0 30px rgba(0, 187, 167, 0.25);
  
  /* Transitions - Smooth, natural timing */
  --transition-fast: 150ms ease-out;
  --transition-normal: 250ms ease-out;
  --transition-slow: 350ms ease-out;
  --transition-bounce: 600ms cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* Z-index layers */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  
  /* Chat-specific Variables */
  --chat-header-height: 4rem;
  --chat-input-height: 3.5rem;
  --message-max-width: 75%;
  --sidebar-width: 280px;
  
  /* Mae Avatar Gradient */
  --mae-avatar: linear-gradient(135deg, #00bba7 0%, #33c9b3 50%, #4dd0c7 100%);
  
  /* Typing Indicator */
  --typing-dot: #00bba7;
  --typing-bg: #f0fffe;
}

/* Dark mode support for future */
@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f1419;
    --foreground: #e6e6e6;
    --card: #1a1f2e;
    --card-foreground: #e6e6e6;
    
    --mae-message-bg: rgba(0, 187, 167, 0.1);
    --mae-message-border: rgba(0, 187, 167, 0.2);
    
    --user-message-bg: #2a2f3e;
    --user-message-border: #3a3f4e;
    
    --muted: #1a1f2e;
    --muted-foreground: #9ca3af;
    --border: #374151;
    --input: #1a1f2e;
    --input-border: #374151;
  }
}

/* Font Import */
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

/* Base Typography Styles */
body {
  font-family: var(--font-family) !important;
  font-weight: var(--font-normal) !important;
  line-height: 1.6 !important;
  color: var(--foreground) !important;
  background-color: var(--background) !important;
}

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-family) !important;
  font-weight: var(--font-semibold) !important;
  line-height: 1.3 !important;
  color: var(--foreground) !important;
}

/* Utility Classes */
.mae-gradient {
  background: var(--mae-gradient);
}

.mae-glow {
  box-shadow: var(--mae-glow);
}

.mae-glow-strong {
  box-shadow: var(--mae-glow-strong);
}

.transition-smooth {
  transition: all var(--transition-normal);
}

.transition-bounce {
  transition: all var(--transition-bounce);
}