import { clerkMiddleware, createRouteMatcher } from '@clerk/nextjs/server';
import { NextResponse } from 'next/server';

// Define public routes that don't require authentication
const isPublicRoute = createRouteMatcher([
  '/',
  '/auth(.*)',
  '/sign-in(.*)',
  '/sign-up(.*)',
  '/demo(.*)',
  '/test-clerk',
  '/clerk-test',
  '/api/gemini-proxy(.*)',
  '/api/maps-config(.*)',
  '/api/copilotkit(.*)',
  '/api/webhooks(.*)',
  '/terms',
  '/privacy',
  '/[transport](.*)',
  '/.well-known/oauth-authorization-server(.*)',
  '/.well-known/oauth-protected-resource(.*)',
]);

export default clerkMiddleware(async (auth, req) => {
  const url = req.nextUrl
  const hostname = url.hostname

  // Special handling for test pages
  if (url.pathname === '/test-clerk' || url.pathname === '/clerk-test') {
    // Allow these pages to load without forcing authentication
    return NextResponse.next()
  }

  // Redirect legacy top-level auth routes to our dedicated auth pages
  if (url.pathname === '/sign-in' || url.pathname === '/sign-up') {
    url.pathname = '/auth'
    return NextResponse.redirect(url)
  }

  // If user is not authenticated and lands on root, redirect to /auth
  if (url.pathname === '/') {
    const { userId } = await auth()
    if (!userId) {
      url.pathname = '/auth'
      return NextResponse.redirect(url)
    }
  }

  // If the route is not public, protect it
  if (!isPublicRoute(req)) {
    const path = url.pathname
    const isApiRoute = path.startsWith('/api') || path.startsWith('/trpc') || path.startsWith('/[transport]')
    
    try {
      if (isApiRoute) {
        await auth.protect()
      } else {
        await auth.protect({ unauthenticatedUrl: '/auth' })
      }
    } catch (error) {
      // Handle authentication errors gracefully
      console.error('Auth protection error:', error)
      if (!isApiRoute) {
        url.pathname = '/auth'
        return NextResponse.redirect(url)
      }
    }
  }
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    '/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest|mp4|webm|mp3|m4a|wav|ogg|avi|mov|mkv|pdf)).*)',
    // Always run for API routes
    '/(api|trpc)(.*)',
    '/[transport](.*)',
    '/.well-known/oauth-authorization-server(.*)',
    '/.well-known/oauth-protected-resource(.*)',
  ],
};