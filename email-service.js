const nodemailer = require('nodemailer');
const axios = require('axios');
require('dotenv').config({ path: '.env.local' });

/**
 * Email Service Module
 * Handles SMTP email sending and webhook logging to n8n
 */

class EmailService {
  constructor() {
    // Initialize SMTP transporter with environment variables
    this.transporter = nodemailer.createTransporter({
      host: process.env.SMTP_HOST,
      port: parseInt(process.env.SMTP_PORT),
      secure: process.env.SMTP_SECURE === 'true', // true for 465, false for other ports
      auth: {
        user: process.env.SMTP_USER,
        pass: process.env.SMTP_PASS,
      },
    });

    // Webhook URL for n8n logging
    this.webhookUrl = 'https://my-n8n-instance.com/webhook/email-log';
  }

  /**
   * Send email via SMTP and log to webhook
   * @param {string} recipientEmail - Email address of the recipient
   * @param {string} subject - Email subject line
   * @param {string} textContent - Plain text email content
   * @param {string} htmlContent - HTML email content (optional)
   * @returns {Promise<Object>} - Result object with success status and details
   */
  async sendEmailWithLogging(recipientEmail, subject, textContent, htmlContent = null) {
    try {
      // Step 1: Prepare email options
      const mailOptions = {
        from: `${process.env.FROM_NAME} <${process.env.FROM_EMAIL}>`,
        to: recipientEmail,
        subject: subject,
        text: textContent,
      };

      // Add HTML content if provided
      if (htmlContent) {
        mailOptions.html = htmlContent;
      }

      console.log(`📧 Attempting to send email to: ${recipientEmail}`);

      // Step 2: Send the email via SMTP
      const emailInfo = await this.transporter.sendMail(mailOptions);
      console.log('✅ Email sent successfully:', emailInfo.response);

      // Step 3: Log the successful email send to n8n webhook
      const sentDate = new Date().toISOString();
      await this.logEmailToWebhook(recipientEmail, sentDate);

      return {
        success: true,
        messageId: emailInfo.messageId,
        response: emailInfo.response,
        sentDate: sentDate,
      };

    } catch (error) {
      console.error('❌ Email sending failed:', error.message);
      
      // Still attempt to log the failure to webhook (optional)
      try {
        await this.logEmailToWebhook(recipientEmail, new Date().toISOString(), false, error.message);
      } catch (webhookError) {
        console.error('❌ Failed to log email failure to webhook:', webhookError.message);
      }

      return {
        success: false,
        error: error.message,
      };
    }
  }

  /**
   * Log email activity to n8n webhook
   * @param {string} email - Recipient email address
   * @param {string} date - ISO-8601 date string
   * @param {boolean} success - Whether email was sent successfully
   * @param {string} errorMessage - Error message if failed
   */
  async logEmailToWebhook(email, date, success = true, errorMessage = null) {
    try {
      // Prepare webhook payload
      const payload = {
        email: email,
        date: date,
        success: success,
      };

      // Add error message if email failed
      if (!success && errorMessage) {
        payload.error = errorMessage;
      }

      console.log('📡 Sending webhook to n8n...');

      // Send POST request to n8n webhook
      const response = await axios.post(this.webhookUrl, payload, {
        headers: {
          'Content-Type': 'application/json',
        },
        timeout: 10000, // 10 second timeout
      });

      console.log('✅ Webhook sent successfully to n8n:', response.status);
      return response.data;

    } catch (error) {
      console.error('❌ Failed to send webhook to n8n:', error.message);
      throw error;
    }
  }

  /**
   * Verify SMTP connection
   * @returns {Promise<boolean>} - True if connection is successful
   */
  async verifyConnection() {
    try {
      await this.transporter.verify();
      console.log('✅ SMTP connection verified successfully');
      return true;
    } catch (error) {
      console.error('❌ SMTP connection failed:', error.message);
      return false;
    }
  }
}

// Export the EmailService class
module.exports = EmailService;

// Example usage (uncomment to test)
/*
async function testEmailService() {
  const emailService = new EmailService();
  
  // Verify SMTP connection first
  const isConnected = await emailService.verifyConnection();
  if (!isConnected) {
    console.error('Cannot proceed - SMTP connection failed');
    return;
  }

  // Send a test email
  const result = await emailService.sendEmailWithLogging(
    '<EMAIL>',
    'Test Email from Our Kidz',
    'Hello! This is a test email sent from Node.js using the EmailService.',
    '<h1>Hello!</h1><p>This is a test email sent from Node.js using the EmailService.</p>'
  );

  console.log('Email service result:', result);
}

// Uncomment the line below to run the test
// testEmailService();
*/
