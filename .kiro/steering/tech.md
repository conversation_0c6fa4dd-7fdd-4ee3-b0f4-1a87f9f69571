# Technology Stack

## Framework & Core
- **Next.js 15** with App Router
- **React 18** with TypeScript
- **Node.js 22** runtime

## Styling & UI
- **Tailwind CSS** with custom healthcare teal theme (`#00bba7`)
- **Shadcn/ui** component library with Radix UI primitives
- **Poppins** font family from Google Fonts
- **Lucide React** for icons
- **Framer Motion** for animations

## Authentication & Database
- **Clerk** for user authentication and management
- **Supabase** for PostgreSQL database and real-time features
- Custom user sync between Clerk and Supabase

## AI & Voice Integration
- **Google Gemini API** for AI conversations and Mae agent
- **Anthropic Claude** SDK for additional AI capabilities
- **CopilotKit** for AI-powered UI components
- **Microphone Stream** for voice input handling

## External Services
- **Google Maps API** for location services and healthcare provider mapping
- **Nodemailer** with SMTP for email functionality
- **Svix** for webhook handling

## Development Tools
- **ESLint** with Next.js config
- **TypeScript** with strict mode
- **PostCSS** and **Autoprefixer**

## Common Commands

```bash
# Development
npm run dev          # Start development server on localhost:3000
npm run build        # Build production bundle
npm run start        # Start production server
npm run lint         # Run ESLint

# Database
# Supabase migrations are handled through the web interface
# Database schema files are in /lib/supabase-schema.sql and /migrations/
```

## Build Configuration
- ESLint and TypeScript errors ignored during builds for rapid development
- Image optimization disabled for static export compatibility
- Remote image patterns configured for Unsplash
- Path aliases: `@/*` maps to project root