# Project Structure

## Root Directory Organization

```
├── app/                    # Next.js App Router pages and API routes
├── components/             # Reusable React components
├── lib/                    # Utility functions and service integrations
├── hooks/                  # Custom React hooks
├── docs/                   # Project documentation
├── migrations/             # Database migration files
├── public/                 # Static assets (images, videos, legal docs)
├── test/                   # Test files and testing documentation
└── examples/               # Usage examples for Mae agent features
```

## App Directory Structure

- **`app/page.tsx`** - Landing page with hero, features, and CTA sections
- **`app/layout.tsx`** - Root layout with providers (Clerk, Theme, Mae)
- **`app/api/`** - API routes organized by feature:
  - `mae/` - Mae agent endpoints (family, user management)
  - `auth-sync/` - Clerk-Supabase user synchronization
  - `onboarding/` - Voice-driven onboarding flow
  - `email-interactions/` - Email functionality
  - `webhooks/` - External service webhooks

## Component Organization

- **`components/ui/`** - Shadcn/ui base components
- **`components/auth/`** - Authentication-related components
- **Feature components** - Named by functionality (e.g., `mae-auth-modal.tsx`)
- **Global components** - Prefixed with `global-` for app-wide functionality

## Library Structure

- **`lib/mae-*.ts`** - Mae agent functionality (auth, family, database tools)
- **`lib/supabase-*.ts`** - Database client and schema definitions
- **`lib/clerk-*.ts`** - Authentication integration
- **`lib/*-function-tools.ts`** - AI function calling implementations
- **`lib/utils.ts`** - General utility functions

## Key Conventions

### File Naming
- **Components**: kebab-case (e.g., `mae-auth-modal.tsx`)
- **Pages**: kebab-case directories with `page.tsx`
- **API routes**: kebab-case directories with `route.ts`
- **Libraries**: kebab-case with descriptive prefixes

### Component Patterns
- Use `"use client"` directive for client-side components
- Export default for main component, named exports for utilities
- Props interfaces defined inline or as separate types
- Consistent use of React.FC or function declarations

### API Route Structure
- Each route in its own directory with `route.ts`
- Consistent error handling and response formatting
- Authentication checks using Clerk middleware
- Database operations through Supabase client

### Environment Configuration
- Development: `.env.development.local`
- Production: `.env.production`
- Examples: `.env.example`
- Feature flags: `NEXT_PUBLIC_*` prefix for client-side access