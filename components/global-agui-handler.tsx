"use client"

import React, { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { 
  aguiEventListener, 
  AG_UI_EVENTS, 
  type FillUserFormPayload,
  type SubmitUserFormPayload,
  type AddFamilyMemberPayload,
  type UpdateFamilyMemberPayload,
  type ValidateFormPayload,
  type OnboardingProgressPayload,
  type EventResponse
} from '@/lib/ag-ui-events'

/**
 * Global AG-UI Event Handler
 * 
 * This component provides global AG-UI event handling for <PERSON>'s function tools
 * to work from any page, not just the onboarding page. It handles events by
 * either processing them directly (for navigation) or redirecting to the 
 * appropriate page where they can be handled properly.
 */
export function GlobalAGUIHandler() {
  const router = useRouter()

  useEffect(() => {
    // Store user data temporarily for cross-page navigation
    let tempUserData: any = {}
    let tempFamilyMembers: any[] = []

    // Check if we're on the onboarding page
    const isOnOnboardingPage = () => {
      return typeof window !== 'undefined' && window.location.pathname === '/onboarding'
    }

    // Handle form filling - store data and navigate to onboarding if needed
    const handleFillUserForm = async (payload: FillUserFormPayload, requestId: string): Promise<EventResponse> => {
      console.log('🌐 Global AG-UI: Handling fillUserForm', payload)

      // Validate payload structure
      if (!payload || typeof payload !== 'object') {
        console.error('🌐 Global AG-UI: Invalid payload received:', payload)
        return {
          success: false,
          error: 'Invalid payload: payload is null or not an object'
        }
      }

      if (!payload.field) {
        console.error('🌐 Global AG-UI: Missing field in payload:', payload)
        return {
          success: false,
          error: 'Invalid payload: field is required'
        }
      }

      if (payload.value === undefined || payload.value === null) {
        console.error('🌐 Global AG-UI: Missing value in payload:', payload)
        return {
          success: false,
          error: 'Invalid payload: value is required'
        }
      }

      // Store the data temporarily regardless of page
      tempUserData[payload.field] = payload.value

      // If we're on the onboarding page, just store data and let onboarding interface handle the UI
      if (isOnOnboardingPage()) {
        console.log('🌐 On onboarding page - storing data for onboarding interface')
        sessionStorage.setItem('mae-temp-user-data', JSON.stringify(tempUserData))

        // Return success but indicate it should be handled by onboarding interface
        return {
          success: true,
          message: `Data stored for onboarding interface to handle ${payload.field}`,
          data: { field: payload.field, value: payload.value, deferred: true }
        }
      }

      console.log('🌐 Navigating to onboarding page to handle form filling')

      // Store data in sessionStorage for the onboarding page to pick up
      sessionStorage.setItem('mae-temp-user-data', JSON.stringify(tempUserData))
      sessionStorage.setItem('mae-temp-family-members', JSON.stringify(tempFamilyMembers))
      sessionStorage.setItem('mae-continue-session', 'true')

      // Navigate to onboarding page
      router.push('/onboarding')

      return {
        success: true,
        message: `Navigating to onboarding page to fill ${payload.field}`,
        data: { field: payload.field, value: payload.value, navigated: true }
      }
    }

    // Handle form submission - navigate to onboarding if needed
    const handleSubmitUserForm = async (payload: SubmitUserFormPayload, requestId: string): Promise<EventResponse> => {
      console.log('🌐 Global AG-UI: Handling submitUserForm', payload)

      // If we're on the onboarding page, let onboarding interface handle it
      if (isOnOnboardingPage()) {
        console.log('🌐 On onboarding page - deferring to onboarding interface')
        sessionStorage.setItem('mae-should-submit', 'true')

        return {
          success: true,
          message: 'Deferred to onboarding interface for form submission',
          data: { deferred: true }
        }
      }

      console.log('🌐 Navigating to onboarding page to handle form submission')

      // Store data in sessionStorage
      sessionStorage.setItem('mae-temp-user-data', JSON.stringify(tempUserData))
      sessionStorage.setItem('mae-temp-family-members', JSON.stringify(tempFamilyMembers))
      sessionStorage.setItem('mae-should-submit', 'true')
      sessionStorage.setItem('mae-continue-session', 'true')

      // Navigate to onboarding page
      router.push('/onboarding')

      return {
        success: true,
        message: 'Navigating to onboarding page to submit form',
        data: { navigated: true }
      }
    }

    // Handle adding family member - store data and navigate if needed
    const handleAddFamilyMember = async (payload: AddFamilyMemberPayload, requestId: string): Promise<EventResponse> => {
      console.log('🌐 Global AG-UI: Handling addFamilyMember', payload)

      // Validate payload structure
      if (!payload || typeof payload !== 'object') {
        console.error('🌐 Global AG-UI: Invalid payload received:', payload)
        return {
          success: false,
          error: 'Invalid payload: payload is null or not an object'
        }
      }

      if (!payload.name) {
        console.error('🌐 Global AG-UI: Missing name in payload:', payload)
        return {
          success: false,
          error: 'Invalid payload: name is required'
        }
      }

      if (!payload.date_of_birth) {
        console.error('🌐 Global AG-UI: Missing date_of_birth in payload:', payload)
        return {
          success: false,
          error: 'Invalid payload: date_of_birth is required'
        }
      }

      if (!payload.relationship) {
        console.error('🌐 Global AG-UI: Missing relationship in payload:', payload)
        return {
          success: false,
          error: 'Invalid payload: relationship is required'
        }
      }

      // Store the family member data regardless of page
      tempFamilyMembers.push(payload)

      // If we're on the onboarding page, let onboarding interface handle it
      if (isOnOnboardingPage()) {
        console.log('🌐 On onboarding page - storing data for onboarding interface')
        sessionStorage.setItem('mae-temp-family-members', JSON.stringify(tempFamilyMembers))

        return {
          success: true,
          message: `Data stored for onboarding interface to add family member ${payload.name}`,
          data: { family_member: payload, deferred: true }
        }
      }

      console.log('🌐 Navigating to onboarding page to handle family member addition')

      // Store data in sessionStorage
      sessionStorage.setItem('mae-temp-user-data', JSON.stringify(tempUserData))
      sessionStorage.setItem('mae-temp-family-members', JSON.stringify(tempFamilyMembers))
      sessionStorage.setItem('mae-continue-session', 'true')

      // Navigate to onboarding page
      router.push('/onboarding')

      return {
        success: true,
        message: `Navigating to onboarding page to add family member ${payload.name}`,
        data: { family_member: payload, navigated: true }
      }
    }

    // Handle validation - can work globally by making API calls
    const handleValidateForm = async (payload: ValidateFormPayload, requestId: string): Promise<EventResponse> => {
      console.log('🌐 Global AG-UI: Handling validateForm', payload)
      
      // Check if payload is valid
      if (!payload || typeof payload !== 'object') {
        console.warn('🌐 Global AG-UI: Invalid payload for validateForm:', payload)
        return {
          success: true,
          message: 'Validation skipped - invalid payload',
          data: { formType: 'unknown' }
        }
      }
      
      // For now, return success - validation will happen on the server
      return {
        success: true,
        message: 'Validation will be handled during form submission',
        data: { formType: payload.formType || 'unknown' }
      }
    }

    // Handle progress updates - navigate to onboarding if needed
    const handleUpdateProgress = async (payload: OnboardingProgressPayload, requestId: string): Promise<EventResponse> => {
      console.log('🌐 Global AG-UI: Handling updateProgress', payload)
      
      // Check if payload is valid
      if (!payload || typeof payload !== 'object') {
        console.warn('🌐 Global AG-UI: Invalid payload for updateProgress:', payload)
        return {
          success: true,
          message: 'Progress update skipped - invalid payload',
          data: { step: 'unknown' }
        }
      }

      // Ensure step exists
      if (!payload.step) {
        console.warn('🌐 Global AG-UI: Missing step in updateProgress payload:', payload)
        return {
          success: true,
          message: 'Progress update skipped - missing step',
          data: { step: 'unknown' }
        }
      }
      
      // If we're not on the onboarding page, navigate there
      if (typeof window !== 'undefined' && window.location.pathname !== '/onboarding') {
        console.log('🌐 Navigating to onboarding page to handle progress update')
        
        // Store current step in sessionStorage
        sessionStorage.setItem('mae-current-step', payload.step)
        
        // Navigate to onboarding page
        router.push('/onboarding')
        
        return {
          success: true,
          message: `Navigating to onboarding page for step ${payload.step}`,
          data: { step: payload.step, navigated: true }
        }
      }
      
      return {
        success: false,
        error: 'Progress update should be handled by onboarding interface on this page'
      }
    }

    // Handle confirmation display - can work globally
    const handleShowConfirmation = async (payload: { message: string; type?: 'success' | 'error' | 'info' }, requestId: string): Promise<EventResponse> => {
      console.log('🌐 Global AG-UI: Handling showConfirmation', payload)
      
      // Check if payload is valid
      if (!payload || typeof payload !== 'object') {
        console.warn('🌐 Global AG-UI: Invalid payload for showConfirmation:', payload)
        return {
          success: true,
          message: 'Confirmation skipped - invalid payload',
          data: { message: 'Invalid confirmation payload' }
        }
      }

      // Ensure message exists
      if (!payload.message) {
        console.warn('🌐 Global AG-UI: Missing message in showConfirmation payload:', payload)
        return {
          success: true,
          message: 'Confirmation skipped - missing message',
          data: { message: 'No message provided' }
        }
      }
      
      // Show a simple alert for now - could be enhanced with a toast system
      if (typeof window !== 'undefined') {
        // Use a simple alert for global confirmation
        alert(payload.message)
      }
      
      return {
        success: true,
        message: 'Confirmation displayed',
        data: payload
      }
    }

    // Handle form clearing
    const handleClearForm = async (payload: { formType: 'user' | 'family_member' }, requestId: string): Promise<EventResponse> => {
      console.log('🌐 Global AG-UI: Handling clearForm', payload)
      
      // Check if payload is valid
      if (!payload || typeof payload !== 'object') {
        console.warn('🌐 Global AG-UI: Invalid payload for clearForm:', payload)
        return {
          success: true,
          message: 'Form clear skipped - invalid payload',
          data: { formType: 'unknown' }
        }
      }

      // Ensure formType exists
      if (!payload.formType) {
        console.warn('🌐 Global AG-UI: Missing formType in clearForm payload:', payload)
        return {
          success: true,
          message: 'Form clear skipped - missing formType',
          data: { formType: 'unknown' }
        }
      }
      
      if (payload.formType === 'user') {
        tempUserData = {}
      } else {
        tempFamilyMembers = []
      }
      
      // Clear sessionStorage as well
      sessionStorage.removeItem('mae-temp-user-data')
      sessionStorage.removeItem('mae-temp-family-members')
      
      return {
        success: true,
        message: `${payload.formType} form data cleared`,
        data: { formType: payload.formType }
      }
    }

    // Handle onboarding reset
    const handleResetOnboarding = async (payload: any, requestId: string): Promise<EventResponse> => {
      console.log('🌐 Global AG-UI: Handling resetOnboarding')
      
      // Clear all temporary data
      tempUserData = {}
      tempFamilyMembers = []
      
      // Clear sessionStorage
      sessionStorage.removeItem('mae-temp-user-data')
      sessionStorage.removeItem('mae-temp-family-members')
      sessionStorage.removeItem('mae-current-step')
      sessionStorage.removeItem('mae-should-submit')
      
      return {
        success: true,
        message: 'Onboarding data reset',
        data: {}
      }
    }

    // Register global event listeners
    aguiEventListener.addEventListener(AG_UI_EVENTS.FILL_USER_FORM, handleFillUserForm)
    aguiEventListener.addEventListener(AG_UI_EVENTS.SUBMIT_USER_FORM, handleSubmitUserForm)
    aguiEventListener.addEventListener(AG_UI_EVENTS.ADD_FAMILY_MEMBER, handleAddFamilyMember)
    aguiEventListener.addEventListener(AG_UI_EVENTS.VALIDATE_FORM, handleValidateForm)
    aguiEventListener.addEventListener(AG_UI_EVENTS.UPDATE_PROGRESS, handleUpdateProgress)
    aguiEventListener.addEventListener(AG_UI_EVENTS.SHOW_CONFIRMATION, handleShowConfirmation)
    aguiEventListener.addEventListener(AG_UI_EVENTS.CLEAR_FORM, handleClearForm)
    aguiEventListener.addEventListener(AG_UI_EVENTS.RESET_ONBOARDING, handleResetOnboarding)

    console.log('🌐 Global AG-UI event listeners registered')

    return () => {
      // Clean up listeners when component unmounts
      aguiEventListener.removeAllListeners()
      console.log('🌐 Global AG-UI event listeners removed')
    }
  }, [router])

  // This component doesn't render anything visible
  return null
}
