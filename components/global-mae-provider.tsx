"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { usePathname } from 'next/navigation'

interface MaeSessionState {
  isConnected: boolean
  isRecording: boolean
  isSpeaking: boolean
  sessionId?: string
  conversationContext?: any
  error?: string
}

interface GlobalMaeContextType {
  sessionState: MaeSessionState
  updateSessionState: (updates: Partial<MaeSessionState>) => void
  continuationMessage?: string
  setContinuationMessage: (message: string) => void
  isSessionActive: boolean
}

const GlobalMaeContext = createContext<GlobalMaeContextType | undefined>(undefined)

export function useGlobalMaeSession() {
  const context = useContext(GlobalMaeContext)
  if (!context) {
    throw new Error('useGlobalMaeSession must be used within GlobalMaeProvider')
  }
  return context
}

interface GlobalMaeProviderProps {
  children: React.ReactNode
}

export function GlobalMaeProvider({ children }: GlobalMaeProviderProps) {
  const pathname = usePathname()
  const [sessionState, setSessionState] = useState<MaeSessionState>({
    isConnected: false,
    isRecording: false,
    isSpeaking: false,
  })
  const [continuationMessage, setContinuationMessage] = useState<string>("")
  
  const updateSessionState = (updates: Partial<MaeSessionState>) => {
    setSessionState(prev => ({ ...prev, ...updates }))
    
    // Store session state in sessionStorage for cross-page continuity
    const newState = { ...sessionState, ...updates }
    sessionStorage.setItem('mae-global-session-state', JSON.stringify(newState))
    
    // Dispatch global event for components that need to know about session changes
    window.dispatchEvent(new CustomEvent('mae-global-session-change', {
      detail: newState
    }))
  }

  // Load session state from storage on mount
  useEffect(() => {
    const savedState = sessionStorage.getItem('mae-global-session-state')
    if (savedState) {
      try {
        const parsed = JSON.parse(savedState)
        setSessionState(parsed)
      } catch (error) {
        console.error('Failed to parse saved Mae session state:', error)
      }
    }
  }, [])

  // Handle page navigation continuity
  useEffect(() => {
    const handlePageChange = () => {
      // If Mae session was active and user is navigating, prepare continuation
      if (sessionState.isConnected) {
        let message = ""
        
        if (pathname === '/onboarding' && sessionState.isConnected) {
          message = "I'm continuing our conversation from the landing page. I'm ready to help you with the onboarding process or any parenting questions you have."
        } else if (pathname === '/' && sessionState.isConnected) {
          message = "I'm back on the main page. How can I help you today?"
        } else {
          message = "I'm continuing our conversation on this page. How can I assist you?"
        }
        
        setContinuationMessage(message)
        
        // Store continuation context
        sessionStorage.setItem('mae-continuation-context', JSON.stringify({
          fromPath: pathname,
          message,
          timestamp: Date.now()
        }))
      }
    }

    handlePageChange()
  }, [pathname, sessionState.isConnected])

  // Listen for Mae session events from other components
  useEffect(() => {
    const handleMaeSessionUpdate = (event: CustomEvent) => {
      const { type, data } = event.detail
      
      switch (type) {
        case 'connection_status':
          updateSessionState({ isConnected: data.connected })
          break
        case 'voice_status':
          updateSessionState({ 
            isRecording: data.recording, 
            isSpeaking: data.speaking 
          })
          break
        case 'error':
          updateSessionState({ error: data.error })
          break
        case 'session_end':
          updateSessionState({
            isConnected: false,
            isRecording: false,
            isSpeaking: false,
            sessionId: undefined,
            conversationContext: undefined,
            error: undefined
          })
          // Clear storage
          sessionStorage.removeItem('mae-global-session-state')
          sessionStorage.removeItem('mae-continuation-context')
          break
      }
    }

    window.addEventListener('mae-session-update', handleMaeSessionUpdate as EventListener)
    
    return () => {
      window.removeEventListener('mae-session-update', handleMaeSessionUpdate as EventListener)
    }
  }, [])

  const value: GlobalMaeContextType = {
    sessionState,
    updateSessionState,
    continuationMessage,
    setContinuationMessage,
    isSessionActive: sessionState.isConnected || sessionState.isRecording
  }

  return (
    <GlobalMaeContext.Provider value={value}>
      {children}
    </GlobalMaeContext.Provider>
  )
}