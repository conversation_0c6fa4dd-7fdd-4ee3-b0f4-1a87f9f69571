'use client';

import { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Plus, Trash2, Sparkles, User, Mail, Phone, MapPin, Calendar, Heart } from 'lucide-react';
import { cn } from '@/lib/utils';

interface MaeInteractiveUIProps {
  streamedInfo: string[];
  currentStep: number;
  onboardingData: any;
  onDataUpdate: (data: any) => void;
}

export function MaeInteractiveUI({ 
  streamedInfo, 
  currentStep, 
  onboardingData, 
  onDataUpdate 
}: MaeInteractiveUIProps) {
  const scrollRef = useRef<HTMLDivElement>(null);
  const [localData, setLocalData] = useState(onboardingData);

  useEffect(() => {
    setLocalData(onboardingData);
  }, [onboardingData]);

  useEffect(() => {
    // Auto-scroll to bottom when new info is added
    if (scrollRef.current) {
      scrollRef.current.scrollTop = scrollRef.current.scrollHeight;
    }
  }, [streamedInfo]);

  const handleParentInfoChange = (field: string, value: string) => {
    const newData = {
      ...localData,
      parentInfo: {
        ...localData.parentInfo,
        [field]: value
      }
    };
    setLocalData(newData);
    onDataUpdate(newData);
  };

  const handleAddChild = () => {
    const newData = {
      ...localData,
      children: [
        ...localData.children,
        { name: '', age: 0, interests: [] }
      ]
    };
    setLocalData(newData);
    onDataUpdate(newData);
  };

  const handleChildChange = (index: number, field: string, value: any) => {
    const newChildren = [...localData.children];
    newChildren[index] = {
      ...newChildren[index],
      [field]: value
    };
    const newData = {
      ...localData,
      children: newChildren
    };
    setLocalData(newData);
    onDataUpdate(newData);
  };

  const handleRemoveChild = (index: number) => {
    const newChildren = localData.children.filter((_: any, i: number) => i !== index);
    const newData = {
      ...localData,
      children: newChildren
    };
    setLocalData(newData);
    onDataUpdate(newData);
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0: // Parent Information
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="parent-name" className="flex items-center gap-2">
                <User className="h-4 w-4 text-teal-600" />
                Your Name
              </Label>
              <Input
                id="parent-name"
                placeholder="Enter your name"
                value={localData.parentInfo.name}
                onChange={(e) => handleParentInfoChange('name', e.target.value)}
                className="border-teal-200 focus:border-teal-400"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="parent-email" className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-teal-600" />
                Email Address
              </Label>
              <Input
                id="parent-email"
                type="email"
                placeholder="<EMAIL>"
                value={localData.parentInfo.email}
                onChange={(e) => handleParentInfoChange('email', e.target.value)}
                className="border-teal-200 focus:border-teal-400"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="parent-phone" className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-teal-600" />
                Phone Number (Optional)
              </Label>
              <Input
                id="parent-phone"
                type="tel"
                placeholder="(*************"
                value={localData.parentInfo.phone || ''}
                onChange={(e) => handleParentInfoChange('phone', e.target.value)}
                className="border-teal-200 focus:border-teal-400"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="parent-location" className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-teal-600" />
                Location (Optional)
              </Label>
              <Input
                id="parent-location"
                placeholder="City, State"
                value={localData.parentInfo.location || ''}
                onChange={(e) => handleParentInfoChange('location', e.target.value)}
                className="border-teal-200 focus:border-teal-400"
              />
            </div>
          </div>
        );

      case 1: // Children Information
        return (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-800">Your Children</h3>
              <Button
                onClick={handleAddChild}
                size="sm"
                className="bg-teal-600 hover:bg-teal-700"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Child
              </Button>
            </div>

            <ScrollArea className="h-[300px] pr-4">
              <div className="space-y-4">
                {localData.children.map((child: any, index: number) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    className="border border-teal-200 rounded-lg p-4 bg-white/50"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <h4 className="font-medium text-gray-700">Child {index + 1}</h4>
                      <Button
                        onClick={() => handleRemoveChild(index)}
                        size="sm"
                        variant="ghost"
                        className="text-red-500 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="grid grid-cols-2 gap-3">
                      <div className="space-y-1">
                        <Label className="text-xs">Name</Label>
                        <Input
                          placeholder="Child's name"
                          value={child.name}
                          onChange={(e) => handleChildChange(index, 'name', e.target.value)}
                          className="h-9 text-sm"
                        />
                      </div>

                      <div className="space-y-1">
                        <Label className="text-xs">Age</Label>
                        <Input
                          type="number"
                          placeholder="Age"
                          value={child.age || ''}
                          onChange={(e) => handleChildChange(index, 'age', parseInt(e.target.value))}
                          className="h-9 text-sm"
                        />
                      </div>
                    </div>

                    <div className="mt-3 space-y-1">
                      <Label className="text-xs flex items-center gap-1">
                        <Heart className="h-3 w-3 text-pink-500" />
                        Interests (Optional)
                      </Label>
                      <Input
                        placeholder="e.g., Soccer, Reading, Art"
                        value={child.interests?.join(', ') || ''}
                        onChange={(e) => handleChildChange(index, 'interests', e.target.value.split(',').map((s: string) => s.trim()))}
                        className="h-9 text-sm"
                      />
                    </div>
                  </motion.div>
                ))}

                {localData.children.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    <Calendar className="h-12 w-12 mx-auto mb-3 text-teal-300" />
                    <p className="text-sm">No children added yet</p>
                    <p className="text-xs mt-1">Click "Add Child" to get started</p>
                  </div>
                )}
              </div>
            </ScrollArea>
          </div>
        );

      default:
        return (
          <div className="text-center py-8">
            <Sparkles className="h-12 w-12 mx-auto mb-3 text-teal-500" />
            <p className="text-gray-600">Continue to the next step</p>
          </div>
        );
    }
  };

  return (
    <div className="h-full flex flex-col">
      {/* User Information Form */}
      <div className="flex-1 bg-white rounded-lg border-2 border-black p-6 mb-4">
        <h3 className="text-lg font-bold mb-4 text-gray-800">User Information</h3>
        {renderStepContent()}
      </div>

      {/* Streamed Information Display */}
      <div className="bg-gradient-to-br from-teal-50 to-cyan-50 rounded-lg p-4 border border-teal-200">
        <div className="flex items-center gap-2 mb-3">
          <Sparkles className="h-4 w-4 text-teal-600" />
          <p className="text-sm font-semibold text-gray-700">
            Streamed in Real-Time and shown dynamically in Mae's interactive UI
          </p>
        </div>
        
        <div 
          ref={scrollRef}
          className="bg-white/70 rounded p-3 h-32 overflow-y-auto text-xs space-y-1 font-mono"
        >
          <AnimatePresence>
            {streamedInfo.map((info, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="text-gray-600"
              >
                {'>'} {info}
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      </div>
    </div>
  );
}