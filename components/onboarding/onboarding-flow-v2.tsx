'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardHeader, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { X, Users, Sparkles } from 'lucide-react';
import { FamilyGraph } from './family-graph';
import { MaeInteractiveUI } from './mae-interactive-ui';
import { cn } from '@/lib/utils';

interface OnboardingData {
  parentInfo: {
    name: string;
    email: string;
    phone?: string;
    location?: string;
  };
  children: Array<{
    name: string;
    age: number;
    grade?: string;
    interests?: string[];
  }>;
  preferences?: {
    communicationStyle?: string;
    primaryConcerns?: string[];
  };
}

export function OnboardingFlowV2() {
  const [currentStep, setCurrentStep] = useState(0);
  const [onboardingData, setOnboardingData] = useState<OnboardingData>({
    parentInfo: {
      name: '',
      email: '',
    },
    children: [],
  });
  const [streamedInfo, setStreamedInfo] = useState<string[]>([]);

  const totalSteps = 4;
  const progress = ((currentStep + 1) / totalSteps) * 100;

  // Update streamed information whenever data changes
  useEffect(() => {
    const info: string[] = [];
    
    if (onboardingData.parentInfo.name) {
      info.push(`Parent: ${onboardingData.parentInfo.name}`);
    }
    if (onboardingData.parentInfo.email) {
      info.push(`Email: ${onboardingData.parentInfo.email}`);
    }
    if (onboardingData.parentInfo.location) {
      info.push(`Location: ${onboardingData.parentInfo.location}`);
    }
    
    onboardingData.children.forEach((child, index) => {
      info.push(`Child ${index + 1}: ${child.name}, Age: ${child.age}`);
      if (child.interests && child.interests.length > 0) {
        info.push(`Interests: ${child.interests.join(', ')}`);
      }
    });

    setStreamedInfo(info);
  }, [onboardingData]);

  const handleClose = () => {
    // Handle closing the onboarding flow
    window.location.href = '/';
  };

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-teal-50 via-white to-purple-50 z-50 overflow-hidden">
      {/* Main Container */}
      <div className="h-full flex flex-col">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between max-w-7xl mx-auto">
            <div className="flex items-center gap-3">
              <div className="flex items-center gap-2">
                <Users className="h-6 w-6 text-teal-600" />
                <Sparkles className="h-5 w-5 text-purple-500" />
              </div>
              <h1 className="text-xl font-semibold text-gray-900">
                Welcome to Our Kidz
              </h1>
            </div>
            <button
              onClick={handleClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
              aria-label="Close onboarding"
            >
              <X className="h-5 w-5 text-gray-500" />
            </button>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="px-6 py-3 bg-white border-b border-gray-100">
          <div className="max-w-7xl mx-auto">
            <Progress value={progress} className="h-2" />
            <p className="text-sm text-gray-600 mt-2">
              Step {currentStep + 1} of {totalSteps}
            </p>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 overflow-hidden">
          <div className="h-full max-w-7xl mx-auto px-6 py-6">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 h-full">
              {/* Left Panel - Mae's Interactive UI */}
              <Card className="bg-gradient-to-br from-teal-50 to-cyan-50 border-teal-200 overflow-hidden">
                <CardHeader className="pb-4">
                  <h2 className="text-2xl font-bold text-gray-900">
                    Mae's Interactive UI
                  </h2>
                </CardHeader>
                <CardContent className="h-[calc(100%-5rem)] overflow-hidden">
                  <MaeInteractiveUI 
                    streamedInfo={streamedInfo}
                    currentStep={currentStep}
                    onboardingData={onboardingData}
                    onDataUpdate={setOnboardingData}
                  />
                </CardContent>
              </Card>

              {/* Right Panel - Family Graph */}
              <Card className="bg-gradient-to-br from-purple-50 to-pink-50 border-purple-200 overflow-hidden">
                <CardHeader className="pb-4">
                  <h2 className="text-2xl font-bold text-gray-900">
                    Family Graph
                  </h2>
                  <p className="text-sm text-gray-600 mt-1">
                    As family members are added, the mermaid graph updates in real-time
                  </p>
                </CardHeader>
                <CardContent className="h-[calc(100%-6rem)] overflow-hidden">
                  <FamilyGraph 
                    parentInfo={onboardingData.parentInfo}
                    children={onboardingData.children}
                  />
                </CardContent>
              </Card>
            </div>
          </div>
        </div>

        {/* Navigation Footer */}
        <div className="bg-white border-t border-gray-200 px-6 py-4">
          <div className="max-w-7xl mx-auto flex justify-between items-center">
            <Button
              variant="outline"
              onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
              disabled={currentStep === 0}
            >
              Previous
            </Button>
            
            <div className="flex gap-2">
              {Array.from({ length: totalSteps }).map((_, index) => (
                <div
                  key={index}
                  className={cn(
                    'w-2 h-2 rounded-full transition-colors',
                    index === currentStep 
                      ? 'bg-teal-600' 
                      : index < currentStep 
                        ? 'bg-teal-400' 
                        : 'bg-gray-300'
                  )}
                />
              ))}
            </div>

            <Button
              onClick={() => {
                if (currentStep < totalSteps - 1) {
                  setCurrentStep(currentStep + 1);
                } else {
                  // Complete onboarding
                  console.log('Onboarding complete:', onboardingData);
                }
              }}
              className="bg-teal-600 hover:bg-teal-700"
            >
              {currentStep === totalSteps - 1 ? 'Complete' : 'Next'}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}