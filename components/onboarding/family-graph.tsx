'use client';

import { useEffect, useRef, useState } from 'react';
import mermaid from 'mermaid';
import { Users, User, Baby } from 'lucide-react';
import { motion } from 'framer-motion';

interface FamilyGraphProps {
  parentInfo: {
    name: string;
    email: string;
  };
  children: Array<{
    name: string;
    age: number;
  }>;
}

export function FamilyGraph({ parentInfo, children }: FamilyGraphProps) {
  const graphRef = useRef<HTMLDivElement>(null);
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Initialize mermaid
    mermaid.initialize({
      startOnLoad: true,
      theme: 'default',
      themeVariables: {
        primaryColor: '#14b8a6',
        primaryTextColor: '#fff',
        primaryBorderColor: '#0d9488',
        lineColor: '#5eead4',
        secondaryColor: '#a78bfa',
        tertiaryColor: '#f0abfc',
        background: '#ffffff',
        mainBkg: '#14b8a6',
        secondBkg: '#a78bfa',
        tertiaryBkg: '#f0abfc',
        nodeBorder: '#0d9488',
        clusterBkg: '#f0fdfa',
        clusterBorder: '#14b8a6',
        defaultLinkColor: '#5eead4',
        edgeLabelBackground: '#ffffff',
        nodeTextColor: '#1f2937',
      },
      flowchart: {
        curve: 'basis',
        padding: 20,
        nodeSpacing: 50,
        rankSpacing: 50,
      },
    });
    setIsInitialized(true);
  }, []);

  useEffect(() => {
    if (!isInitialized || !graphRef.current) return;

    const renderGraph = async () => {
      // Clear previous graph
      graphRef.current!.innerHTML = '';

      // Build the mermaid graph definition
      let graphDefinition = 'graph TD\n';

      // Add parent node
      const parentName = parentInfo.name || 'Parent';
      graphDefinition += `    Parent["${parentName}"]\n`;
      graphDefinition += `    Parent:::parentClass\n`;

      // Add children nodes and connections
      if (children.length > 0) {
        children.forEach((child, index) => {
          const childId = `Child${index + 1}`;
          const childName = child.name || `Child ${index + 1}`;
          const childLabel = `${childName}<br/>Age: ${child.age || 'N/A'}`;
          
          graphDefinition += `    ${childId}["${childLabel}"]\n`;
          graphDefinition += `    ${childId}:::childClass\n`;
          graphDefinition += `    Parent --> ${childId}\n`;
        });
      }

      // Add styling
      graphDefinition += `\n    classDef parentClass fill:#14b8a6,stroke:#0d9488,stroke-width:3px,color:#fff\n`;
      graphDefinition += `    classDef childClass fill:#a78bfa,stroke:#7c3aed,stroke-width:2px,color:#fff\n`;

      try {
        const { svg } = await mermaid.render('familyGraph', graphDefinition);
        if (graphRef.current) {
          graphRef.current.innerHTML = svg;
        }
      } catch (error) {
        console.error('Error rendering mermaid graph:', error);
        // Fallback to simple visualization
        if (graphRef.current) {
          graphRef.current.innerHTML = `
            <div class="flex flex-col items-center justify-center h-full text-gray-500">
              <Users class="h-12 w-12 mb-2" />
              <p class="text-sm">Family visualization will appear here</p>
            </div>
          `;
        }
      }
    };

    renderGraph();
  }, [isInitialized, parentInfo, children]);

  // Fallback simple visualization if mermaid fails
  const renderSimpleGraph = () => {
    return (
      <div className="flex flex-col items-center justify-center h-full p-8">
        {/* Parent Node */}
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          className="bg-teal-500 text-white rounded-lg px-6 py-3 shadow-lg flex items-center gap-2"
        >
          <User className="h-5 w-5" />
          <span className="font-semibold">{parentInfo.name || 'Parent'}</span>
        </motion.div>

        {/* Connections */}
        {children.length > 0 && (
          <div className="flex flex-col items-center">
            <div className="w-0.5 h-8 bg-teal-300 my-2" />
            <div className="flex gap-4">
              {children.map((child, index) => (
                <div key={index} className="flex flex-col items-center">
                  <div className="w-0.5 h-4 bg-purple-300" />
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={{ scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                    className="bg-purple-500 text-white rounded-lg px-4 py-2 shadow-md flex items-center gap-2"
                  >
                    <Baby className="h-4 w-4" />
                    <div className="text-sm">
                      <div className="font-medium">{child.name || `Child ${index + 1}`}</div>
                      <div className="text-xs opacity-90">Age: {child.age || 'N/A'}</div>
                    </div>
                  </motion.div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Empty State */}
        {children.length === 0 && (
          <div className="mt-8 text-center text-gray-500">
            <p className="text-sm">Add family members to see the graph</p>
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="h-full w-full bg-white rounded-lg p-4 overflow-auto">
      <div ref={graphRef} className="h-full w-full flex items-center justify-center">
        {/* Mermaid graph will be rendered here */}
        {!isInitialized && renderSimpleGraph()}
      </div>
    </div>
  );
}