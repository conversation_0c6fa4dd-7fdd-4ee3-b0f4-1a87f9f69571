"use client"

import { useState, useEffect, useCallback } from "react"
import Image from "next/image"
import Link from "next/link"
import { ChevronRight } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"

const slides = [
  {
    id: 1,
    title: "Meet Mae...",
    image: "/images/hero-slide-1.jpg",
    buttonText: "Learn more",
    buttonLink: "/contact",
    type: "image",
  },
  {
    id: 2,
    title: "just speak into\nthe phone.",
    image: "/mother-child-care.png",
    buttonText: "Discover how",
    buttonLink: "/ai",
    type: "image",
  },
  {
    id: 3,
    title: "are my children sleeping enough?",
    image: "/children-in-bed.jpg",
    buttonText: "Contact Us",
    buttonLink: "/contact",
    type: "image",
  },
]

export default function HeroCarousel() {
  const [currentSlide, setCurrentSlide] = useState(0)

  const goToSlide = useCallback((index: number) => {
    setCurrentSlide(index)
  }, [])

  const nextSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev + 1) % slides.length)
  }, [])

  useEffect(() => {
    const interval = setInterval(nextSlide, 5000)
    return () => clearInterval(interval)
  }, [nextSlide])

  return (
    <div className="relative h-[500px] md:h-[600px] lg:h-[700px] w-full overflow-hidden">
      <AnimatePresence mode="wait">
        {slides.map((slide, index) => 
          index === currentSlide && (
            <motion.div
              key={slide.id}
              initial={{ opacity: 0, scale: 1.05 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ 
                duration: 0.7,
                ease: [0.25, 0.46, 0.45, 0.94]
              }}
              className="absolute inset-0"
            >
              <motion.div 
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2, duration: 0.5 }}
                className="absolute inset-0 bg-black/10 z-10" 
              />

              {slide.type === "image" ? (
                <motion.div 
                  initial={{ scale: 1.1 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 1.5, ease: "easeOut" }}
                  className="relative w-full h-full"
                >
                  <video src={slide.image} autoPlay muted loop playsInline className="w-full h-full object-cover" />
                  <div className="absolute top-0 right-0 w-32 h-16 bg-black/30 z-5"></div>
                </motion.div>
              ) : (
                <motion.div
                  initial={{ scale: 1.1 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 1.5, ease: "easeOut" }}
                  className="relative w-full h-full"
                >
                  <Image
                    src={slide.image || "/placeholder.svg"}
                    alt={slide.title}
                    fill
                    className="object-cover object-top"
                    priority={index === 0}
                  />
                </motion.div>
              )}

              <div className="absolute inset-0 z-20 flex items-center">
                <div className="container mx-auto px-4">
                  <div className="max-w-xl">
                    {slide.id === 2 ? (
                      <motion.h1 
                        initial={{ y: 100, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ delay: 0.4, duration: 0.8, ease: "easeOut" }}
                        className="text-4xl md:text-5xl lg:text-6xl font-light text-white mb-6 drop-shadow-lg"
                      >
                        just speak
                        <br />
                        into the
                        <br />
                        phone.
                      </motion.h1>
                    ) : slide.id === 3 ? (
                      <motion.h1 
                        initial={{ y: 100, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ delay: 0.4, duration: 0.8, ease: "easeOut" }}
                        className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 drop-shadow-lg"
                      >
                        are my children
                        <br />
                        sleeping
                        <br />
                        enough?
                      </motion.h1>
                    ) : (
                      <motion.h1 
                        initial={{ y: 100, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ delay: 0.4, duration: 0.8, ease: "easeOut" }}
                        className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 drop-shadow-lg"
                      >
                        {slide.title}
                      </motion.h1>
                    )}
                    <motion.div
                      initial={{ y: 50, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.8, duration: 0.6, ease: "easeOut" }}
                      whileHover={{ scale: 1.05 }}
                      whileTap={{ scale: 0.95 }}
                    >
                      <Link
                        href={slide.buttonLink}
                        className="inline-flex items-center justify-center px-6 py-3 bg-teal-500 text-white rounded-full hover:bg-teal-600 transition-all duration-300 hover:shadow-lg"
                      >
                        {slide.buttonText} 
                        <motion.div
                          animate={{ x: [0, 5, 0] }}
                          transition={{ 
                            repeat: Infinity, 
                            duration: 1.5,
                            ease: "easeInOut"
                          }}
                        >
                          <ChevronRight className="ml-2 h-4 w-4" />
                        </motion.div>
                      </Link>
                    </motion.div>
                  </div>
                </div>
              </div>
            </motion.div>
          )
        )}
      </AnimatePresence>

      <motion.div 
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 1.0, duration: 0.6 }}
        className="absolute bottom-8 left-0 right-0 z-30 flex justify-center gap-2"
      >
        {slides.map((_, index) => (
          <motion.button
            key={index}
            onClick={() => goToSlide(index)}
            whileHover={{ scale: 1.3 }}
            whileTap={{ scale: 0.8 }}
            className={`w-3 h-3 rounded-full transition-all ${
              index === currentSlide ? "bg-white scale-125" : "bg-white/50 hover:bg-white/80"
            }`}
            aria-label={`Go to slide ${index + 1}`}
          />
        ))}
      </motion.div>
    </div>
  )
}
