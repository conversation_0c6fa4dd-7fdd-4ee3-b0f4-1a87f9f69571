"use client";

import React, { useState, useEffect } from "react";
import {
  CheckCircle2,
  Circle,
  CircleAlert,
  CircleDotDashed,
  CircleX,
} from "lucide-react";
import { motion, AnimatePresence, LayoutGroup, Variants } from "framer-motion";
import { useMaeActivityTracker, type MaeActivity } from "@/hooks/use-mae-activity-tracker";

// Type definitions
interface Subtask {
  id: string;
  title: string;
  description: string;
  status: string;
  priority: string;
  tools?: string[]; // Optional array of MCP server tools
}

interface Task {
  id: string;
  title: string;
  description: string;
  status: string;
  priority: string;
  level: number;
  dependencies: string[];
  subtasks: Subtask[];
}

// Real-time task generation based on <PERSON>'s activities

export default function Plan() {
  const maeActivity = useMaeActivityTracker();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [expandedTasks, setExpandedTasks] = useState<string[]>([]);
  const [expandedSubtasks, setExpandedSubtasks] = useState<{
    [key: string]: boolean;
  }>({});
  
  // Add support for reduced motion preference
  const prefersReducedMotion = 
    typeof window !== 'undefined' 
      ? window.matchMedia('(prefers-reduced-motion: reduce)').matches 
      : false;

  // Generate tasks based on Mae's activities and session state
  useEffect(() => {
    const generateTasksFromMaeActivity = () => {
      const tasks: Task[] = [];
      
      // Current Session Task
      if (maeActivity.sessionActive) {
        const sessionTask: Task = {
          id: "session",
          title: "Voice Session Active",
          description: "Mae is actively engaged in a voice conversation",
          status: maeActivity.isListening ? "in-progress" : 
                 maeActivity.isSpeaking ? "in-progress" : "completed",
          priority: "high",
          level: 0,
          dependencies: [],
          subtasks: [
            {
              id: "session.listening",
              title: "Listening for input",
              description: "Mae is listening for voice commands",
              status: maeActivity.isListening ? "in-progress" : "completed",
              priority: "high",
              tools: ["voice-recognition", "audio-processing"]
            },
            {
              id: "session.speaking",
              title: "Responding to user",
              description: "Mae is providing voice responses",
              status: maeActivity.isSpeaking ? "in-progress" : "completed",
              priority: "high",
              tools: ["text-to-speech", "voice-synthesis"]
            }
          ]
        };
        tasks.push(sessionTask);
      }

      // Current Tool Task
      if (maeActivity.currentTool) {
        const toolTask: Task = {
          id: "current-tool",
          title: `Using ${maeActivity.currentTool}`,
          description: `Mae is currently executing ${maeActivity.currentTool}`,
          status: "in-progress",
          priority: "high",
          level: 0,
          dependencies: maeActivity.sessionActive ? ["session"] : [],
          subtasks: [
            {
              id: "current-tool.execute",
              title: "Executing function",
              description: `Running ${maeActivity.currentTool} with parameters`,
              status: "in-progress",
              priority: "high",
              tools: [maeActivity.currentTool, "function-executor"]
            }
          ]
        };
        tasks.push(toolTask);
      }

      // Recent Activities Task
      if (maeActivity.activities.length > 0) {
        const recentActivities = maeActivity.activities.slice(0, 5);
        const activityTask: Task = {
          id: "recent-activities",
          title: "Recent Activities",
          description: "Mae's recent actions and function calls",
          status: "completed",
          priority: "medium",
          level: 0,
          dependencies: [],
          subtasks: recentActivities.map((activity, index) => ({
            id: `activity.${activity.id}`,
            title: activity.name,
            description: activity.description,
            status: activity.status,
            priority: activity.type === 'function' ? "high" : "medium",
            tools: activity.type === 'tool' ? [activity.name] : 
                   activity.type === 'function' ? [activity.name, "function-executor"] :
                   activity.type === 'processing' ? ["processing-engine"] : []
          }))
        };
        tasks.push(activityTask);
      }

      // Onboarding Flow Task (if activities suggest onboarding)
      const onboardingActivities = maeActivity.activities.filter(a => 
        a.name.toLowerCase().includes('onboarding') || 
        a.name.toLowerCase().includes('user') ||
        a.name.toLowerCase().includes('family') ||
        a.description.toLowerCase().includes('onboarding')
      );
      
      if (onboardingActivities.length > 0) {
        const onboardingTask: Task = {
          id: "onboarding",
          title: "Onboarding Process",
          description: "Guiding user through account setup and family information",
          status: maeActivity.activities.some(a => a.status === 'active') ? "in-progress" : "completed",
          priority: "high",
          level: 0,
          dependencies: [],
          subtasks: [
            {
              id: "onboarding.user-info",
              title: "Collect User Information",
              description: "Gathering user details through voice input",
              status: onboardingActivities.some(a => a.name.includes('user') && a.status === 'active') ? "in-progress" : "completed",
              priority: "high",
              tools: ["collect_user_information", "voice-input-processor"]
            },
            {
              id: "onboarding.family-info",
              title: "Add Family Members",
              description: "Adding family member information",
              status: onboardingActivities.some(a => a.name.includes('family') && a.status === 'active') ? "in-progress" : "pending",
              priority: "high",
              tools: ["add_family_member", "family-data-processor"]
            },
            {
              id: "onboarding.validation",
              title: "Validate Information",
              description: "Validating all collected information",
              status: onboardingActivities.some(a => a.name.includes('valid') && a.status === 'active') ? "in-progress" : "pending",
              priority: "medium",
              tools: ["validate_onboarding_data", "data-validator"]
            }
          ]
        };
        tasks.push(onboardingTask);
      }
      return tasks;
    };

      // Connection Status Task
    //   const connectionTask: Task = {
    //     id: "connection",
    //     title: "Connection Status",
    //     description: "Mae's connection status",
    //     status: "in-progress",
    //     priority: "high",
    //     level: 0,
    //     dependencies: [],
    //     subtasks: [
    //       // {
    //       //   id: "connection.gemini",
    //       //   title: "Gemini Live API",
    //       //   description: "Connection to Google's Gemini Live API",
    //       //   status: maeActivity.connectionStatus === 'connected' ? "completed" : 
    //       //          maeActivity.connectionStatus === 'connecting' ? "in-progress" : "failed",
    //       //   priority: "high",
    //       //   tools: ["gemini-live-api", "websocket-connection"]
    //       // },
    //       // {
    //       //   id: "connection.audio",
    //       //   title: "Audio Processing",
    //       //   description: "Audio input/output processing system",
    //       //   status: maeActivity.sessionActive ? "completed" : "pending",
    //       //   priority: "medium",
    //       //   tools: ["audio-worklet", "media-devices"]
    //       // }
    //     ]
    //   };
    //   tasks.push(connectionTask);

    //   return tasks;
    // };

    const newTasks = generateTasksFromMaeActivity();
    setTasks(newTasks);
    
    // Auto-expand first task if it's active
    if (newTasks.length > 0 && newTasks[0].status === "in-progress") {
      setExpandedTasks([newTasks[0].id]);
    }
  }, [maeActivity.activities, maeActivity.sessionActive, maeActivity.currentTool, maeActivity.connectionStatus, maeActivity.isListening, maeActivity.isSpeaking]);

  // Toggle task expansion
  const toggleTaskExpansion = (taskId: string) => {
    setExpandedTasks((prev) =>
      prev.includes(taskId)
        ? prev.filter((id) => id !== taskId)
        : [...prev, taskId],
    );
  };

  // Toggle subtask expansion
  const toggleSubtaskExpansion = (taskId: string, subtaskId: string) => {
    const key = `${taskId}-${subtaskId}`;
    setExpandedSubtasks((prev) => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  // Task status is now controlled by Mae's real activity - no manual toggling needed

  // Animation variants with reduced motion support
  const taskVariants = {
    hidden: { 
      opacity: 0, 
      y: prefersReducedMotion ? 0 : -5 
    },
    visible: { 
      opacity: 1, 
      y: 0,
      transition: { 
        type: prefersReducedMotion ? "tween" : "spring", 
        stiffness: 500, 
        damping: 30,
        duration: prefersReducedMotion ? 0.2 : undefined
      }
    },
    exit: {
      opacity: 0,
      y: prefersReducedMotion ? 0 : -5,
      transition: { duration: 0.15 }
    }
  };

  const subtaskListVariants = {
    hidden: { 
      opacity: 0, 
      height: 0,
      overflow: "hidden" 
    },
    visible: { 
      height: "auto", 
      opacity: 1,
      overflow: "visible",
      transition: { 
        duration: 0.25, 
        staggerChildren: prefersReducedMotion ? 0 : 0.05,
        when: "beforeChildren",
        ease: [0.2, 0.65, 0.3, 0.9] // Custom easing curve for Apple-like feel
      }
    },
    exit: {
      height: 0,
      opacity: 0,
      overflow: "hidden",
      transition: { 
        duration: 0.2,
        ease: [0.2, 0.65, 0.3, 0.9]
      }
    }
  };

  const subtaskVariants = {
    hidden: { 
      opacity: 0, 
      x: prefersReducedMotion ? 0 : -10 
    },
    visible: { 
      opacity: 1, 
      x: 0,
      transition: { 
        type: prefersReducedMotion ? "tween" : "spring", 
        stiffness: 500, 
        damping: 25,
        duration: prefersReducedMotion ? 0.2 : undefined
      }
    },
    exit: {
      opacity: 0,
      x: prefersReducedMotion ? 0 : -10,
      transition: { duration: 0.15 }
    }
  };

  const subtaskDetailsVariants = {
    hidden: { 
      opacity: 0, 
      height: 0,
      overflow: "hidden"
    },
    visible: { 
      opacity: 1, 
      height: "auto",
      overflow: "visible",
      transition: { 
        duration: 0.25,
        ease: [0.2, 0.65, 0.3, 0.9]
      }
    }
  };

  // Status badge animation variants
  const statusBadgeVariants = {
    initial: { scale: 1 },
    animate: { 
      scale: prefersReducedMotion ? 1 : [1, 1.08, 1],
      transition: { 
        duration: 0.35,
        ease: [0.34, 1.56, 0.64, 1] // Springy custom easing for bounce effect
      }
    }
  };

  return (
    <div className="bg-background text-foreground h-full overflow-auto">
      <motion.div
        className="healthcare-card border-l-4 border-l-blue-500 rounded-lg border shadow overflow-hidden"
        initial={{ opacity: 0, y: 10 }}
        animate={{
          opacity: 1,
          y: 0,
          transition: {
            duration: 0.3,
            ease: [0.2, 0.65, 0.3, 0.9]
          }
        }}
      >
        <LayoutGroup>
          <div className="p-4 overflow-hidden">
            <ul className="space-y-1 overflow-hidden">
              {tasks.length === 0 ? (
                <motion.li
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className="text-center py-8"
                >
                  <div className="text-muted-foreground">
                    <Circle className="h-14 w-14 mx-auto mb-2" />
                    <p className="text-sm">Mae is ready to assist you</p>
                    <p className="text-xs mt-1">Start a voice conversation to see her activities</p>
                  </div>
                </motion.li>
              ) : (
                tasks.map((task, index) => {
                const isExpanded = expandedTasks.includes(task.id);
                const isCompleted = task.status === "completed";

                return (
                  <motion.li
                    key={task.id}
                    className={` ${index !== 0 ? "mt-1 pt-2" : ""} `}
                    initial="hidden"
                    animate="visible"
                    variants={taskVariants as Variants}
                  >
                    {/* Task row */}
                    <motion.div 
                      className="group flex items-center px-3 py-1.5 rounded-md"
                      whileHover={{ 
                        backgroundColor: "rgba(0,0,0,0.03)",
                        transition: { duration: 0.2 }
                      }}
                    >
                      <motion.div
                        className="mr-2 flex-shrink-0"
                        whileHover={{ scale: 1.1 }}
                      >
                        <AnimatePresence mode="wait">
                          <motion.div
                            key={task.status}
                            initial={{ opacity: 0, scale: 0.8, rotate: -10 }}
                            animate={{ opacity: 1, scale: 1, rotate: 0 }}
                            exit={{ opacity: 0, scale: 0.8, rotate: 10 }}
                            transition={{
                              duration: 0.2,
                              ease: [0.2, 0.65, 0.3, 0.9]
                            }}
                          >
                            {task.status === "completed" ? (
                              <CheckCircle2 className="h-4.5 w-4.5 text-green-500" />
                            ) : task.status === "in-progress" ? (
                              <CircleDotDashed className="h-4.5 w-4.5 text-blue-500" />
                            ) : task.status === "need-help" ? (
                              <CircleAlert className="h-4.5 w-4.5 text-yellow-500" />
                            ) : task.status === "failed" ? (
                              <CircleX className="h-4.5 w-4.5 text-red-500" />
                            ) : (
                              <Circle className="text-muted-foreground h-4.5 w-4.5" />
                            )}
                          </motion.div>
                        </AnimatePresence>
                      </motion.div>

                      <motion.div
                        className="flex min-w-0 flex-grow cursor-pointer items-center justify-between"
                        onClick={() => toggleTaskExpansion(task.id)}
                      >
                        <div className="mr-2 flex-1 truncate">
                          <span
                            className={`${isCompleted ? "text-muted-foreground line-through" : ""}`}
                          >
                            {task.title}
                          </span>
                        </div>

                        <div className="flex flex-shrink-0 items-center space-x-2 text-xs">
                          {task.dependencies.length > 0 && (
                            <div className="flex items-center mr-2">
                              <div className="flex flex-wrap gap-1">
                                {task.dependencies.map((dep, idx) => (
                                  <motion.span
                                    key={idx}
                                    className="bg-secondary/40 text-secondary-foreground rounded px-1.5 py-0.5 text-[10px] font-medium shadow-sm"
                                    initial={{ opacity: 0, scale: 0.9 }}
                                    animate={{ opacity: 1, scale: 1 }}
                                    transition={{
                                      duration: 0.2,
                                      delay: idx * 0.05
                                    }}
                                    whileHover={{ 
                                      y: -1, 
                                      backgroundColor: "rgba(0,0,0,0.1)",
                                      transition: { duration: 0.2 } 
                                    }}
                                  >
                                    {dep}
                                  </motion.span>
                                ))}
                              </div>
                            </div>
                          )}

                          <motion.span
                            className={`rounded px-1.5 py-0.5 ${
                              task.status === "completed"
                                ? "bg-green-100 text-green-700"
                                : task.status === "in-progress"
                                  ? "bg-blue-100 text-blue-700"
                                  : task.status === "need-help"
                                    ? "bg-yellow-100 text-yellow-700"
                                    : task.status === "failed"
                                      ? "bg-red-100 text-red-700"
                                      : "bg-muted text-muted-foreground"
                            }`}
                            variants={statusBadgeVariants as unknown as Variants}
                            initial="initial"
                            animate="animate"
                            key={task.status} // Force animation on status change
                          >
                            {task.status}
                          </motion.span>
                        </div>
                      </motion.div>
                    </motion.div>

                    {/* Subtasks - staggered */}
                    <AnimatePresence mode="wait">
                      {isExpanded && task.subtasks.length > 0 && (
                        <motion.div 
                          className="relative overflow-hidden"
                          variants={subtaskListVariants as unknown as Variants}
                          initial="hidden"
                          animate="visible"
                          exit="hidden"
                          layout
                        >
                          {/* Vertical connecting line aligned with task icon */}
                          <div className="absolute top-0 bottom-0 left-[20px] border-l-2 border-dashed border-muted-foreground/30" />
                          <ul className="border-muted mt-1 mr-2 mb-1.5 ml-3 space-y-0.5">
                            {task.subtasks.map((subtask) => {
                              const subtaskKey = `${task.id}-${subtask.id}`;
                              const isSubtaskExpanded = expandedSubtasks[subtaskKey];

                              return (
                                <motion.li
                                  key={subtask.id}
                                  className="group flex flex-col py-0.5 pl-6"
                                  onClick={() =>
                                    toggleSubtaskExpansion(task.id, subtask.id)
                                  }
                                  variants={subtaskVariants as Variants}
                                  initial="hidden"
                                  animate="visible"
                                  exit="exit"
                                  layout
                                >
                                  <motion.div 
                                    className="flex flex-1 items-center rounded-md p-1"
                                    whileHover={{ 
                                      backgroundColor: "rgba(0,0,0,0.03)",
                                      transition: { duration: 0.2 }
                                    }}
                                    layout
                                  >
                                    <motion.div
                                      className="mr-2 flex-shrink-0"
                                      whileHover={{ scale: 1.1 }}
                                      layout
                                    >
                                      <AnimatePresence mode="wait">
                                        <motion.div
                                          key={subtask.status}
                                          initial={{ opacity: 0, scale: 0.8, rotate: -10 }}
                                          animate={{ opacity: 1, scale: 1, rotate: 0 }}
                                          exit={{ opacity: 0, scale: 0.8, rotate: 10 }}
                                          transition={{
                                            duration: 0.2,
                                            ease: [0.2, 0.65, 0.3, 0.9]
                                          }}
                                        >
                                          {subtask.status === "completed" ? (
                                            <CheckCircle2 className="h-3.5 w-3.5 text-green-500" />
                                          ) : subtask.status === "in-progress" ? (
                                            <CircleDotDashed className="h-3.5 w-3.5 text-blue-500" />
                                          ) : subtask.status === "need-help" ? (
                                            <CircleAlert className="h-3.5 w-3.5 text-yellow-500" />
                                          ) : subtask.status === "failed" ? (
                                            <CircleX className="h-3.5 w-3.5 text-red-500" />
                                          ) : (
                                            <Circle className="text-muted-foreground h-3.5 w-3.5" />
                                          )}
                                        </motion.div>
                                      </AnimatePresence>
                                    </motion.div>

                                    <span
                                      className={`cursor-pointer text-sm ${subtask.status === "completed" ? "text-muted-foreground line-through" : ""}`}
                                    >
                                      {subtask.title}
                                    </span>
                                  </motion.div>

                                  <AnimatePresence mode="wait">
                                    {isSubtaskExpanded && (
                                      <motion.div 
                                        className="text-muted-foreground border-foreground/20 mt-1 ml-1.5 border-l border-dashed pl-5 text-xs overflow-hidden"
                                        variants={subtaskDetailsVariants as unknown as Variants}
                                        initial="hidden"
                                        animate="visible"
                                        exit="hidden"
                                        layout
                                      >
                                        <p className="py-1">{subtask.description}</p>
                                        {subtask.tools && subtask.tools.length > 0 && (
                                          <div className="mt-0.5 mb-1 flex flex-wrap items-center gap-1.5">
                                            <span className="text-muted-foreground font-medium">
                                              MCP Servers:
                                            </span>
                                            <div className="flex flex-wrap gap-1">
                                              {subtask.tools.map((tool, idx) => (
                                                <motion.span
                                                  key={idx}
                                                  className="bg-secondary/40 text-secondary-foreground rounded px-1.5 py-0.5 text-[10px] font-medium shadow-sm"
                                                  initial={{ opacity: 0, y: -5 }}
                                                  animate={{ 
                                                    opacity: 1, 
                                                    y: 0,
                                                    transition: {
                                                      duration: 0.2,
                                                      delay: idx * 0.05
                                                    }
                                                  }}
                                                  whileHover={{ 
                                                    y: -1, 
                                                    backgroundColor: "rgba(0,0,0,0.1)",
                                                    transition: { duration: 0.2 } 
                                                  }}
                                                >
                                                  {tool}
                                                </motion.span>
                                              ))}
                                            </div>
                                          </div>
                                        )}
                                      </motion.div>
                                    )}
                                  </AnimatePresence>
                                </motion.li>
                              );
                            })}
                          </ul>
                        </motion.div>
                      )}
                    </AnimatePresence>
                  </motion.li>
                );
              }))}
            </ul>
          </div>
        </LayoutGroup>
      </motion.div>
    </div>
  );
}