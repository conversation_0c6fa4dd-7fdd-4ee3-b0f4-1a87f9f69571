"use client"

import { motion, useInView, useMotionValue, useSpring } from "framer-motion"
import { useEffect, useRef } from "react"

interface AnimatedCounterProps {
  value: number
  duration?: number
  suffix?: string
  className?: string
}

export function AnimatedCounter({ 
  value, 
  duration = 2, 
  suffix = "", 
  className = "" 
}: AnimatedCounterProps) {
  const ref = useRef<HTMLElement | null>(null)
  const motionValue = useMotionValue(0)
  const springValue = useSpring(motionValue, { 
    damping: 60, 
    stiffness: 100,
    duration: duration * 1000
  })
  const isInView = useInView(ref, { once: true, margin: "0px 0px -100px 0px" })

  useEffect(() => {
    if (isInView) {
      motionValue.set(value)
    }
  }, [motionValue, isInView, value])

  useEffect(() => {
    const unsubscribe = springValue.on("change", (latest) => {
      if (ref.current instanceof HTMLElement) {
        const displayValue = Math.floor(latest as number)
        ref.current.textContent = displayValue.toLocaleString() + suffix
      }
    })

    return () => unsubscribe()
  }, [springValue, suffix])

  return (
    <motion.span
      ref={ref}
      className={className}
      initial={{ opacity: 0 }}
      animate={isInView ? { opacity: 1 } : {}}
      transition={{ duration: 0.5 }}
    >
      0{suffix}
    </motion.span>
  )
}

export function AnimatedProgress({ 
  value, 
  max = 100, 
  className = "",
  barClassName = ""
}: {
  value: number
  max?: number
  className?: string
  barClassName?: string
}) {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-50px" })
  const percentage = (value / max) * 100

  return (
    <div ref={ref} className={`w-full bg-gray-200 rounded-full h-2 ${className}`}>
      <motion.div
        className={`h-2 bg-gradient-to-r from-teal-500 to-blue-400 rounded-full ${barClassName}`}
        initial={{ width: "0%" }}
        animate={isInView ? { width: `${percentage}%` } : {}}
        transition={{ duration: 1.5, ease: "easeOut" }}
      />
    </div>
  )
}