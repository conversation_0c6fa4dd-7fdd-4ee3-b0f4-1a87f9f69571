"use client"

import * as React from "react"
import { useState, useRef } from "react"
import { Mic, Send, CheckCircle, MapPin, Upload, X } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import Image from "next/image"
import { useGeminiLive } from "@/hooks/use-gemini-live"
import { motion, useInView, AnimatePresence } from "framer-motion"
import { FoundersHuddle } from "@/components/founders-huddle"
import AgentPlan from "@/components/ui/agent-plan"
import { initializeCrossTabCommunication, sendFABState } from "@/lib/cross-tab-communication"

export function CTASection() {
  const [email, setEmail] = useState("")
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [showVoiceAgent, setShowVoiceAgent] = useState(false)
  const [showMaePlan, setShowMaePlan] = useState(false)
  const [isFloating, setIsFloating] = useState(false)
  const [showFloatingOptions, setShowFloatingOptions] = useState(false)
  const [buttonStartPos, setButtonStartPos] = useState({ x: 0, y: 0 })
  const [uploadingFile, setUploadingFile] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<Array<{uri: string, name: string, mimeType: string}>>([])
  const {
    isRecording,
    status,
    error,
    isConnected,
    startRecording,
    stopRecording,
    sendFile,
    initClient,
    reinitWithFiles
  } = useGeminiLive({
    uploadedFiles,
    // userId: user?.id,
    // userEmail: user?.email
  })

  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  // Initialize the client and cross-tab communication when component mounts
  React.useEffect(() => {
    initClient()

    // Initialize cross-tab communication for Mae context sharing
    const communicator = initializeCrossTabCommunication()
    console.log('🔗 Cross-tab communication initialized for landing page Mae')

    // Listen for FAB reset events
    const handleFABReset = () => {
      console.log('🎈 FAB reset received, resetting CTA section state')
      setIsFloating(false)
      setShowVoiceAgent(false)
      setShowFloatingOptions(false)
    }

    window.addEventListener('mae-fab-reset', handleFABReset)

    return () => {
      if (communicator) {
        communicator.destroy()
      }
      window.removeEventListener('mae-fab-reset', handleFABReset)
    }
  }, [initClient])

  const handleMicClick = async (event?: React.MouseEvent) => {
    console.log('🎤 Mic button clicked, isRecording:', isRecording);

    // If not floating yet, trigger the floating animation
    if (!isFloating) {
      // Capture the button's current position more accurately
      if (event) {
        const button = (event.currentTarget as HTMLElement);
        const rect = button.getBoundingClientRect();
        setButtonStartPos({
          x: rect.left + rect.width / 2,
          y: rect.top + rect.height / 2
        });
      }
      setIsFloating(true);
      // Don't automatically show voice agent in main area when floating
      // Voice agent will be controlled by the floating button

      // Notify global FAB to appear and sync across tabs
      sendFABState(true, true)
      window.dispatchEvent(new CustomEvent('mae-fab-state-change', {
        detail: { visible: true, floating: true }
      }));
      return;
    }

    if (isRecording) {
      console.log('🛑 Stopping recording...');
      stopRecording();
      // Only hide voice agent if not in floating mode
      if (!isFloating) {
        setShowVoiceAgent(false);
      }
    } else {
      console.log('🎤 Starting recording...');
      // Only show voice agent in main area if not in floating mode
      if (!isFloating) {
        setShowVoiceAgent(true);
      }
      
      try {
        // Check if microphone is supported
        if (!navigator.mediaDevices?.getUserMedia) {
          throw new Error('Microphone access is not supported in this browser. Please use Chrome, Firefox, or Safari.');
        }

        // Check current permission status
        try {
          const permissionStatus = await navigator.permissions.query({ name: 'microphone' as PermissionName });
          
          if (permissionStatus.state === 'denied') {
            throw new Error('Microphone access was denied. Please enable microphone permissions in your browser settings and refresh the page.');
          }
        } catch (permError) {
          // Some browsers might not support permissions API, continue anyway
          console.warn('Could not check microphone permissions:', permError);
        }
        
        // Custom initial message to trigger Mae's email-first workflow
        const initialMessage = "Hi Mae! I just clicked the microphone button and I'm ready to get personalized pediatric assistance. Please follow your email-first workflow to help me.";
        
        await startRecording(initialMessage);
        console.log('✅ Recording started successfully with custom system instruction');
      } catch (error) {
        console.error('❌ Failed to start recording:', error);
        setShowVoiceAgent(false);
        
        // Show user-friendly error message
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        if (errorMessage.includes('Permission denied') || errorMessage.includes('denied')) {
          alert('🎤 Microphone Permission Required\n\nTo talk with Mae, please:\n1. Click "Allow" when your browser asks for microphone access\n2. Or enable microphone permissions in your browser settings\n3. Refresh the page and try again');
        } else {
          alert(`❌ Voice Chat Error\n\n${errorMessage}`);
        }
      }
    }
  }

  // Convert file to base64 like the working extension
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => {
        const result = reader.result as string;
        // Remove data URL prefix (e.g., "data:image/jpeg;base64,")
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = error => reject(error);
    });
  };

  const handleFileUpload = async () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*,audio/*,.pdf,.doc,.docx,.txt,.csv,.json';
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (!file) return;

      console.log('📁 File selected:', file.name);
      setUploadingFile(true);

      try {
        // Convert file to base64 like the working extension
        const base64Data = await fileToBase64(file);
        const mimeType = file.type || 'application/octet-stream';
        
        console.log('📁 File converted to base64, sending to Mae...');
        
        // Send file directly to Mae like the working extension
        if (isConnected) {
          sendFile(base64Data, mimeType);
          console.log(`✅ File sent to Mae: ${file.name} (${formatFileSize(file.size)})`);
          
          // Send follow-up message
          const acknowledgmentMessage = `I just uploaded a file called "${file.name}". Please acknowledge that you received it and ask if I would like you to analyze it in detail.`;
          
          // Wait a moment then send the text message
          setTimeout(async () => {
            if (!isRecording) {
              await startRecording(acknowledgmentMessage);
              setShowVoiceAgent(true);
            }
          }, 500);
          
        } else {
          console.log('❌ Mae not connected - cannot send file');
        }
        
      } catch (error) {
        console.error('❌ File processing error:', error);
        console.log('📁 File upload failed:', error instanceof Error ? error.message : 'Unknown error');
      } finally {
        setUploadingFile(false);
      }
    };
    input.click();
  }

  // Helper function to format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const resetToOriginal = () => {
    setIsFloating(false);
    setShowVoiceAgent(false);
    setShowFloatingOptions(false);
    if (isRecording) {
      stopRecording();
    }

    // Notify global FAB to hide and sync across tabs
    sendFABState(false, false)
    window.dispatchEvent(new CustomEvent('mae-fab-state-change', {
      detail: { visible: false, floating: false }
    }));
  }

  const handleEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (email) {
      setIsSubmitted(true)
      // Reset after 3 seconds
      setTimeout(() => {
        setIsSubmitted(false)
        setEmail("")
      }, 3000)
    }
  }

  return (
    <section ref={ref} className="py-24">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <motion.div 
            initial={{ opacity: 0, y: 50 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, ease: "easeOut" }}
            className="text-center mb-12 font-inter font-light gradient-to-r from-teal-500 to-blue-400 space-y-4 text-white"
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={isInView ? { opacity: 1, scale: 1 } : {}}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Badge variant="outline" className="mb-4 w-fit h-8 mx-auto px-10 text-md bg-gradient-to-r from-teal-500 to-blue-400 text-white font-inter font-medium">
                Try It Now
              </Badge>
            </motion.div>
            <motion.h2 
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="text-2xl md:text-3xl font-inter font-light tracking-tight text-black dark:text-white"
            >
              Experience AI-Powered Pediatric Care
            </motion.h2>
            <motion.p 
              initial={{ opacity: 0, y: 30 }}
              animate={isInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="text-muted-foreground font-inter font-light max-w-3xl mx-auto leading-relaxed mb-10"
            >
              Just speak your question and get AI-powered answers in seconds. 
              No appointment needed.
            </motion.p>
          </motion.div>

          {/* Onboarding Information */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.6 }}
          >
            <Card className="mb-10 healthcare-card">
              <CardHeader className="text-center">
                <CardTitle className="text-xl font-inter font-light">Get Started with Mae</CardTitle>
                <CardDescription className="font-inter font-light">
                  Mae will guide you through a personalized onboarding process
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid md:grid-cols-3 gap-4">
                  <div className="text-center space-y-2">
                    <div className="w-12 h-12 mx-auto bg-teal-100 rounded-full flex items-center justify-center">
                      <span className="text-teal-600 font-semibold">1</span>
                    </div>
                    <h4 className="font-medium">Voice Setup</h4>
                    <p className="text-sm text-muted-foreground">Chat with Mae to create your account and family profile</p>
                  </div>
                  <div className="text-center space-y-2">
                    <div className="w-12 h-12 mx-auto bg-teal-100 rounded-full flex items-center justify-center">
                      <span className="text-teal-600 font-semibold">2</span>
                    </div>
                    <h4 className="font-medium">Family Info</h4>
                    <p className="text-sm text-muted-foreground">Add your children and their health information</p>
                  </div>
                  <div className="text-center space-y-2">
                    <div className="w-12 h-12 mx-auto bg-teal-100 rounded-full flex items-center justify-center">
                      <span className="text-teal-600 font-semibold">3</span>
                    </div>
                    <h4 className="font-medium">Personalized Care</h4>
                    <p className="text-sm text-muted-foreground">Get tailored health guidance for your family</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Voice Demo */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.8 }}
          >
            <Card className="mb-10 healthcare-card hover:border-2 hover:border-teal-500 transition-all duration-300">
              <CardHeader className="text-center font-inter font-tight font-light">
                <CardTitle className="text-xl font-inter font-light">Try Mae now — just speak your question</CardTitle>
                <CardDescription className="font-inter font-light">
                  Ask about your child&apos;s health or learn about the onboarding process
                </CardDescription>
              </CardHeader>
              <CardContent className="flex flex-col items-center space-y-6">
                <div className="relative flex flex-col items-center space-y-4">
                  {/* Voice Agent Interface */}
                  {showVoiceAgent && (
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      exit={{ opacity: 0, scale: 0.8 }}
                      transition={{ duration: 0.3 }}
                      className="w-full max-w-md p-6 bg-card dark:bg-card rounded-xl relative z-30"
                    >
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-teal-800 dark:text-teal-200">Chat with Mae</h3>
                        <motion.button
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          onClick={() => {
                            setShowVoiceAgent(false)
                            stopRecording()
                          }}
                          className="w-8 h-8 bg-red-500 text-white rounded-full text-sm flex items-center justify-center transition-all shadow-lg"
                        >
                          ×
                        </motion.button>
                      </div>

                      {/* Audio visualization */}
                      <div className="flex items-center justify-center mb-4">
                        <motion.div 
                          animate={isRecording ? { 
                            scale: [1, 1.1, 1],
                            boxShadow: ["0 0 0 0 rgba(20, 184, 166, 0.4)", "0 0 0 20px rgba(20, 184, 166, 0)", "0 0 0 0 rgba(20, 184, 166, 0)"]
                          } : {}}
                          transition={{ duration: 1.5, repeat: Infinity }}
                          className={`w-16 h-16 rounded-full border-4 border-teal-400 flex items-center justify-center ${isRecording ? 'bg-teal-100' : 'bg-gray-100'}`}
                        >
                          <Mic className={`w-8 h-8 ${isRecording ? 'text-teal-600' : 'text-gray-400'}`} />
                        </motion.div>
                      </div>

                      <div className="text-center">
                        <p className="text-sm text-teal-700 mb-2">
                          {isRecording ? "Listening... Speak your question" : isConnected ? "Ready to listen" : "Connecting..."}
                        </p>
                        <motion.div
                          whileHover={{ scale: 1.05 }}
                          whileTap={{ scale: 0.95 }}
                        >
                          <Button
                            onClick={handleMicClick}
                            disabled={!isConnected}
                            className={`${isRecording ? 'bg-red-500 hover:bg-red-600' : 'bg-teal-500 hover:bg-teal-600'} text-white disabled:opacity-50 disabled:cursor-not-allowed`}
                          >
                            {isRecording ? 'Stop Recording' : 'Start Recording'}
                          </Button>
                        </motion.div>
                      </div>
                    </motion.div>
                  )}

                  {/* Main Voice Button */}
                  {(!showVoiceAgent || isFloating) && (
                    <div className="relative flex items-center justify-center" style={{ width: 80, height: 80 }}>
                      {/* Rotating gradient ring */}
                      <motion.div
                        className="absolute inset-0 flex items-center justify-center"
                        animate={isFloating ? {
                          rotate: 360,
                          opacity: 0.3
                        } : {
                          rotate: 360,
                          opacity: 1
                        }}
                        transition={{
                          rotate: { repeat: Infinity, duration: 3, ease: "linear" },
                          opacity: { duration: 0.3 }
                        }}
                        style={{ width: 80, height: 80 }}
                      >
                        <svg width="80" height="80" viewBox="0 0 80 80" fill="none">
                          <defs>
                            <linearGradient id="mic-gradient" x1="0" y1="0" x2="80" y2="80" gradientUnits="userSpaceOnUse">
                              <stop stopColor="#14b8a6" />
                              <stop offset="1" stopColor="#3b82f6" />
                            </linearGradient>
                          </defs>
                          <circle cx="40" cy="40" r="36" stroke="url(#mic-gradient)" strokeWidth="6" fill="none" />
                        </svg>
                      </motion.div>
                      {/* Pulsing glow */}
                      <motion.div
                        className="absolute inset-0 flex items-center justify-center"
                        animate={isFloating ? {
                          boxShadow: "0 0 0 0 rgba(20,184,166,0.1)"
                        } : {
                          boxShadow: [
                            "0 0 0 0 rgba(20,184,166,0.25)",
                            "0 0 0 20px rgba(20,184,166,0.08)",
                            "0 0 0 0 rgba(20,184,166,0.0)"
                          ]
                        }}
                        transition={{
                          boxShadow: isFloating ? { duration: 0.3 } : { duration: 2, repeat: Infinity }
                        }}
                        style={{ width: 80, height: 80, borderRadius: '50%' }}
                      />
                      {/* Main Button */}
                      <motion.div
                        animate={isFloating ? {
                          opacity: 0.4,
                          scale: 0.95
                        } : {
                          opacity: 1,
                          scale: 1
                        }}
                        transition={{ duration: 0.3, ease: "easeOut" }}
                      >
                        <Button
                          size="icon"
                          onClick={handleMicClick}
                          disabled={isFloating}
                          className={`relative z-20 w-16 h-16 rounded-full transition-all duration-300 border-2 shadow-xl flex items-center justify-center ${
                            isFloating
                              ? 'border-gray-400 bg-gray-400 cursor-not-allowed'
                              : 'border-teal-500 bg-primary hover:bg-primary/90 cursor-pointer'
                          }`}
                        >
                          <motion.span
                            initial={{ scale: 0, opacity: 0 }}
                            animate={{ scale: 1, opacity: 1 }}
                            transition={{ type: "spring", stiffness: 400, damping: 20 }}
                            key={isRecording ? 'recording' : 'idle'}
                          >
                            <Mic className={`w-8 h-8 ${isFloating ? 'text-gray-600' : 'text-primary-foreground'}`} />
                          </motion.span>
                        </Button>
                      </motion.div>
                    </div>
                  )}
                </div>
              <div className="text-center text-sm font-inter font-light text-black dark:text-white space-y-2">
                {error && (
                  <p className="text-xs text-red-500 bg-red-50 p-2 rounded">
                    Error: {error}
                  </p>
                )}
                {!showVoiceAgent && (
                  <p className="text-sm font-inter font-light text-black dark:text-white">
                    Experience our AI-powered pediatric voice assistant
                  </p>
                )}
                
                {/* Mae's Plan Toggle */}
                {(showVoiceAgent || isRecording) && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className="mt-4"
                  >
                    {/* <button
                      onClick={() => setShowMaePlan(!showMaePlan)}
                      className="text-xs text-teal-600 hover:text-teal-700 font-medium underline"
                    >
                      {showMaePlan ? 'Hide' : 'Show'} Mae's Current Plan
                    </button> */}
                  </motion.div>
                )}

                {/* Debug info */}
                {/* {process.env.NODE_ENV === 'development' && (
                  <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded mt-2 space-y-1">
                    <div>Voice Agent: {showVoiceAgent ? 'Active' : 'Inactive'}</div>
                    <div>API Key: Configured via secure server-side proxy</div>
                  </div>
                )} */}
              </div>
            </CardContent>
          </Card>
          </motion.div>

          {/* Mae's Plan Component */}
          <AnimatePresence>
            {showMaePlan && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.3 }}
                className="mt-8"
              >
                <Card className="healthcare-card border-teal-200">
                  <CardHeader>
                    <CardTitle className="text-lg font-inter font-medium text-teal-800 dark:text-teal-200 flex items-center gap-2">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                      </svg>
                      Mae&apos;s Current Plan
                    </CardTitle>
                    <CardDescription className="text-sm text-teal-600">
                      See what Mae is working on to help you
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="max-h-96 overflow-hidden">
                      <AgentPlan />
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Email Capture */}
          {/* <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 1.0 }}
          > */}
            {/* <Card className="mb-10 healthcare-card hover:border-2 hover:border-teal-500 transition-all duration-300">
              <CardHeader className="text-center font-inter font-light">
                <CardTitle className="font-inter font-light">Get Your AI-Powered Answer</CardTitle>
                <CardDescription>
                  Almost there — where should we send your personalized response?
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleEmailSubmit} className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
                  <motion.div
                    whileFocus={{ scale: 1.02 }}
                    className="flex-1"
                  >
                    <Input
                      type="email"
                      placeholder="Enter your email"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      className="flex-1"
                      required
                    />
                  </motion.div>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button className="bg-[#14b8a6] text-white cursor-pointer" type="submit" disabled={isSubmitted}>
                      {isSubmitted ? (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          className="flex items-center"
                        >
                          <CheckCircle className="w-4 h-4 mr-2 text-white" />
                          Sent!
                        </motion.div>
                      ) : (
                        <>
                          <Send className="w-4 h-4 mr-2 bg-[#14b8a6] text-white" />
                          Get Answer
                        </>
                      )}
                    </Button>
                  </motion.div>
                </form>
              </CardContent>
            </Card>
          </motion.div> */}

          {/* Waitlist CTA */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 1.2 }}
          >
            <Card className="mb-10 healthcare-card hover:border-2 hover:border-teal-500 transition-all duration-300">
              <CardContent className="text-center py-12 space-y-6">
                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  animate={isInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.6, delay: 1.4 }}
                  className="space-y-4"
                >
                  <h3 className="text-2xl font-inter font-light">
                    Join Our Early Access Program
                  </h3>
                  <p className="text-sm text-muted-foreground font-inter font-light max-w-2xl mx-auto">
                    Be among the first 5,000 families to experience the future of pediatric care. 
                    Get 1 month premium access completely free.
                  </p>
                </motion.div>
                
                <motion.div 
                  initial={{ opacity: 0, y: 20 }}
                  animate={isInView ? { opacity: 1, y: 0 } : {}}
                  transition={{ duration: 0.6, delay: 1.6 }}
                  className="flex flex-col sm:flex-row gap-4 justify-center"
                >
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button asChild size="lg" className="text-md px-8 btn-healthcare bg-[#cbfbf1] font-inter font-medium text-black">
                      <Link href="/contact">
                        Join Early Access
                      </Link>
                    </Button>
                  </motion.div>
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <Button asChild size="lg" variant="outline" className="text-md px-8 btn-healthcare border-teal-500 font-inter font-medium text-teal-600 hover:bg-teal-50">
                      <Link href="/onboarding">
                        Start Onboarding with Mae
                      </Link>
                    </Button>
                  </motion.div>
                </motion.div>
                
                <motion.div 
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={isInView ? { opacity: 1, scale: 1 } : {}}
                  transition={{ duration: 0.6, delay: 1.8 }}
                  className="pt-4"
                >
                  <Badge variant="destructive" className="text-sm bg-gradient-to-r from-teal-500 to-blue-400 font-inter font-medium">
                    First 5,000 get 1 month premium free
                  </Badge>
                </motion.div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Founders Huddle */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={isInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 1.4 }}
          >
            <FoundersHuddle className="mb-10" />
          </motion.div>
        </div>
      </div>

      {/* Floating Action Button */}
      <AnimatePresence>
        {isFloating && (
          <motion.div
            className="fixed z-50"
            style={{
              right: '24px',
              bottom: '24px',
              transformOrigin: 'center center'
            }}
            initial={{
              opacity: 0,
              scale: 0.5,
              x: buttonStartPos.x - (typeof window !== 'undefined' ? window.innerWidth - 88 : 0),
              y: buttonStartPos.y - (typeof window !== 'undefined' ? window.innerHeight - 88 : 0)
            }}
            animate={{
              opacity: 1,
              scale: 1,
              x: 0,
              y: 0
            }}
            exit={{
              opacity: 0,
              scale: 0.2
            }}
            transition={{
              type: "spring",
              stiffness: 100,
              damping: 20,
              mass: 0.8,
              duration: 1.0
            }}
          >
            <div className="relative flex flex-col items-center">
              {/* Expanded Options - positioned absolutely to not push main button */}
              <AnimatePresence>
                {showFloatingOptions && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8, y: 10 }}
                    animate={{ opacity: 1, scale: 1, y: 0 }}
                    exit={{ opacity: 0, scale: 0.8, y: 10 }}
                    transition={{ duration: 0.2 }}
                    className="absolute bottom-20 flex flex-col items-center gap-3"
                  >
                    {/* Close Button */}
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={resetToOriginal}
                      className="w-12 h-12 bg-red-500 hover:bg-red-600 text-white rounded-full shadow-lg flex items-center justify-center"
                      title="Close Mae Assistant"
                    >
                      <X className="w-5 h-5" />
                    </motion.button>

                    {/* File Upload Button */}
                    <motion.button
                      whileHover={{ scale: uploadingFile ? 1 : 1.1 }}
                      whileTap={{ scale: uploadingFile ? 1 : 0.9 }}
                      onClick={uploadingFile ? undefined : handleFileUpload}
                      disabled={uploadingFile}
                      className={`w-12 h-12 ${uploadingFile ? 'bg-blue-300' : 'bg-blue-500 hover:bg-blue-600'} text-white rounded-full shadow-lg flex items-center justify-center ${uploadingFile ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                      title={uploadingFile ? "Uploading..." : "Upload File for Mae to Analyze"}
                    >
                      {uploadingFile ? (
                        <motion.div
                          animate={{ rotate: 360 }}
                          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                          className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                        />
                      ) : (
                        <Upload className="w-5 h-5" />
                      )}
                    </motion.button>

                    {/* Options Toggle Button */}
                    <motion.button
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      onClick={() => setShowFloatingOptions(!showFloatingOptions)}
                      className="w-12 h-12 bg-gray-600 hover:bg-gray-700 text-white rounded-full shadow-lg flex items-center justify-center"
                      title="Hide Options"
                    >
                      <motion.div
                        animate={{ rotate: 90 }}
                        transition={{ duration: 0.2 }}
                        className="text-white font-bold"
                      >
                        ⋯
                      </motion.div>
                    </motion.button>
                  </motion.div>
                )}
              </AnimatePresence>

              {/* Main Button Container - always stays in same position */}
              <div className="flex flex-col items-center gap-3">
                {/* Options Toggle Button - only show when options are hidden */}
                {!showFloatingOptions && (
                  <motion.button
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                    onClick={() => setShowFloatingOptions(true)}
                    className="w-12 h-12 bg-gray-600 hover:bg-gray-700 text-white rounded-full shadow-lg flex items-center justify-center"
                    title="More Options"
                  >
                    <motion.div
                      animate={{ rotate: 0 }}
                      transition={{ duration: 0.2 }}
                      className="text-white font-bold"
                    >
                      ⋯
                    </motion.div>
                  </motion.button>
                )}

                {/* Main Floating Button */}
                <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleMicClick}
              className="w-16 h-16 bg-transparent hover:bg-gray-900 text-white rounded-full shadow-xl flex items-center justify-center"
              animate={isRecording ? {
                scale: [1, 1.1, 1],
                boxShadow: [
                  "0 4px 20px rgba(20, 184, 166, 0.3)",
                  "0 4px 30px rgba(20, 184, 166, 0.6)",
                  "0 4px 20px rgba(20, 184, 166, 0.3)"
                ]
              } : {}}
              transition={{ duration: 1, repeat: isRecording ? Infinity : 0 }}
              title={isRecording ? "Stop Recording" : "Start Recording"}
            >
              <Image
                src="/OKdarkTsp.png"
                alt="Our Kidz Logo"
                width={32}
                height={32}
                className="w-8 h-8 object-contain"
              />
            </motion.button>
              </div>

              {/* Recording Indicator */}
              {isRecording && (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center"
                >
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                    className="w-3 h-3 bg-white rounded-full"
                  />
                </motion.div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </section>
  )
}
