"use client"

import * as React from "react"
import { Globe } from "lucide-react"
import { Button } from "@/components/ui/button"

declare global {
  interface Window {
    google: {
      translate: {
        [x: string]: any
        GoogleTranslateElement: new (
          config: {
            pageLanguage: string;
            includedLanguages: string;
            autoDisplay: boolean;
          },
          elementId: string
        ) => void;
        translate: {
          restore: () => void;
        };
      };
    };
    googleTranslateElementInit: () => void;
  }
}

export function LanguageToggle() {
  const [mounted, setMounted] = React.useState(false)
  const [currentLang, setCurrentLang] = React.useState('en')
  const [isTranslating, setIsTranslating] = React.useState(false)

  React.useEffect(() => {
    setMounted(true)

    if (mounted) {
      // Initialize Google Translate
      const cookieName = 'googtrans'
      // Clear any existing translation cookies
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname}`
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${window.location.hostname}`
      // Set initial language explicitly to English
      document.cookie = `${cookieName}=/en/en; path=/; domain=${window.location.hostname}`

      // Create container for translate element
      const translateDiv = document.createElement('div')
      translateDiv.id = 'google_translate_element'
      translateDiv.style.display = 'none'
      document.body.appendChild(translateDiv)

      // Initialize translate widget
      window.googleTranslateElementInit = () => {
        new window.google.translate.TranslateElement(
          {
            pageLanguage: 'en',
            includedLanguages: 'en,es',
            autoDisplay: false,
          },
          'google_translate_element'
        )
      }

      // Add translate script
      const script = document.createElement('script')
      script.src =
        'https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit'
      script.async = true
      document.body.appendChild(script)

      // Add styles
      const style = document.createElement('style')
      style.textContent = `
        #google_translate_element,
        .skiptranslate,
        .goog-te-banner-frame {
          display: none !important;
        }
        body {
          top: 0 !important;
        }
        .goog-text-highlight {
          background-color: transparent !important;
          box-shadow: none !important;
        }
        /* Force original font */
        body {
          font-family: var(--font-sans) !important;
        }
        /* Override Google Translate font changes */
        .goog-te-gadget *,
        .goog-te-menu-value *,
        div[style*="font-size"],
        span[style*="font-size"],
        div[style*="font-family"],
        span[style*="font-family"] {
          font-family: inherit !important;
          font-size: inherit !important;
          line-height: inherit !important;
        }
        /* Hide Google Translate elements */
        .VIpgJd-ZVi9od-ORHb-OEVmcd,
        .VIpgJd-ZVi9od-SmfZ-OEVmcd,
        .goog-te-spinner-pos,
        .goog-te-spinner {
          display: none !important;
        }
      `
      document.head.appendChild(style)

      return () => {
        // Cleanup
        const translateElement = document.getElementById('google_translate_element')
        if (translateElement?.parentNode) {
          translateElement.parentNode.removeChild(translateElement)
        }
        if (style.parentNode) {
          style.parentNode.removeChild(style)
        }
        if (script.parentNode) {
          script.parentNode.removeChild(script)
        }
        // Reset the initialization function
        window.googleTranslateElementInit = () => {}
      }
    }
  }, [mounted])

  const handleLanguageChange = async (lang: string) => {
    try {
      setIsTranslating(true)

      const cookieName = 'googtrans'
      const domain = window.location.hostname

      // Clear existing cookies first
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${domain}`
      document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${domain}`

      // Set new language explicitly
      if (lang === 'en') {
        document.cookie = `${cookieName}=/en/en; path=/; domain=${domain}`
        setCurrentLang('en')
      } else if (lang === 'es') {
        document.cookie = `${cookieName}=/en/es; path=/; domain=${domain}`
        setCurrentLang('es')
      }

      // For Spanish or if restore method fails
      const changeLanguage = () => {
        const select = document.querySelector('.goog-te-combo') as HTMLSelectElement
        if (select) {
          select.value = lang
          select.dispatchEvent(new Event('change'))
          return true
        }
        return false
      }

      let attempts = 0
      const maxAttempts = 5

      const tryChangeLanguage = async () => {
        while (attempts < maxAttempts) {
          if (changeLanguage()) {
            setCurrentLang(lang)
            return true
          }
          await new Promise(resolve => setTimeout(resolve, 500))
          attempts++
        }
        return false
      }

      const success = await tryChangeLanguage()
      if (!success) {
        if (lang === 'en') {
          const iframe = document.querySelector('.goog-te-banner-frame') as HTMLIFrameElement
          if (iframe) {
            iframe.remove()
          }
          window.location.reload()
        } else {
          console.error('Failed to change language after multiple attempts')
        }
      }
    } catch (error) {
      console.error('Translation error:', error)
      if (lang === 'en') {
        window.location.reload()
      }
    } finally {
      setIsTranslating(false)
    }
  }

  const handleToggle = () => {
    const newLang = currentLang === 'en' ? 'es' : 'en'
    handleLanguageChange(newLang)
  }

  if (!mounted) {
    return null
  }

  const isEnglish = currentLang === 'en'

  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={handleToggle}
      disabled={isTranslating}
      className="relative h-10 w-10 rounded-full cursor-pointer border-transparent bg-background hover:bg-accent hover:text-accent-foreground"
      aria-label="Toggle language"
    >
      <div className="flex items-center justify-center">
        <Globe className="h-4 w-4 mr-1" />
        <span className="text-xs font-medium">
          {isTranslating ? '...' : isEnglish ? 'EN' : 'ES'}
        </span>
      </div>
    </Button>
  )
}
