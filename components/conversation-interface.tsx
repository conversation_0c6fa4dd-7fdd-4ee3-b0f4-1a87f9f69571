"use client"

import React, { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { Send, Share, Calendar, User, Bot, UserPlus } from 'lucide-react'
import { handleEmailConversation } from '@/lib/email-function-tool'
import { InviteDialog } from './invite-dialog'

interface ConversationMessage {
  id: string
  type: 'user' | 'ai'
  content: string
  timestamp: Date
}

interface ConversationInterfaceProps {
  initialMessages?: ConversationMessage[]
  onNewMessage?: (message: ConversationMessage) => void
}

export function ConversationInterface({ 
  initialMessages = [], 
  onNewMessage 
}: ConversationInterfaceProps) {
  const [messages, setMessages] = useState<ConversationMessage[]>(initialMessages)
  const [inputValue, setInputValue] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [showEmailShare, setShowEmailShare] = useState(false)
  const [emailAddress, setEmailAddress] = useState('')
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!inputValue.trim() || isLoading) return

    const userMessage: ConversationMessage = {
      id: `user-${Date.now()}`,
      type: 'user',
      content: inputValue.trim(),
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setInputValue('')
    setIsLoading(true)

    try {
      // Call the actual AI API
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage.content,
          conversation_history: messages.map(msg => ({
            role: msg.type === 'user' ? 'user' : 'assistant',
            content: msg.content
          }))
        })
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()

      const aiMessage: ConversationMessage = {
        id: `ai-${Date.now()}`,
        type: 'ai',
        content: data.response || 'I apologize, but I encountered an issue processing your request. Please try again.',
        timestamp: new Date()
      }

      setMessages(prev => [...prev, aiMessage])
      setIsLoading(false)

      if (onNewMessage) {
        onNewMessage(aiMessage)
      }
    } catch (error) {
      console.error('Error getting AI response:', error)

      const errorMessage: ConversationMessage = {
        id: `ai-${Date.now()}`,
        type: 'ai',
        content: 'I apologize, but I\'m having trouble connecting right now. Please check your internet connection and try again.',
        timestamp: new Date()
      }

      setMessages(prev => [...prev, errorMessage])
      setIsLoading(false)
    }
  }

  const handleEmailShare = async () => {
    if (!emailAddress.trim()) return

    const conversationText = messages
      .map(msg => `${msg.type === 'user' ? 'Your Question' : "Mae's Response"}: ${msg.content}`)
      .join('\n\n')

    try {
      await handleEmailConversation({
        recipient_email: emailAddress,
        user_question: messages.find(m => m.type === 'user')?.content || 'Conversation shared',
        ai_response: messages.find(m => m.type === 'ai')?.content || 'No AI response yet',
        subject: 'Shared Conversation from Our Kidz',
        include_context: true,
        conversation_context: conversationText
      })
      
      setShowEmailShare(false)
      setEmailAddress('')
      alert('Conversation shared successfully!')
    } catch (error) {
      alert('Failed to share conversation. Please try again.')
    }
  }

  return (
    <div className="flex flex-col h-full max-w-4xl mx-auto">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <img src="/OKdarkTsp.png" alt="Our Kidz Logo" className="w-12 h-12" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Our Kidz</h1>
              <p className="text-sm text-gray-600">The Smart, Agentic Companion Built for Today&apos;s Modern Families</p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowEmailShare(true)}
              className="flex items-center gap-2"
            >
              <Share className="w-4 h-4" />
              Share
            </Button>
            <InviteDialog>
              <Button size="sm" className="bg-teal-500 hover:bg-teal-600 flex items-center gap-2">
                <UserPlus className="w-4 h-4" />
                Invite Friends
              </Button>
            </InviteDialog>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.length === 0 && (
          <div className="text-center py-12">
            <div className="bg-gradient-to-r from-teal-50 to-blue-50 border border-teal-200 rounded-lg p-8 max-w-2xl mx-auto">
              <Bot className="w-12 h-12 text-teal-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Grow your Community, Get Rewarded
              </h3>
              <p className="text-gray-600 mb-4">
                Got a friend who&apos;s juggling diapers, doctor visits, or teen drama?
                Every signup adds one free month to each of your subscriptions and there&apos;s
                always more to share with a friend.
              </p>
              <p className="text-sm text-gray-500 italic">
                Start a conversation with Mae by typing your question below!
              </p>
            </div>
          </div>
        )}

        {messages.map((message) => (
          <div key={message.id} className="flex gap-3">
            <div className="flex-shrink-0">
              {message.type === 'user' ? (
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-white" />
                </div>
              ) : (
                <div className="w-8 h-8 bg-teal-500 rounded-full flex items-center justify-center">
                  <Bot className="w-4 h-4 text-white" />
                </div>
              )}
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <span className="font-medium text-sm">
                  {message.type === 'user' ? 'Your Question:' : "Mae&apos;s Response:"}
                </span>
                <span className="text-xs text-gray-500">
                  {message.timestamp.toLocaleString()}
                </span>
              </div>
              <Card className={message.type === 'user' ? 'bg-blue-50 border-blue-200' : 'bg-teal-50 border-teal-200'}>
                <CardContent className="p-4">
                  <p className="text-gray-800 whitespace-pre-wrap">{message.content}</p>
                </CardContent>
              </Card>
            </div>
          </div>
        ))}

        {isLoading && (
          <div className="flex gap-3">
            <div className="w-8 h-8 bg-teal-500 rounded-full flex items-center justify-center">
              <Bot className="w-4 h-4 text-white" />
            </div>
            <div className="flex-1">
              <div className="font-medium text-sm mb-1">Mae&apos;s Response:</div>
              <Card className="bg-teal-50 border-teal-200">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-teal-500"></div>
                    <span className="text-gray-600">Mae is thinking...</span>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Input Form */}
      <div className="border-t border-gray-200 p-4">
        <form onSubmit={handleSubmit} className="flex gap-2">
          <Textarea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="Ask Mae about your child's health, development, or any parenting concerns..."
            className="flex-1 min-h-[60px] resize-none"
            disabled={isLoading}
          />
          <Button 
            type="submit" 
            disabled={!inputValue.trim() || isLoading}
            className="self-end"
          >
            <Send className="w-4 h-4" />
          </Button>
        </form>
      </div>

      {/* Email Share Modal */}
      {showEmailShare && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-md">
            <CardContent className="p-6">
              <h3 className="text-lg font-medium mb-4">Share Conversation</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address
                  </label>
                  <input
                    type="email"
                    value={emailAddress}
                    onChange={(e) => setEmailAddress(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div className="flex gap-2 justify-end">
                  <Button 
                    variant="outline" 
                    onClick={() => setShowEmailShare(false)}
                  >
                    Cancel
                  </Button>
                  <Button 
                    onClick={handleEmailShare}
                    disabled={!emailAddress.trim()}
                  >
                    Send Email
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
