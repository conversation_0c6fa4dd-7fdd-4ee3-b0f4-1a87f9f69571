"use client"

import * as React from "react"
import { useState, useEffect, useCallback } from "react"
import Image from "next/image"
import Link from "next/link"
import { ChevronRight, Play, Pause, MessageCircle, Clapperboard, Sparkles } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { motion, AnimatePresence } from "framer-motion"

const slides = [
  {
    id: 1,
    title: "Meet Mae...",
    subtitle: "AI-powered pediatric guidance at your fingertips",
    image: "/images/hero-slide-1.jpg",
    // buttonText: "Contact Us",
    // buttonLink: "/contact",
    type: "image",
    badge: "Trusted by 10,000+ families"
  },
  {
    id: 2,
    title: "Just speak into the phone",
    subtitle: "Get instant answers to your parenting questions",
    video: "/pickup-thephone.mp4",
    buttonText: "",
    buttonLink: "/ai",
    type: "video",
    badge: "Voice-powered AI"
  },
  {
    id: 3,
    title: "Are my children sleeping enough?",
    subtitle: "Track health patterns and get personalized insights",
    image: "/children-in-bed.jpg",
    // buttonText: "Contact Us",
    // buttonLink: "/contact",
    type: "image",
    badge: "Health tracking"
  },
]

export function HeroSection() {
  const [currentSlide, setCurrentSlide] = useState(0)
  const [isPlaying, setIsPlaying] = useState(true)

  const goToSlide = useCallback((index: number) => {
    setCurrentSlide(index)
  }, [])

  const nextSlide = useCallback(() => {
    setCurrentSlide((prev) => (prev + 1) % slides.length)
  }, [])

  useEffect(() => {
    if (!isPlaying) return
    const interval = setInterval(nextSlide, 6000)
    return () => clearInterval(interval)
  }, [nextSlide, isPlaying])

  const currentSlideData = slides[currentSlide]

  return (
    <section className="relative h-[600px] md:h-[700px] lg:h-[800px] w-full overflow-hidden">
      <AnimatePresence mode="wait">
        {slides.map((slide, index) => 
          index === currentSlide && (
            <motion.div
              key={slide.id}
              initial={{ opacity: 0, scale: 1.1 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.95 }}
              transition={{ 
                duration: 0.8,
                ease: [0.25, 0.46, 0.45, 0.94]
              }}
              className="absolute inset-0"
            >
              <motion.div 
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2, duration: 0.6 }}
                className="absolute inset-0 bg-gradient-to-r from-black/60 via-black/30 to-transparent z-10" 
              />

              {slide.type === "video" ? (
                <motion.div 
                  initial={{ scale: 1.1 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 1.2, ease: "easeOut" }}
                  className="relative w-full h-full"
                >
                  <video 
                    src={slide.video} 
                    autoPlay 
                    muted 
                    loop 
                    playsInline 
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute top-0 right-0 w-32 h-16 bg-black/30 z-5" />
                </motion.div>
              ) : (
                <motion.div
                  initial={{ scale: 1.1 }}
                  animate={{ scale: 1 }}
                  transition={{ duration: 1.2, ease: "easeOut" }}
                  className="relative w-full h-full"
                >
                  <Image
                    src={slide.image || "/placeholder.svg"}
                    alt={slide.title}
                    fill
                    className="object-cover object-center"
                    priority={index === 0}
                  />
                </motion.div>
              )}

              <div className="absolute inset-0 z-20 flex items-center">
                <div className="container mx-auto px-4">
                  <div className="max-w-2xl space-y-6">
                    <motion.div
                      initial={{ y: 50, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 0.4, duration: 0.8, ease: "easeOut" }}
                    >
                      <Badge variant="secondary" className="bg-gradient-to-r from-teal-500 to-blue-400 text-white w-fit h-8 mx-auto px-10 mb-10 text-md border-gradient-to-r from-teal-500 to-blue-400 font-inter font-medium">
                        {slide.badge}
                      </Badge>
                    </motion.div>
                    
                    <div className="space-y-4">
                      <motion.h1 
                        initial={{ y: 80, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ delay: 0.6, duration: 0.8, ease: "easeOut" }}
                        className="text-4xl md:text-5xl lg:text-6xl font-bold text-white leading-tight"
                      >
                        {slide.title}
                      </motion.h1>
                      <motion.p 
                        initial={{ y: 60, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ delay: 0.8, duration: 0.8, ease: "easeOut" }}
                        className="text-lg md:text-xl text-white/90 max-w-lg leading-relaxed"
                      >
                        {slide.subtitle}
                      </motion.p>
                    </div>

                    <motion.div
                      initial={{ y: 40, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: 1.0, duration: 0.8, ease: "easeOut" }}
                      className="flex flex-col sm:flex-row gap-4 pt-4"
                    >
                      <Button
                        onClick={() => {
                          // Activate the GlobalFAB
                          window.dispatchEvent(new CustomEvent('mae-fab-state-change', {
                            detail: { visible: true, floating: true }
                          }))
                        }}
                        size="lg"
                        className="bg-gradient-to-r from-teal-500 to-blue-500 hover:from-teal-600 hover:to-blue-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300"
                      >
                        <MessageCircle className="w-5 h-5" />
                        Talk to Mae
                        <Sparkles className="w-4 h-4" />
                      </Button>

                      {/* <Button
                        asChild
                        size="lg"
                        variant="outline"
                        className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm"
                      >
                        <Link href="/auth" className="flex items-center gap-2">
                          Get Started
                          <ChevronRight className="w-4 h-4" />
                        </Link>
                      </Button> */}

                      <div className="relative">
                        <span className="absolute -top-2 -right-2 z-10 rounded-full bg-gradient-to-r from-teal-500 to-blue-500 px-2 py-0.5 text-[10px] font-semibold text-white shadow-md">NEW</span>
                        <Button
                          asChild
                          size="lg"
                          className="relative group overflow-hidden rounded-full bg-gradient-to-r from-teal-500/20 via-cyan-400/20 to-blue-500/20 text-white ring-1 ring-teal-300/40 border-0 backdrop-blur-md hover:bg-white/20 shadow-[0_12px_40px_-12px_rgba(0,187,167,0.55)] hover:shadow-[0_18px_60px_-12px_rgba(0,187,167,0.75)] transition-all duration-300"
                          aria-label="View product demo videos"
                        >
                          <Link href="/demo" className="flex items-center gap-2">
                            <Clapperboard className="w-5 h-5" />
                            <span className="font-semibold">View Demos</span>
                            <span className="hidden md:inline text-xs opacity-80">• 60-second reels</span>
                            <ChevronRight className="w-4 h-4" />
                          </Link>
                        </Button>
                        {/* animated shine */}
                        <span className="pointer-events-none absolute inset-0 rounded-full overflow-hidden">
                          <span className="absolute -inset-1 w-[140%] h-full bg-[linear-gradient(110deg,transparent,rgba(255,255,255,.35),transparent)] translate-x-[-120%] group-hover:translate-x-[120%] transition-transform duration-700 ease-out" />
                        </span>
                      </div>

                      {slide.buttonText && slide.buttonLink && (
                        <Button
                          asChild
                          variant="outline"
                          size="lg"
                          className="bg-white/10 border-white/30 text-white hover:bg-white/20 backdrop-blur-sm"
                        >
                          <Link href={slide.buttonLink} className="flex items-center gap-2">
                            {slide.buttonText}
                            <ChevronRight className="w-4 h-4" />
                          </Link>
                        </Button>
                      )}
                    </motion.div>
                  </div>
                </div>
              </div>
            </motion.div>
          )
        )}
      </AnimatePresence>

      {/* Slide Controls */}
      <motion.div 
        initial={{ y: 50, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 1.2, duration: 0.6 }}
        className="absolute bottom-8 left-0 right-0 z-30"
      >
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between">
            <div className="flex gap-2">
              {slides.map((_, index) => (
                <motion.button
                  key={index}
                  onClick={() => goToSlide(index)}
                  whileHover={{ scale: 1.2 }}
                  whileTap={{ scale: 0.9 }}
                  className={cn(
                    "w-3 h-3 rounded-full transition-all",
                    index === currentSlide 
                      ? "bg-white scale-125" 
                      : "bg-white/50 hover:bg-white/80"
                  )}
                  aria-label={`Go to slide ${index + 1}`}
                />
              ))}
            </div>
            
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsPlaying(!isPlaying)}
                className="text-white hover:bg-white/10"
              >
                <motion.div
                  initial={false}
                  animate={{ rotate: isPlaying ? 0 : 180 }}
                  transition={{ duration: 0.3 }}
                >
                  {isPlaying ? (
                    <Pause className="h-4 w-4" />
                  ) : (
                    <Play className="h-4 w-4" />
                  )}
                </motion.div>
              </Button>
            </motion.div>
          </div>
        </div>
      </motion.div>
    </section>
  )
}
