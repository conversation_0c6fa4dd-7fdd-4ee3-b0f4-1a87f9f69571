"use client"

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  Mic, 
  MessageSquare, 
  Sparkles, 
  Activity, 
  Brain,
  Volume2,
  Wand2,
  CheckCircle2,
  AlertCircle,
  Loader2,
  Zap,
  Users,
  User,
  Heart,
  ArrowRight
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useGlobalMaeSession } from '@/components/global-mae-provider'
import { useMaeActivityTracker } from '@/hooks/use-mae-activity-tracker'

interface MaeVoiceStatusPanelProps {
  className?: string
  currentStep?: string
  compact?: boolean
}

export function MaeVoiceStatusPanel({ 
  className,
  currentStep = 'welcome',
  compact = false
}: MaeVoiceStatusPanelProps) {
  const { sessionState, isSessionActive } = useGlobalMaeSession()
  const maeActivity = useMaeActivityTracker()
  const [voiceLevel, setVoiceLevel] = useState(0)
  const [currentActivity, setCurrentActivity] = useState<string>('')

  // Simulate voice level animation when Mae is listening
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (sessionState?.isRecording) {
      interval = setInterval(() => {
        setVoiceLevel(Math.random() * 100)
      }, 100)
    } else {
      setVoiceLevel(0)
    }
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [sessionState?.isRecording])

  // Track Mae activities
  useEffect(() => {
    const latestActivity = maeActivity.activities[0]
    if (latestActivity) {
      setCurrentActivity(latestActivity.description)
    }
  }, [maeActivity.activities])

  // Get step-specific Mae guidance
  const getStepGuidance = () => {
    switch (currentStep) {
      case 'welcome':
        return {
          title: "Ready to Begin",
          description: "I'll guide you through setting up your family profile using voice commands.",
          suggestions: ["Say 'start onboarding'", "Ask 'what do I need to do?'"]
        }
      case 'user_info':
        return {
          title: "Personal Information",
          description: "Tell me about yourself - I'll fill out the form as you speak.",
          suggestions: ["My name is...", "My email is...", "I live in zip code..."]
        }
      case 'family_info':
        return {
          title: "Family Members",
          description: "Describe your children and I'll add them to your family profile.",
          suggestions: ["I have a 5-year-old daughter named...", "Add my son who is 8 years old"]
        }
      case 'complete':
        return {
          title: "All Set!",
          description: "Your family profile is complete. I'm here whenever you need assistance.",
          suggestions: ["Ask me anything about parenting", "Help me find resources"]
        }
      default:
        return {
          title: "Mae Assistant",
          description: "I'm here to help with voice-guided assistance.",
          suggestions: ["Say 'help' for guidance"]
        }
    }
  }

  const guidance = getStepGuidance()

  if (compact) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className={cn("flex items-center gap-4 p-4 rounded-xl", className)}
      >
        <div className={cn(
          "w-10 h-10 rounded-xl flex items-center justify-center transition-all",
          isSessionActive
            ? "bg-gradient-to-br from-primary to-primary/70 shadow-lg shadow-primary/25"
            : "bg-muted"
        )}>
          <Sparkles className={cn(
            "h-5 w-5 transition-colors",
            isSessionActive ? "text-primary-foreground animate-pulse" : "text-muted-foreground"
          )} />
        </div>
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <p className="text-base font-semibold truncate">Mae AI Assistant</p>
            <Badge
              variant={isSessionActive ? "default" : "secondary"}
              className={cn(
                "text-xs transition-all",
                isSessionActive && sessionState?.isRecording && "animate-pulse"
              )}
            >
              {isSessionActive ? (sessionState?.isRecording ? 'Listening' : 'Connected') : 'Standby'}
            </Badge>
          </div>
          <p className="text-sm text-muted-foreground">
            {isSessionActive
              ? (sessionState?.isRecording
                  ? 'Speak naturally - I\'ll help fill out forms'
                  : 'Ready for voice commands and guidance'
                )
              : 'Voice-powered onboarding assistant'
            }
          </p>
        </div>
        {sessionState?.isRecording && (
          <div className="flex items-center gap-1">
            {Array.from({ length: 4 }, (_, i) => (
              <motion.div
                key={i}
                className="w-1 bg-primary rounded-full"
                animate={{
                  height: [6, 16, 6],
                  opacity: [0.4, 1, 0.4]
                }}
                transition={{
                  duration: 0.6,
                  repeat: Infinity,
                  delay: i * 0.15
                }}
              />
            ))}
          </div>
        )}
        {currentActivity && !sessionState?.isRecording && (
          <div className="flex items-center gap-2">
            <Loader2 className="h-4 w-4 text-primary animate-spin" />
            <span className="text-xs text-primary font-medium max-w-32 truncate">
              {currentActivity}
            </span>
          </div>
        )}
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn("space-y-4", className)}
    >
      {/* Main Mae Status Card */}
      <Card className={cn(
        "border-2 transition-all duration-500",
        isSessionActive 
          ? "border-primary/50 bg-gradient-to-br from-primary/5 via-primary/3 to-primary/10 shadow-xl shadow-primary/10" 
          : "border-border/50 bg-card/50 backdrop-blur-sm"
      )}>
        <CardHeader className="pb-4">
          <div className="flex items-center gap-4">
            <motion.div
              animate={{ 
                scale: isSessionActive ? [1, 1.05, 1] : 1,
                rotate: sessionState?.isRecording ? [0, 2, -2, 0] : 0
              }}
              transition={{ 
                duration: sessionState?.isRecording ? 1 : 2, 
                repeat: isSessionActive ? Infinity : 0,
                ease: "easeInOut"
              }}
              className={cn(
                "w-16 h-16 rounded-2xl flex items-center justify-center transition-all duration-300",
                isSessionActive 
                  ? "bg-gradient-to-br from-primary via-primary/90 to-primary/70 shadow-xl shadow-primary/30" 
                  : "bg-gradient-to-br from-muted to-muted/70"
              )}
            >
              <Sparkles className={cn(
                "h-8 w-8 transition-all duration-300",
                isSessionActive ? "text-primary-foreground" : "text-muted-foreground"
              )} />
            </motion.div>
            
            <div className="flex-1">
              <CardTitle className="text-xl flex items-center gap-2">
                Mae AI Assistant
                <Badge 
                  variant={isSessionActive ? "default" : "secondary"}
                  className={cn(
                    "transition-all duration-300",
                    isSessionActive && "animate-pulse"
                  )}
                >
                  {isSessionActive ? 'Active' : 'Standby'}
                </Badge>
              </CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                {guidance.description}
              </p>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Voice Activity Indicator */}
          <AnimatePresence>
            {sessionState?.isRecording && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="space-y-3"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 text-sm font-medium text-primary">
                    <Volume2 className="h-4 w-4 animate-pulse" />
                    <span>Listening for your voice...</span>
                  </div>
                  <Badge variant="outline" className="border-primary/50 text-primary">
                    <Mic className="h-3 w-3 mr-1" />
                    Live
                  </Badge>
                </div>
                
                {/* Voice Level Visualization */}
                <div className="relative h-12 bg-muted/30 rounded-lg overflow-hidden">
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-primary/20 via-primary/40 to-primary/20"
                    animate={{
                      x: [-100, 100, -100],
                      opacity: [0.3, 0.8, 0.3]
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="flex items-center gap-1">
                      {Array.from({ length: 12 }, (_, i) => (
                        <motion.div
                          key={i}
                          className="w-1 bg-primary rounded-full"
                          animate={{
                            height: voiceLevel > (i * 8) ? [8, 24, 8] : [4, 8, 4],
                            opacity: voiceLevel > (i * 8) ? [0.6, 1, 0.6] : [0.3, 0.5, 0.3]
                          }}
                          transition={{ 
                            duration: 0.3 + (i * 0.05),
                            repeat: Infinity,
                            ease: "easeInOut"
                          }}
                        />
                      ))}
                    </div>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Current Activity */}
          <AnimatePresence>
            {currentActivity && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="flex items-center gap-3 p-3 bg-primary/5 rounded-lg border border-primary/20"
              >
                <Loader2 className="h-4 w-4 text-primary animate-spin" />
                <span className="text-sm text-primary font-medium">{currentActivity}</span>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Voice Suggestions */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">Try saying:</span>
            </div>
            <div className="space-y-2">
              {guidance.suggestions.map((suggestion, index) => (
                <motion.div
                  key={suggestion}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center gap-3 p-2 bg-muted/30 rounded-lg border border-border/30 hover:bg-muted/50 transition-colors cursor-pointer group"
                  onClick={() => {
                    // Dispatch voice suggestion event
                    window.dispatchEvent(new CustomEvent('mae-voice-suggestion', {
                      detail: { suggestion, step: currentStep }
                    }))
                  }}
                >
                  <div className="w-6 h-6 rounded-full bg-primary/10 flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                    <ArrowRight className="h-3 w-3 text-primary" />
                  </div>
                  <span className="text-sm text-muted-foreground group-hover:text-foreground transition-colors">
                    "{suggestion}"
                  </span>
                </motion.div>
              ))}
            </div>
          </div>

          {/* AG-UI Event Status */}
          {maeActivity.activities.length > 0 && (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Brain className="h-4 w-4 text-primary" />
                <span className="text-sm font-medium">Recent Activities</span>
              </div>
              <div className="space-y-2 max-h-32 overflow-y-auto">
                {maeActivity.activities.slice(0, 3).map((activity, index) => (
                  <motion.div
                    key={activity.id}
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="flex items-center gap-3 p-2 bg-muted/20 rounded-md"
                  >
                    <div className={cn(
                      "w-2 h-2 rounded-full",
                      activity.status === 'completed' && "bg-green-500",
                      activity.status === 'active' && "bg-primary animate-pulse",
                      activity.status === 'error' && "bg-red-500"
                    )} />
                    <span className="text-xs text-muted-foreground flex-1">
                      {activity.description}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {new Date(activity.timestamp).toLocaleTimeString([], { 
                        hour: '2-digit', 
                        minute: '2-digit' 
                      })}
                    </span>
                  </motion.div>
                ))}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Mae Capabilities Card */}
      <Card className="border-border/50 bg-card/30 backdrop-blur-sm">
        <CardHeader className="pb-3">
          <CardTitle className="text-base flex items-center gap-2">
            <Zap className="h-4 w-4 text-primary" />
            Voice Capabilities
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-3">
            {[
              { 
                icon: User, 
                label: 'Profile Setup', 
                active: currentStep === 'user_info',
                description: 'Fill forms with voice'
              },
              { 
                icon: Users, 
                label: 'Family Management', 
                active: currentStep === 'family_info',
                description: 'Add family members'
              },
              { 
                icon: CheckCircle2, 
                label: 'Smart Validation', 
                active: true,
                description: 'Real-time form checking'
              },
              { 
                icon: Wand2, 
                label: 'Auto-Complete', 
                active: isSessionActive,
                description: 'Intelligent suggestions'
              }
            ].map((capability, index) => {
              const Icon = capability.icon
              return (
                <motion.div
                  key={capability.label}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                  className={cn(
                    "p-3 rounded-lg border transition-all duration-300 group cursor-pointer",
                    capability.active 
                      ? "bg-primary/10 border-primary/30 hover:bg-primary/15" 
                      : "bg-muted/30 border-border/30 hover:bg-muted/50"
                  )}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-center gap-2 mb-1">
                    <Icon className={cn(
                      "h-4 w-4 transition-colors",
                      capability.active ? "text-primary" : "text-muted-foreground"
                    )} />
                    <span className={cn(
                      "text-xs font-medium transition-colors",
                      capability.active ? "text-primary" : "text-muted-foreground"
                    )}>
                      {capability.label}
                    </span>
                  </div>
                  <p className="text-xs text-muted-foreground group-hover:text-foreground transition-colors">
                    {capability.description}
                  </p>
                </motion.div>
              )
            })}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
