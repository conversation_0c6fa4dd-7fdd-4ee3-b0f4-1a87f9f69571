/**
 * Personalized Mae Interface Component
 * 
 * This component provides a personalized Mae experience using the user's
 * authentication context and family data to deliver tailored conversations.
 */

"use client"

import React, { useState, useEffect, useCallback } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Mi<PERSON>, MicOff, Users, MessageSquare, Clock, User, Baby, Heart } from 'lucide-react'
import { useGeminiLivePersonalized } from '@/hooks/use-gemini-live-personalized'
import type { UserContextData } from '@/lib/user-context-service'

interface PersonalizedMaeInterfaceProps {
  className?: string
  showUserContext?: boolean
  autoStart?: boolean
  authUserId?: string
  userEmail?: string
  autoLoadContext?: boolean
  saveConversations?: boolean
}

export function PersonalizedMaeInterface({ 
  className = '',
  showUserContext = true,
  autoStart = false,
  authUserId,
  userEmail,
  autoLoadContext = true,
  saveConversations = true
}: PersonalizedMaeInterfaceProps) {
  const [selectedSessionId, setSelectedSessionId] = useState<string>('')
  const [recentSessions, setRecentSessions] = useState<any[]>([])
  const [isLoadingSessions, setIsLoadingSessions] = useState(false)

  // Initialize personalized Mae session
  const {
    status,
    error,
    isRecording,
    isModelSpeaking,
    isConnected,
    isLoadingContext,
    userContext,
    currentSessionId,
    initClient,
    startRecording,
    stopRecording,
    endSession,
    loadUserContext
  } = useGeminiLivePersonalized({
    authUserId,
    userEmail,
    autoLoadContext,
    saveConversations
  })

  // Load recent sessions for the user
  const loadRecentSessions = useCallback(async () => {
    if (!authUserId) return

    setIsLoadingSessions(true)
    try {
      const response = await fetch(`/api/mae-sessions?auth_user_id=${authUserId}&limit=5`)
      if (response.ok) {
        const data = await response.json()
        setRecentSessions(data.sessions || [])
      }
    } catch (error) {
      console.error('❌ Error loading recent sessions:', error)
    } finally {
      setIsLoadingSessions(false)
    }
  }, [authUserId])

  // Load recent sessions on mount
  useEffect(() => {
    if (authUserId) {
      loadRecentSessions()
    }
  }, [authUserId, loadRecentSessions])

  // Auto-start if requested
  useEffect(() => {
    if (autoStart && authUserId && !isConnected && !isLoadingContext) {
      initClient()
    }
  }, [autoStart, authUserId, isConnected, isLoadingContext, initClient])

  // Handle microphone toggle
  const handleMicrophoneToggle = useCallback(async () => {
    if (isRecording) {
      stopRecording()
    } else {
      if (!isConnected) {
        await initClient()
      }
      await startRecording()
    }
  }, [isRecording, isConnected, initClient, startRecording, stopRecording])

  // Handle session ending
  const handleEndSession = useCallback(() => {
    endSession()
    // Reload sessions to show the latest
    setTimeout(() => loadRecentSessions(), 1000)
  }, [endSession, loadRecentSessions])

  // Render authentication required state
  if (!authUserId) {
    return (
      <Card className={className}>
        <CardContent className="p-6 text-center">
          <User className="w-12 h-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Sign In for Personalized Mae
          </h3>
          <p className="text-gray-600 mb-4">
            Sign in to get personalized conversations with Mae based on your family profile.
          </p>
          <p className="text-sm text-gray-500">
            Mae will remember your children&apos;s names, ages, and important details to provide better guidance.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* User Context Display */}
      {showUserContext && userContext && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="w-5 h-5 text-teal-500" />
              Your Family Profile
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Parent Information</h4>
                <p className="text-sm text-gray-600">
                  <strong>Name:</strong> {userContext.user.name}
                </p>
                <p className="text-sm text-gray-600">
                  <strong>Role:</strong> {userContext.user.role}
                </p>
                <p className="text-sm text-gray-600">
                  <strong>Member since:</strong> {new Date(userContext.user.created_at).toLocaleDateString()}
                </p>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Family Members</h4>
                {userContext.family_members.length > 0 ? (
                  <div className="space-y-2">
                    {userContext.family_members.map((member) => (
                      <div key={member.id} className="flex items-center gap-2">
                        <Baby className="w-4 h-4 text-blue-500" />
                        <span className="text-sm">
                          {member.name} ({member.age} years old)
                        </span>
                        {member.is_primary && (
                          <Badge variant="secondary" className="text-xs">Primary</Badge>
                        )}
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-sm text-gray-500">No family members added yet</p>
                )}
              </div>
            </div>

            {userContext.family_summary.primary_concerns.length > 0 && (
              <div className="mt-4 pt-4 border-t">
                <h4 className="font-medium text-gray-900 mb-2">Primary Concerns</h4>
                <div className="flex flex-wrap gap-2">
                  {userContext.family_summary.primary_concerns.map((concern, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      <Heart className="w-3 h-3 mr-1" />
                      {concern}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Mae Interface */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className={`w-3 h-3 rounded-full ${isConnected ? 'bg-green-500' : 'bg-gray-400'}`} />
              {userContext ? `Mae for ${userContext.user.name}` : 'Mae - Personalized Assistant'}
            </div>
            {isConnected && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleEndSession}
                className="text-red-600 hover:text-red-700"
              >
                End Session
              </Button>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Status and Loading */}
          <div className="mb-6">
            {isLoadingContext ? (
              <div className="flex items-center gap-2 text-blue-600">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                <span className="text-sm">Loading your family information...</span>
              </div>
            ) : (
              <div className="text-sm text-gray-600">
                <strong>Status:</strong> {status}
              </div>
            )}
            
            {error && (
              <div className="text-red-600 text-sm mt-2">
                <strong>Error:</strong> {error}
              </div>
            )}
          </div>

          {/* Microphone Control */}
          <div className="text-center mb-6">
            <Button
              onClick={handleMicrophoneToggle}
              disabled={isLoadingContext}
              className={`w-20 h-20 rounded-full ${
                isRecording
                  ? 'bg-red-500 hover:bg-red-600 animate-pulse'
                  : isModelSpeaking
                  ? 'bg-teal-400 animate-pulse'
                  : 'bg-teal-500 hover:bg-teal-600'
              }`}
            >
              {isRecording ? (
                <MicOff className="w-8 h-8 text-white" />
              ) : (
                <Mic className="w-8 h-8 text-white" />
              )}
            </Button>
            
            <div className="mt-4 space-y-1">
              <p className="text-sm font-medium">
                {isRecording
                  ? 'Recording... Speak now'
                  : isModelSpeaking
                  ? 'Mae is speaking...'
                  : 'Click to start talking'}
              </p>
              
              {userContext && (
                <p className="text-xs text-gray-500">
                  Mae knows about {userContext.family_members.map(m => m.name).join(', ')} and your family
                </p>
              )}
            </div>
          </div>

          {/* Current Session Info */}
          {currentSessionId && (
            <div className="bg-gray-50 rounded-lg p-3 mb-4">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <MessageSquare className="w-4 h-4" />
                <span>Session: {currentSessionId}</span>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Sessions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Clock className="w-5 h-5 text-blue-500" />
            Recent Conversations
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingSessions ? (
            <div className="flex items-center gap-2 text-gray-600">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-600"></div>
              <span className="text-sm">Loading recent conversations...</span>
            </div>
          ) : recentSessions.length > 0 ? (
            <div className="space-y-3">
              {recentSessions.map((session) => (
                <div
                  key={session.session_id}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div>
                    <p className="font-medium text-sm">{session.last_topic}</p>
                    <p className="text-xs text-gray-500">
                      {new Date(session.created_at).toLocaleDateString()} • {session.duration} • {session.message_count} messages
                    </p>
                  </div>
                  <Badge variant="outline" className="text-xs">
                    {session.session_id.split('_')[1]}
                  </Badge>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6">
              <MessageSquare className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">No recent conversations</p>
              <p className="text-xs text-gray-400 mt-1">
                Start a conversation with Mae to see your history here
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}