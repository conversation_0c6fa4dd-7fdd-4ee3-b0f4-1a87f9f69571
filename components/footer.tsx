"use client"

import * as React from "react"
import { Heart, Mail, Phone, MapPin, Linkedin, Twitter, Share, UserPlus } from "lucide-react"
import { <PERSON>ton } from "@/components/ui/button"
import Link from "next/link"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import Image from "next/image"
import { motion } from "framer-motion"
import { ShareDialog } from "@/components/share-dialog"
import { InviteDialog } from "@/components/invite-dialog"
import { useToast } from "@/hooks/use-toast"

const footerLinks = {
  product: [
    { name: "AI Assistant" },
    { name: "Health Tracking" },
    { name: "Care Journal" },
    { name: "Pricing" },
  ],
  company: [

    { name: "Contact" },
  ],
  resources: [
    { name: "Help Center" },
    { name: "API Docs" },
    { name: "Blog" },
    { name: "Community" },
  ],
  legal: [
    { name: "Privacy Policy", href: "/privacy" },
    { name: "Terms of Service", href: "/terms" },
    { name: "HIPAA Compliance" },
    { name: "Security" },
  ],
}

const socialLinks = [
  { name: "LinkedIn", icon: Linkedin },
  { name: "Twitter", icon: Twitter },
]

export default function Footer() {
  const [email, setEmail] = React.useState("")
  const [isSubmitting, setIsSubmitting] = React.useState(false)
  const { toast } = useToast()

  const handleNewsletterSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!email.trim()) {
      toast({
        title: "Email Required",
        description: "Please enter your email address.",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      const response = await fetch('/api/newsletter-signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email: email.trim() }),
      })

      const result = await response.json()

      if (response.ok) {
        toast({
          title: "Successfully Subscribed!",
          description: "Thank you for subscribing to our newsletter. You'll receive updates on pediatric AI and health insights.",
        })
        setEmail("")
      } else {
        toast({
          title: "Subscription Failed",
          description: result.error || "Failed to subscribe. Please try again.",
          variant: "destructive",
        })
      }
    } catch (error) {
      console.error('Newsletter signup error:', error)
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred"
      toast({
        title: "Network Error",
        description: `Unable to connect: ${errorMessage}. Please check your internet connection and try again.`,
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <footer className="border-t border-teal-500 gradient-to-r from-teal-500 to-blue-600">
      <div className="container mx-auto px-4 py-16">
        <div className="flex flex-wrap justify-center gap-8 lg:gap-12">
          {/* Brand Section */}
          <div className="w-full md:w-auto md:max-w-sm space-y-4">
            <div className="flex items-center space-x-2">
              <div className="flex h-16 w-16 items-center justify-center rounded-lg">
                <motion.div
                  whileHover={{ scale: 1.1, rotate: 10 }}
                  transition={{ duration: 0.2 }}
                  className="inline-block"
                >
                  <Image src="/OKdarkTsp.png" alt="Our Kidz" width={64} height={64} />
                </motion.div>
              </div>
              <div className="flex flex-col">
                <span className="text-2xl font-light font-inter tracking-tight text-foreground">our kidz</span>
                <span className="text-xs text-muted-foreground font-medium tracking-wide">AI-Powered Parenting</span>
              </div>
            </div>
            <p className="text-muted-foreground font-inter font-light max-w-sm text-sm">
              AI-powered pediatric care platform empowering parents with trusted guidance
              and secure health tracking for their children.
            </p>
            <div className="space-y-2">
              <div className="flex items-center space-x-2 text-sm font-inter font-light text-muted-foreground">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-2 text-sm font-inter font-light text-muted-foreground">
                <Phone className="h-4 w-4" />
                <span>1-800-OUR-KIDZ</span>
              </div>
              <div className="flex items-center space-x-2 text-sm font-inter font-light text-muted-foreground">
                <MapPin className="h-4 w-4" />
                <span>Boise, ID</span>
              </div>
            </div>
          </div>

          {/* Product Links */}
          <div className="w-full sm:w-auto min-w-[120px] space-y-4">
            <h3 className="font-semibold">Product</h3>
            <ul className="space-y-2">
              {footerLinks.product.map((link) => (
                <li key={link.name}>
                  <span className="text-sm text-muted-foreground">
                    {link.name}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          {/* Company Links */}
          <div className="w-full sm:w-auto min-w-[120px] space-y-4">
            <h3 className="font-semibold">Company</h3>
            <ul className="space-y-2">
              {footerLinks.company.map((link) => (
                <li key={link.name}>
                  <span className="text-sm text-muted-foreground">
                    {link.name}
                  </span>
                </li>
              ))}
            </ul>
          </div>

          {/* Community Section */}
          <div className="w-full sm:w-auto min-w-[140px] space-y-4">
            <h3 className="font-semibold">Community</h3>
            <p className="text-sm text-muted-foreground">
              Help grow our parenting community
            </p>
            {/* <div className="space-y-2">
              <ShareDialog>
                <Button
                  variant="outline"
                  size="sm"
                  className="w-full justify-start text-sm"
                >
                  <Share className="w-4 h-4 mr-2" />
                  Share Our Kidz
                </Button>
              </ShareDialog>
              <InviteDialog>
                <Button
                  size="sm"
                  className="w-full justify-start bg-teal-500 hover:bg-teal-600 text-sm"
                >
                  <UserPlus className="w-4 h-4 mr-2" />
                  Invite Friends
                </Button>
              </InviteDialog>
            </div> */}
            {/* <p className="text-xs text-muted-foreground">
              Get 1 free months for each friend who joins!
            </p> */}
          </div>

          {/* Resources Links */}
          {/* <div className="space-y-4">
            <h3 className="font-semibold">Resources</h3>
            <ul className="space-y-2">
              {footerLinks.resources.map((link) => (
                <li key={link.name}>
                  <Link
                    href={link.href}
                    className="text-sm text-muted-foreground hover:text-foreground transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div> */}

          {/* Newsletter */}
          <div className="w-full sm:w-auto min-w-[200px] space-y-4">
            <h3 className="font-semibold">Stay Updated</h3>
            <p className="text-sm text-muted-foreground">
              Get the latest updates on pediatric AI and health insights.
            </p>
            <form onSubmit={handleNewsletterSubmit} className="space-y-2">
              <Input
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="text-sm"
                required
                disabled={isSubmitting}
              />
              <Button
                type="submit"
                size="sm"
                className="w-full"
                disabled={isSubmitting}
              >
                {isSubmitting ? "Subscribing..." : "Subscribe"}
              </Button>
            </form>
          </div>
        </div>

        <Separator className="my-8  bg-gradient-to-r from-teal-500 to-blue-400 " />

        {/* Community Call-to-Action */}
        <div className="text-center py-6 bg-gradient-to-r from-teal-50 to-blue-50 dark:from-teal-950/20 dark:to-blue-950/20 rounded-lg border border-teal-200 dark:border-teal-800">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Love Our Kidz? Share the Experience!
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-4 max-w-2xl mx-auto">
            Help other parents discover Mae and get rewarded! Every friend you invite gets 1 free month, and so do you.
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center items-center">
            <ShareDialog>
              <Button variant="outline" className="flex items-center gap-2">
                <Share className="w-4 h-4" />
                Share Our Kidz
              </Button>
            </ShareDialog>
            <InviteDialog>
              <Button className="bg-teal-500 hover:bg-teal-600 flex items-center gap-2">
                <UserPlus className="w-4 h-4" />
                Invite Friends & Get Rewarded
              </Button>
            </InviteDialog>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="flex flex-col space-y-4 mt-6">
          {/* Copyright and disclaimer */}


          {/* Legal Links and Social - Stack on mobile */}
          <div className="flex flex-col md:flex-row md:justify-between items-center space-y-4 md:space-y-0">
            {/* Legal Links */}

            <div className="flex flex-wrap justify-center items-center justify-center mx-auto gap-x-3 gap-y-2 gap-2 text-xs md:text-sm">
              {footerLinks.legal.map((link, index) => (
                <React.Fragment key={link.name}>
                  {"href" in link && link.href ? (
                    <Link
                      href={link.href}
                      className="text-muted-foreground hover:text-foreground underline-offset-4 hover:underline whitespace-nowrap"
                    >
                      {link.name}
                    </Link>
                  ) : (
                    <span className="text-muted-foreground whitespace-nowrap">{link.name}</span>
                  )}
                  {index < footerLinks.legal.length - 1 && (
                    <span className="text-muted-foreground hidden md:inline">•</span>
                  )}
                </React.Fragment>
              ))}
            </div>

            {/* Social Links */}
            <div className="flex items-center space-x-2">
              {socialLinks.map((social) => (
                <Button
                  key={social.name}
                  variant="ghost"
                  size="icon"
                  aria-label={social.name}
                  disabled
                >
                  <social.icon className="h-4 w-4" />
                </Button>
              ))}
              </div>

          </div>
        </div>
        <div className="text-sm text-muted-foreground text-center space-x-2 md:mr-auto">
        <span>© 2025 our kidz. All rights reserved.</span>
      </div>
      </div>
    </footer>
  )
}
