"use client"

import React from "react"
import Link from "next/link"
import Image from "next/image"
import { Menu, Heart, MessageCircle, Share, UserPlus, MapPin } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Sheet, Sheet<PERSON>ontent, SheetTrigger } from "@/components/ui/sheet"
import { ThemeToggle } from "@/components/theme-toggle"
import { LanguageToggle } from "@/components/language-toggle"
import { InviteDialog } from "@/components/invite-dialog"
import { ShareDialog } from "@/components/share-dialog"
import { motion } from "framer-motion"
import {
  SignInButton,
  SignUpButton,
  SignedIn,
  SignedOut,
  UserButton,
} from '@clerk/nextjs'

// const navigation = [
//   { name: "Products", href: "/products", icon: Stethoscope },
//   { name: "AI Assistant", href: "/ai", icon: Heart },
//   { name: "Company", href: "/company", icon: Shield },
// ]

export function Header() {
  const [isOpen, setIsOpen] = React.useState(false);

  return (
    <motion.header 
      initial={{ y: -100, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
      className="sticky top-0 z-50 w-full border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60"
    >
      <div className="container mx-auto flex h-16 max-w-screen-2xl items-center px-4 relative">
        
        {/* Logo - perfectly centered */}
        <motion.div 
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="absolute left-1 transform -translate-x-1/2"
        >
          <Link href="/" className="flex items-center space-x-2">
            <motion.div
              whileHover={{ scale: 1.1, rotate: -10 }}
              transition={{ duration: 0.2 }}
            >
              <Image src="/OKdarkTsp.png" alt="our kidz" width={48} height={48} />
            </motion.div>
            <motion.span 
              whileHover={{ color: "#14b8a6" }}
              transition={{ duration: 0.2 }}
              className="text-2xl font-light font-inter tracking-tight text-foreground hidden sm:block"
            >
              our kidz
            </motion.span>
          </Link>
        </motion.div>

        {/* Right side - Action Buttons, Toggles and Menu */}
        <div className="flex items-center space-x-4 ml-auto">
          {/* Desktop Navigation */}
          <motion.nav
            initial={{ x: 50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="hidden lg:flex items-center space-x-6"
          >
            {/* <Link
              href="/healthcare"
              className="flex items-center space-x-2 text-foreground/70 hover:text-foreground transition-colors"
            >
              <MapPin className="w-4 h-4" />
              <span>Find Providers</span>
            </Link> */}
          </motion.nav>

          {/* Action Buttons - Hidden on mobile */}
          <motion.div
            initial={{ x: 50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            className="hidden md:flex items-center space-x-4"
          >
            <ShareDialog>
              <Button
                variant="ghost"
                size="sm"
                className="flex items-center text-foreground/70 hover:text-foreground "
              >
                <Share className="w-4 h-4" />
                Share
              </Button>
            </ShareDialog>
            <InviteDialog>
              <Button
                size="sm"
                className="bg-teal-500 hover:bg-teal-600 flex items-center"
              >
                <UserPlus className="w-4 h-4" />
                Invite Friends
              </Button>
            </InviteDialog>
          </motion.div>

          {/* Language and Theme Toggles */}
          <motion.div
            initial={{ x: 50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="flex items-center space-x-2"
          >
            <LanguageToggle />
            <ThemeToggle />
          </motion.div>

          {/* Clerk Authentication */}
          <motion.div
            initial={{ x: 50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.25 }}
            className="flex items-center"
          >
            <SignedOut>
              <Link href="/auth">
                <Button size="sm" className="bg-gradient-to-r from-teal-500 to-blue-600 hover:from-teal-600 hover:to-blue-700 text-white">
                  Sign In
                </Button>
              </Link>
            </SignedOut>
            <SignedIn>
              <UserButton afterSignOutUrl="/" />
            </SignedIn>
          </motion.div>

          {/* Mobile Menu Button */}
          <Sheet open={isOpen} onOpenChange={setIsOpen}>
            <SheetTrigger asChild>
              <motion.div
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                initial={{ x: 50, opacity: 0 }}
                animate={{ x: 0, opacity: 1 }}
                transition={{ duration: 0.6, delay: 0.3 }}
                className="md:hidden"
              >
                <Button
                  variant="ghost"
                  size="icon"
                  aria-label="Toggle menu"
                >
                  <Menu className="h-5 w-5" />
                </Button>
              </motion.div>
            </SheetTrigger>
            <SheetContent side="right" className="w-[300px] sm:w-[400px]">
              <div className="flex flex-col space-y-4 mt-6">
                <div className="flex items-center space-x-2 pb-4 border-b">
                  <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary">
                    <Heart className="h-5 w-5 text-primary-foreground" />
                  </div>
                  <span className="font-bold">Our Kidz</span>
                </div>

                
                <nav className="flex flex-col space-y-3">
                  <Link
                    href="/conversation"
                    onClick={() => setIsOpen(false)}
                    className="flex items-center space-x-3 text-lg font-medium text-foreground/60 transition-colors hover:text-foreground/80"
                  >
                    <MessageCircle className="h-5 w-5" />
                    <span>Chat with Mae</span>
                  </Link>
                  <Link
                    href="/healthcare"
                    onClick={() => setIsOpen(false)}
                    className="flex items-center space-x-3 text-lg font-medium text-foreground/60 transition-colors hover:text-foreground/80"
                  >
                    <MapPin className="h-5 w-5" />
                    <span>Find Providers</span>
                  </Link>
                  <ShareDialog>
                    <button
                      onClick={() => setIsOpen(false)}
                      className="flex items-center space-x-3 text-lg font-medium text-foreground/60 transition-colors hover:text-foreground/80 w-full text-left"
                    >
                      <Share className="h-5 w-5" />
                      <span>Share</span>
                    </button>
                  </ShareDialog>
                  <InviteDialog>
                    <button
                      onClick={() => setIsOpen(false)}
                      className="flex items-center space-x-3 text-lg font-medium text-foreground/60 transition-colors hover:text-foreground/80 w-full text-left"
                    >
                      <UserPlus className="h-5 w-5" />
                      <span>Invite Friends</span>
                    </button>
                  </InviteDialog>
                </nav>
                
                <div className="pt-4 border-t">
                  <Button asChild className="w-full">
                    <Link href="/contact" onClick={() => setIsOpen(false)}>
                      Get Started
                    </Link>
                  </Button>
                </div>
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </motion.header>
  )
}

