"use client"

import * as React from "react"
import { SignedOut } from "@clerk/nextjs"
import { SignIn, SignUp } from "@clerk/nextjs"
import { Button } from "@/components/ui/button"

export function LandingAuthOverlay() {
  const [mode, setMode] = React.useState<"sign-in" | "sign-up">("sign-in")

  // Only show on production root marketing domain
  const shouldShow = React.useMemo(() => {
    if (typeof window === "undefined") return false
    const { hostname, pathname } = window.location
    const isMarketingDomain = hostname === "our-kidz.com" || hostname === "www.our-kidz.com"
    return isMarketingDomain && pathname === "/"
  }, [])

  return (
    <SignedOut>
      {!shouldShow ? null : (
      <div className="fixed inset-0 z-[100] flex items-center justify-center">
        {/* Backdrop */}
        <div className="absolute inset-0 bg-background/70 backdrop-blur-sm" />

        {/* Modal */}
        <div className="relative z-[101] w-full max-w-md mx-4">
          <div className="mb-4 flex items-center justify-center gap-2">
            <Button
              variant={mode === "sign-in" ? "default" : "outline"}
              size="sm"
              onClick={() => setMode("sign-in")}
            >
              Sign In
            </Button>
            <Button
              variant={mode === "sign-up" ? "default" : "outline"}
              size="sm"
              onClick={() => setMode("sign-up")}
            >
              Sign Up
            </Button>
          </div>

          <div className="rounded-lg border bg-card p-4 shadow-lg">
            {mode === "sign-in" ? (
              <SignIn routing="hash" appearance={{
                variables: {
                  colorPrimary: "#14b8a6",
                }
              }} />
            ) : (
              <SignUp routing="hash" appearance={{
                variables: {
                  colorPrimary: "#14b8a6",
                }
              }} />
            )}
          </div>
        </div>
      </div>
      )}
    </SignedOut>
  )
}


