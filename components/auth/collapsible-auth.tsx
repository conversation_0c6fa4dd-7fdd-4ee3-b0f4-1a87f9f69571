'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { ChevronDown, ChevronUp, User, LogOut, CheckCircle2, AlertCircle } from 'lucide-react'
import { useAuth } from '@/lib/auth-provider'
import { AuthForms } from './auth-forms'
import { cn } from '@/lib/utils'

interface CollapsibleAuthProps {
  className?: string
  defaultOpen?: boolean
  onAuthSuccess?: () => void
  hideWhenAuthenticated?: boolean
}

export function CollapsibleAuth({ 
  className, 
  defaultOpen = false, 
  onAuthSuccess,
  hideWhenAuthenticated = false 
}: CollapsibleAuthProps) {
  const { user, loading, signOut } = useAuth()
  const [isOpen, setIsOpen] = useState(defaultOpen)

  // Hide entirely when authenticated if requested
  if (hideWhenAuthenticated && user) {
    return null
  }

  const handleAuthSuccess = () => {
    setIsOpen(false) // Collapse after successful auth
    onAuthSuccess?.()
  }

  if (loading) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-gray-600">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-teal-600"></div>
            <span className="text-sm">Loading authentication...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  // If user is authenticated, show compact authenticated state
  if (user) {
    return (
      <Card className={cn("w-full", className)}>
        <Collapsible open={isOpen} onOpenChange={setIsOpen}>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <CheckCircle2 className="h-5 w-5 text-green-600" />
                  <div>
                    <CardTitle className="text-sm font-medium">
                      Signed in as {user.email}
                    </CardTitle>
                    <p className="text-xs text-gray-500 mt-1">
                      Account verified and ready
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {!user.email_confirmed_at && (
                    <AlertCircle className="h-4 w-4 text-amber-500" />
                  )}
                  {isOpen ? (
                    <ChevronUp className="h-4 w-4 text-gray-400" />
                  ) : (
                    <ChevronDown className="h-4 w-4 text-gray-400" />
                  )}
                </div>
              </div>
            </CardHeader>
          </CollapsibleTrigger>
          
          <CollapsibleContent>
            <CardContent className="p-4 pt-0 border-t">
              <div className="space-y-3">
                {!user.email_confirmed_at && (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription className="text-sm">
                      Please check your email and click the verification link to fully activate your account.
                    </AlertDescription>
                  </Alert>
                )}
                
                <div className="text-xs text-gray-600 space-y-1">
                  <div>Account ID: {user.id.slice(0, 8)}...</div>
                  <div>Created: {new Date(user.created_at).toLocaleDateString()}</div>
                  {user.email_confirmed_at && (
                    <div>Verified: {new Date(user.email_confirmed_at).toLocaleDateString()}</div>
                  )}
                </div>
                
                <div className="flex gap-2 pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => signOut()}
                    className="flex items-center gap-2"
                  >
                    <LogOut className="h-3 w-3" />
                    Sign Out
                  </Button>
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Collapsible>
      </Card>
    )
  }

  // If user is not authenticated, show collapsible auth forms
  return (
    <Card className={cn("w-full", className)}>
      <Collapsible open={isOpen} onOpenChange={setIsOpen}>
        <CollapsibleTrigger asChild>
          <CardHeader className="cursor-pointer hover:bg-gray-50 transition-colors p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <User className="h-5 w-5 text-teal-600" />
                <div>
                  <CardTitle className="text-sm font-medium">
                    Sign In / Create Account
                  </CardTitle>
                  <p className="text-xs text-gray-500 mt-1">
                    Access your personalized Mae experience
                  </p>
                </div>
              </div>
              {isOpen ? (
                <ChevronUp className="h-4 w-4 text-gray-400" />
              ) : (
                <ChevronDown className="h-4 w-4 text-gray-400" />
              )}
            </div>
          </CardHeader>
        </CollapsibleTrigger>
        
        <CollapsibleContent>
          <CardContent className="p-4 pt-0 border-t">
            <AuthForms onSuccess={handleAuthSuccess} />
          </CardContent>
        </CollapsibleContent>
      </Collapsible>
    </Card>
  )
}