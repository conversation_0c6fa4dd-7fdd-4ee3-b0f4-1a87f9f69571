'use client';

import React from 'react';
import { CopilotKit } from '@copilotkit/react-core';
import { CopilotPopup } from '@copilotkit/react-ui';

interface CopilotProviderProps {
  children: React.ReactNode;
}

export function CopilotProvider({ children }: CopilotProviderProps) {
  return (
    <CopilotKit 
      runtimeUrl="/api/copilotkit"
      publicApiKey={process.env.NEXT_PUBLIC_COPILOT_CLOUD_PUBLIC_API_KEY}
      showDevConsole={process.env.NODE_ENV === 'development'}
    >
      {children}
      {/* CopilotPopup disabled - using Gemini Live voice interface instead */}
      {/*
      <CopilotPopup
        instructions="You are <PERSON>, an AI pediatric health coach. You help parents find healthcare providers and give health advice based on current research. When finding healthcare providers, you can show an interactive map."
        labels={{
          title: "Mae - Your Health Assistant",
          initial: "Hi! I'm <PERSON>. I can help you find local pediatricians, urgent care, answer health questions about your child, and show you providers on an interactive map."
        }}
        defaultOpen={false}
        clickOutsideToClose={true}
      />
      */}
    </CopilotKit>
  );
}

export default CopilotProvider;