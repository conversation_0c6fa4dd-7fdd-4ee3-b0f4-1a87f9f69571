"use client"

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Activity, 
  Brain,
  Mic,
  MessageSquare,
  Zap,
  CheckCircle2,
  AlertCircle,
  Clock,
  User,
  Users,
  FileText,
  Settings,
  ArrowRight,
  Sparkles,
  Volume2,
  Eye
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useMaeActivityTracker } from '@/hooks/use-mae-activity-tracker'
import { useGlobalMaeSession } from '@/components/global-mae-provider'

interface MaeActivityFeedProps {
  className?: string
  maxItems?: number
  showTimestamps?: boolean
  compact?: boolean
}

export function MaeActivityFeed({ 
  className,
  maxItems = 10,
  showTimestamps = true,
  compact = false
}: MaeActivityFeedProps) {
  const maeActivity = useMaeActivityTracker()
  const { sessionState, isSessionActive } = useGlobalMaeSession()
  const [recentActivities, setRecentActivities] = useState<any[]>([])

  // Update recent activities
  useEffect(() => {
    const activities = maeActivity.activities.slice(0, maxItems)
    setRecentActivities(activities)
  }, [maeActivity.activities, maxItems])

  // Get icon for activity type
  const getActivityIcon = (activity: any) => {
    switch (activity.type) {
      case 'listening':
        return Mic
      case 'speaking':
        return Volume2
      case 'function':
        return Zap
      case 'navigation':
        return ArrowRight
      case 'processing':
        return Brain
      case 'tool':
        return Settings
      default:
        return Activity
    }
  }

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return 'text-green-500'
      case 'active':
        return 'text-primary'
      case 'error':
        return 'text-red-500'
      default:
        return 'text-muted-foreground'
    }
  }

  // Get activity description with enhanced details
  const getEnhancedDescription = (activity: any) => {
    const baseDescription = activity.description || activity.name

    // Add context based on activity type
    switch (activity.type) {
      case 'function':
        if (activity.name.includes('Fill User Form')) {
          return `${baseDescription} via voice command`
        }
        if (activity.name.includes('Add Family Member')) {
          return `${baseDescription} through voice interaction`
        }
        break
      case 'listening':
        return `${baseDescription} - Ready for voice input`
      case 'speaking':
        return `${baseDescription} - Providing guidance`
      case 'processing':
        return `${baseDescription} - Analyzing voice input`
    }

    return baseDescription
  }

  if (compact) {
    return (
      <Card className={cn("border-border/50 bg-card/30", className)}>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm flex items-center gap-2">
            <Brain className="h-4 w-4 text-primary" />
            Mae Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {recentActivities.length > 0 ? (
              recentActivities.slice(0, 3).map((activity, index) => {
                const Icon = getActivityIcon(activity)
                return (
                  <motion.div
                    key={activity.id}
                    initial={{ opacity: 0, x: -5 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.05 }}
                    className="flex items-center gap-2 p-2 bg-muted/20 rounded-md"
                  >
                    <Icon className={cn("h-3 w-3 flex-shrink-0", getStatusColor(activity.status))} />
                    <span className="text-xs text-muted-foreground flex-1 truncate">
                      {getEnhancedDescription(activity)}
                    </span>
                    <div className={cn(
                      "w-1.5 h-1.5 rounded-full flex-shrink-0",
                      activity.status === 'completed' && "bg-green-500",
                      activity.status === 'active' && "bg-primary animate-pulse",
                      activity.status === 'error' && "bg-red-500"
                    )} />
                  </motion.div>
                )
              })
            ) : (
              <div className="text-center py-3">
                <p className="text-xs text-muted-foreground">No recent activity</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn("space-y-4", className)}
    >
      <Card className="border-border/50 bg-card/50 backdrop-blur-sm">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-lg flex items-center gap-2">
              <Brain className="h-5 w-5 text-primary" />
              Mae Activity Feed
            </CardTitle>
            <div className="flex items-center gap-2">
              {isSessionActive && (
                <Badge variant="outline" className="border-primary/50 text-primary">
                  <div className="w-2 h-2 rounded-full bg-primary animate-pulse mr-2" />
                  Live
                </Badge>
              )}
              <Badge variant="secondary" className="text-xs">
                {recentActivities.length} events
              </Badge>
            </div>
          </div>
        </CardHeader>

        <CardContent>
          <ScrollArea className="h-64">
            <AnimatePresence>
              {recentActivities.length > 0 ? (
                <div className="space-y-3">
                  {recentActivities.map((activity, index) => {
                    const Icon = getActivityIcon(activity)
                    return (
                      <motion.div
                        key={activity.id}
                        initial={{ opacity: 0, y: 10, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: -10, scale: 0.95 }}
                        transition={{ delay: index * 0.02 }}
                        className={cn(
                          "flex items-start gap-3 p-3 rounded-lg border transition-all duration-200",
                          activity.status === 'active' && "bg-primary/5 border-primary/20",
                          activity.status === 'completed' && "bg-green-500/5 border-green-500/20",
                          activity.status === 'error' && "bg-red-500/5 border-red-500/20",
                          activity.status !== 'active' && activity.status !== 'completed' && activity.status !== 'error' && "bg-muted/30 border-border/30"
                        )}
                      >
                        <div className={cn(
                          "w-8 h-8 rounded-lg flex items-center justify-center flex-shrink-0 transition-all",
                          activity.status === 'active' && "bg-primary/20",
                          activity.status === 'completed' && "bg-green-500/20",
                          activity.status === 'error' && "bg-red-500/20",
                          activity.status !== 'active' && activity.status !== 'completed' && activity.status !== 'error' && "bg-muted/50"
                        )}>
                          <Icon className={cn("h-4 w-4", getStatusColor(activity.status))} />
                        </div>
                        
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <span className="text-sm font-medium truncate">
                              {activity.name}
                            </span>
                            <Badge 
                              variant="outline" 
                              className={cn(
                                "text-xs",
                                activity.status === 'active' && "border-primary/50 text-primary",
                                activity.status === 'completed' && "border-green-500/50 text-green-600",
                                activity.status === 'error' && "border-red-500/50 text-red-600"
                              )}
                            >
                              {activity.status}
                            </Badge>
                          </div>
                          <p className="text-xs text-muted-foreground">
                            {getEnhancedDescription(activity)}
                          </p>
                          {showTimestamps && (
                            <div className="flex items-center gap-1 mt-1">
                              <Clock className="h-3 w-3 text-muted-foreground" />
                              <span className="text-xs text-muted-foreground">
                                {new Date(activity.timestamp).toLocaleTimeString()}
                              </span>
                            </div>
                          )}
                        </div>
                      </motion.div>
                    )
                  },)}
                </div>
              ) : (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  className="text-center py-8"
                >
                  <Eye className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                  <p className="text-sm text-muted-foreground">
                    No recent Mae activities
                  </p>
                  <p className="text-xs text-muted-foreground mt-1">
                    Activities will appear here as Mae assists you
                  </p>
                </motion.div>
              )}
            </AnimatePresence>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Current Voice State */}
      <AnimatePresence>
        {sessionState?.isRecording && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
          >
            <Card className="border-primary/50 bg-gradient-to-r from-primary/10 to-primary/5">
              <CardContent className="p-4">
                <div className="flex items-center gap-3">
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 1, repeat: Infinity }}
                    className="w-10 h-10 rounded-full bg-primary/20 flex items-center justify-center"
                  >
                    <Mic className="h-5 w-5 text-primary" />
                  </motion.div>
                  <div className="flex-1">
                    <p className="text-sm font-medium text-primary">Mae is listening...</p>
                    <p className="text-xs text-muted-foreground">Speak naturally to continue</p>
                  </div>
                  <div className="flex items-center gap-1">
                    {Array.from({ length: 4 }, (_, i) => (
                      <motion.div
                        key={i}
                        className="w-1 bg-primary rounded-full"
                        animate={{
                          height: [4, 16, 4],
                          opacity: [0.4, 1, 0.4]
                        }}
                        transition={{
                          duration: 0.6,
                          repeat: Infinity,
                          delay: i * 0.1
                        }}
                      />
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}
