'use client';

import React, { useState, useEffect } from 'react';
import { HealthcareProvider } from '@/lib/location-function-tools';
import { AnimatePresence, motion } from 'framer-motion';
import { X, Hospital, Pill, Heart, Stethoscope, MapPin, Phone, Star } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { APIProvider, Map, AdvancedMarker, InfoWindow } from '@vis.gl/react-google-maps';
import { useCopilotAction, useCopilotReadable } from '@copilotkit/react-core';
import { useMapsApiKey } from '@/hooks/use-maps-api-key';

// Map control state interface
interface MapControlState {
  isVisible: boolean;
  searchResults: HealthcareProvider[];
  searchQuery: string;
  searchType: 'pediatricians' | 'hospitals' | 'urgent_care' | 'pharmacies';
  userLocation: { lat: number; lng: number } | null;
  mapCenter: { lat: number; lng: number };
  selectedProvider: HealthcareProvider | null;
  hoveredProvider: HealthcareProvider | null;
}

// Location state interface for AG-UI state sharing
interface LocationState {
  currentLocation: string;
  coordinates: { lat: number; lng: number } | null;
  lastSearchQuery: string;
  searchResults: HealthcareProvider[];
}

export function MaeControlledMap() {
  const { apiKey: mapsApiKey, loading: mapsLoading, error: mapsError } = useMapsApiKey();
  const [mapState, setMapState] = useState<MapControlState>({
    isVisible: false,
    searchResults: [],
    searchQuery: '',
    searchType: 'pediatricians',
    userLocation: null,
    mapCenter: { lat: 40.7128, lng: -74.0060 }, // Default fallback
    selectedProvider: null,
    hoveredProvider: null
  });

  // AG-UI state management for location sharing
  const [locationState, setLocationState] = useState<LocationState>({
    currentLocation: '',
    coordinates: null,
    lastSearchQuery: '',
    searchResults: []
  });

  // Make location state readable by the Agent
  useCopilotReadable({
    description: 'Current user location and search state for healthcare providers',
    value: locationState
  });

  // Function to get the appropriate icon for different provider types
  const getProviderIcon = (searchType: string, index: number) => {
    const iconColor = '#00bba7';
    const textColor = 'white';
    
    let iconSvg = '';
    
    switch (searchType) {
      case 'pharmacies':
        iconSvg = `<circle cx="16" cy="16" r="12" fill="${iconColor}" stroke="white" stroke-width="2"/>
                   <circle cx="16" cy="10" r="3" fill="${textColor}"/>
                   <rect x="13" y="18" width="6" height="4" rx="2" fill="${textColor}"/>`;
        break;
      case 'hospitals':
        iconSvg = `<circle cx="16" cy="16" r="12" fill="${iconColor}" stroke="white" stroke-width="2"/>
                   <path d="M16 8v8M12 12h8" stroke="${textColor}" stroke-width="2" stroke-linecap="round"/>`;
        break;
      case 'urgent_care':
        iconSvg = `<circle cx="16" cy="16" r="12" fill="${iconColor}" stroke="white" stroke-width="2"/>
                   <path d="M16 8v8M12 12h8" stroke="${textColor}" stroke-width="1.5" stroke-linecap="round"/>
                   <circle cx="16" cy="16" r="3" fill="none" stroke="${textColor}" stroke-width="1"/>`;
        break;
      default: // pediatricians
        iconSvg = `<circle cx="16" cy="16" r="12" fill="${iconColor}" stroke="white" stroke-width="2"/>
                   <path d="M12 14c0-2.2 1.8-4 4-4s4 1.8 4 4" stroke="${textColor}" stroke-width="1.5" stroke-linecap="round"/>
                   <circle cx="16" cy="11" r="2" fill="${textColor}"/>`;
        break;
    }

    return `data:image/svg+xml;charset=UTF-8,${encodeURIComponent(`
      <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
        ${iconSvg}
        <text x="16" y="26" text-anchor="middle" fill="white" font-size="10" font-weight="bold">${index + 1}</text>
      </svg>
    `)}`;
  };

  // Helper function to get map center from search results
  const getMapCenter = (providers: HealthcareProvider[], userLocation?: { lat: number; lng: number } | null): { lat: number; lng: number } => {
    // If we have user location, use it
    if (userLocation) {
      return userLocation;
    }
    
    // If we have providers, use the first one's location
    if (providers && providers.length > 0 && providers[0].coordinates) {
      return providers[0].coordinates;
    }
    
    // Fallback to default center
    return { lat: 40.7128, lng: -74.0060 };
  };

  // Test function to manually trigger map display for debugging
  const testMapDisplay = () => {
    console.log('🧪 Testing map display with sample data');
    const sampleProviders = [
      {
        name: "Test Pediatrician",
        address: "123 Main St, Test City, TC 12345",
        phone: "(*************",
        rating: 4.5,
        reviews_count: 100,
        distance: 2.3,
        coordinates: { lat: 40.7128, lng: -74.0060 }
      }
    ];

    const newMapCenter = getMapCenter(sampleProviders);
    
    setMapState({
      isVisible: true,
      searchResults: sampleProviders,
      searchQuery: 'Test Location',
      searchType: 'pediatricians',
      userLocation: newMapCenter,
      mapCenter: newMapCenter,
      selectedProvider: null,  // Added these lines!! IF ERROR HAPPENS
      hoveredProvider: null
    });

    // Update AG-UI state
    setLocationState({
      currentLocation: 'Test Location',
      coordinates: newMapCenter,
      lastSearchQuery: 'Test Location',
      searchResults: sampleProviders
    });
  };

  // Listen for map control events from Mae (Gemini Live)
  useEffect(() => {
    const handleShowMap = (event: any) => {
      console.log('🗺️ Received showMaeMap event:', event.detail);
      const { searchResults, searchQuery, searchType } = event.detail;

      // Ensure searchResults is properly formatted
      const providers = Array.isArray(searchResults) ? searchResults :
                       searchResults?.providers ? searchResults.providers : [];

      console.log('🗺️ Processed providers:', providers);
      console.log('🗺️ Provider count from event:', providers.length);
      console.log('🗺️ First provider sample:', providers[0]);

      // Calculate the appropriate map center
      const newMapCenter = getMapCenter(providers, locationState.coordinates);
      console.log('🗺️ New map center:', newMapCenter);

      setMapState({
        isVisible: true,
        searchResults: providers,
        searchQuery: searchQuery || '',
        searchType: searchType || 'pediatricians',
        userLocation: locationState.coordinates,
        mapCenter: newMapCenter,
        selectedProvider: null,  // Added these lines!! IF ERROR HAPPENS
        hoveredProvider: null
      });

      // Update AG-UI state
      setLocationState(prev => ({
        ...prev,
        lastSearchQuery: searchQuery || '',
        searchResults: providers
      }));

      console.log('🗺️ Map state updated - should be visible now');
    };

    const handleHideMap = () => {
      console.log('🗺️ Received hideMaeMap event');
      setMapState(prev => ({ ...prev, isVisible: false }));
    };

    // Listen to window events from the Gemini Live hook
    if (typeof window !== 'undefined') {
      window.addEventListener('showMaeMap', handleShowMap);
      window.addEventListener('hideMaeMap', handleHideMap);
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('showMaeMap', handleShowMap);
        window.removeEventListener('hideMaeMap', handleHideMap);
      }
    };
  }, [locationState.coordinates]);

  // CopilotKit action for updating user location
  useCopilotAction({
    name: 'updateUserLocation',
    description: 'Update the user\'s current location for healthcare searches',
    parameters: [
      {
        name: 'location',
        type: 'string',
        description: 'The user\'s location (city, address, or zip code)'
      },
      {
        name: 'coordinates',
        type: 'object',
        description: 'Latitude and longitude coordinates for the location'
      }
    ],
    handler: async ({ location, coordinates }) => {
      console.log('🌍 Updating user location:', { location, coordinates });
      
      setLocationState(prev => ({
        ...prev,
        currentLocation: location || '',
        coordinates: coordinates as { lat: number; lng: number } | null
      }));
      
      return `User location updated to ${location}`;
    }
  });

  // Also provide CopilotKit actions for web chat interface
  useCopilotAction({
    name: 'showHealthcareMap',
    description: 'Display an interactive map showing healthcare providers to the user',
    parameters: [
      {
        name: 'searchResults',
        type: 'object',
        description: 'Array of healthcare providers to display on the map'
      },
      {
        name: 'searchQuery',
        type: 'string',
        description: 'The location that was searched'
      },
      {
        name: 'searchType',
        type: 'string',
        description: 'Type of providers being shown'
      }
    ],
    handler: async ({ searchResults, searchQuery, searchType }) => {
      console.log('🗺️ CopilotKit showing map with results:', { searchResults, searchQuery, searchType });
      console.log('🗺️ Provider count:', (searchResults as HealthcareProvider[])?.length || 0);
      console.log('🗺️ First provider:', (searchResults as HealthcareProvider[])?.[0]);
      
      // Ensure searchResults is properly formatted
      const providers = Array.isArray(searchResults) ? searchResults : 
                       (searchResults as any)?.providers ? (searchResults as any).providers : [];
      
      // Calculate the appropriate map center
      const newMapCenter = getMapCenter(providers, locationState.coordinates);
      console.log('🗺️ CopilotKit map center:', newMapCenter);
      
      setMapState({
        isVisible: true,
        searchResults: providers,
        searchQuery: searchQuery || '',
        searchType: (searchType as 'pediatricians' | 'hospitals' | 'urgent_care' | 'pharmacies') || 'pediatricians',
        userLocation: locationState.coordinates,
        mapCenter: newMapCenter,
        selectedProvider: null,  // Added these lines!! IF ERROR HAPPENS
        hoveredProvider: null
      });

      // Update AG-UI state
      setLocationState(prev => ({
        ...prev,
        lastSearchQuery: searchQuery || '',
        searchResults: providers
      }));
      
      return `Map is now displayed showing ${providers.length} ${searchType || 'providers'} near ${searchQuery || 'your location'}`;
    }
  });

  useCopilotAction({
    name: 'hideHealthcareMap',
    description: 'Hide the healthcare provider map from view',
    handler: async () => {
      console.log('🗺️ CopilotKit hiding map');
      setMapState(prev => ({ ...prev, isVisible: false }));
      return 'Map has been hidden';
    }
  });

  const handleCloseMap = () => {
    setMapState({
      isVisible: false,
      searchResults: [],
      searchQuery: '',
      searchType: 'pediatricians',
      userLocation: null,
      mapCenter: { lat: 40.7128, lng: -74.0060 },
      selectedProvider: null,
      hoveredProvider: null
    });
  };

  return (
    <>
      {/* Debug button - only in development */}
      {/* {process.env.NODE_ENV === 'development' && (
        <div className="fixed top-4 right-4 z-50">
          <button
            onClick={testMapDisplay}
            className="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 rounded text-sm"
          >
            Test Map
          </button>
        </div>
      )} */}

      <AnimatePresence>
        {mapState.isVisible && (
          <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 50 }}
          transition={{ duration: 0.3 }}
          className="fixed inset-0 z-50 bg-background/80 backdrop-blur-sm flex items-center justify-center p-2 sm:p-4"
        >
          <motion.div
            initial={{ scale: 0.9 }}
            animate={{ scale: 1 }}
            exit={{ scale: 0.9 }}
            className="w-full h-full sm:max-w-[95vw] sm:max-h-[90vh] sm:h-auto bg-background rounded-lg shadow-2xl border overflow-hidden"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 border-b bg-card">
              <div>
                <h2 className="text-xl font-semibold text-primary">
                  Healthcare Providers Map
                </h2>
                {mapState.searchQuery && (
                  <p className="text-sm text-muted-foreground">
                    Showing {mapState.searchType.replace('_', ' ')} near {mapState.searchQuery}
                  </p>
                )}
              </div>
              <Button 
                variant="ghost" 
                size="icon"
                onClick={handleCloseMap}
                className="text-muted-foreground hover:text-foreground"
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Map Content */}
            <div className="flex flex-col h-[calc(100vh-8rem)] sm:h-[calc(90vh-8rem)]">
              <HealthcareMapDisplay 
                providers={mapState.searchResults}
                searchQuery={mapState.searchQuery}
                searchType={mapState.searchType}
                mapCenter={mapState.mapCenter}
                selectedProvider={mapState.selectedProvider}
                hoveredProvider={mapState.hoveredProvider}
                onProviderSelect={(provider) => setMapState(prev => ({ ...prev, selectedProvider: provider }))}
                onProviderHover={(provider) => setMapState(prev => ({ ...prev, hoveredProvider: provider }))}
                getProviderIcon={getProviderIcon}
                mapsApiKey={mapsApiKey}
                mapsLoading={mapsLoading}
                mapsError={mapsError}
              />
            </div>
          </motion.div>
        </motion.div>
        )}
      </AnimatePresence>
    </>
  );
}

function HealthcareMapDisplay({
  providers,
  searchQuery,
  searchType,
  mapCenter,
  selectedProvider,
  hoveredProvider,
  onProviderSelect,
  onProviderHover,
  getProviderIcon,
  mapsApiKey,
  mapsLoading,
  mapsError
}: {
  providers: HealthcareProvider[];
  searchQuery: string;
  searchType: string;
  mapCenter: { lat: number; lng: number };
  selectedProvider: HealthcareProvider | null;
  hoveredProvider: HealthcareProvider | null;
  onProviderSelect: (provider: HealthcareProvider) => void;
  onProviderHover: (provider: HealthcareProvider | null) => void;
  getProviderIcon: (searchType: string, index: number) => string;
  mapsApiKey: string | null;
  mapsLoading: boolean;
  mapsError: string | null;
}) {
  console.log('🗺️ HealthcareMapDisplay rendering with:', {
    providersCount: providers.length,
    searchQuery,
    searchType,
    mapCenter,
    firstProvider: providers[0]
  });

  if (!providers || providers.length === 0) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <p className="text-muted-foreground">No healthcare providers found for this location.</p>
          <p className="text-sm text-muted-foreground mt-2">Try searching for a different area or type of provider.</p>
        </div>
      </div>
    );
  }

  // Show loading state while fetching API key
  if (mapsLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="mt-2 text-sm text-muted-foreground">Loading Maps...</p>
        </div>
      </div>
    );
  }

  // Show error state if API key fetch failed
  if (mapsError) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600">Failed to load Maps API</p>
          <p className="text-sm text-muted-foreground mt-2">{mapsError}</p>
        </div>
      </div>
    );
  }

  // Show message if no API key available
  if (!mapsApiKey) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <p className="text-muted-foreground">Maps API key not available</p>
          <p className="text-sm text-muted-foreground mt-2">Please check your configuration</p>
        </div>
      </div>
    );
  }


  return (
    <div className="h-full flex flex-col lg:flex-row gap-2 sm:gap-4">
      {/* Map Section - takes up more space */}
      <div className="flex-1 min-h-0 order-1">
        <APIProvider apiKey={mapsApiKey}>
          <div className="h-full w-full rounded-lg overflow-hidden border">
            <Map
              defaultCenter={mapCenter}
              center={mapCenter}
              defaultZoom={13}
              mapId="healthcare-map"
              gestureHandling="greedy"
              disableDefaultUI={false}
              zoomControl={true}
              mapTypeControl={true}
              streetViewControl={true}
              fullscreenControl={true}
              scaleControl={true}
              rotateControl={false}
              clickableIcons={true}
              keyboardShortcuts={true}
              scrollwheel={true}
              options={{ 
                restriction: {
                  latLngBounds: {
                    north: 85,
                    south: -85,
                    west: -180,
                    east: 180,
                  },
                  strictBounds: false,
                },
                minZoom: 3,
                maxZoom: 20,
              }}
            >
              {providers.map((provider, index) => (
                provider.coordinates && (
                  <React.Fragment key={index}>
                    <AdvancedMarker
                      position={provider.coordinates}
                      title={provider.name}
                      onClick={() => onProviderSelect(provider)}
                    >
                      <div 
                        className="cursor-pointer transform hover:scale-110 transition-transform"
                        onMouseEnter={() => onProviderHover(provider)}
                        onMouseLeave={() => onProviderHover(null)}
                      >
                        <img 
                          src={getProviderIcon(searchType, index)} 
                          alt={`${provider.name} marker`}
                          width={32}
                          height={32}
                        />
                      </div>
                    </AdvancedMarker>
                    
                    {(selectedProvider === provider || hoveredProvider === provider) && (
                      <InfoWindow
                        position={provider.coordinates}
                        onCloseClick={() => {
                          onProviderSelect(null as any);
                          onProviderHover(null);
                        }}
                      >
                        <div className="p-2 max-w-xs">
                          <div className="flex items-center gap-2 mb-2">
                            <span className="bg-primary text-primary-foreground text-xs px-1.5 py-0.5 rounded">
                              {index + 1}
                            </span>
                            <h4 className="font-semibold text-sm text-gray-800">{provider.name}</h4>
                          </div>
                          
                          <div className="space-y-1 text-xs text-gray-600">
                            <div className="flex items-start gap-1">
                              <MapPin className="h-3 w-3 mt-0.5 flex-shrink-0" />
                              <span className="break-words">{provider.address}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Phone className="h-3 w-3 flex-shrink-0" />
                              <span>{provider.phone}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Star className="h-3 w-3 fill-yellow-400 text-yellow-400 flex-shrink-0" />
                              <span>{provider.rating} ({provider.reviews_count}) • {provider.distance} mi</span>
                            </div>
                          </div>
                          
                          <div className="mt-2 pt-2 border-t">
                            <Button 
                              size="sm" 
                              className="w-full text-xs h-7"
                              onClick={() => {
                                const query = encodeURIComponent(provider.address);
                                window.open(`https://www.google.com/maps/dir/?api=1&destination=${query}`, '_blank');
                              }}
                            >
                              Get Directions
                            </Button>
                          </div>
                        </div>
                      </InfoWindow>
                    )}
                  </React.Fragment>
                )
              ))}
            </Map>
          </div>
        </APIProvider>
      </div>

      {/* Provider list sidebar - smaller and scrollable */}
      <div className="w-full lg:w-80 flex flex-col min-h-0 order-2 max-h-48 lg:max-h-none">
        <div className="mb-2">
          <h3 className="font-semibold text-sm text-muted-foreground">
            {providers.length} Provider{providers.length !== 1 ? 's' : ''} Found
          </h3>
        </div>
        <div className="flex-1 overflow-y-auto space-y-2 pr-2">
          {providers.map((provider, index) => (
            <div 
              key={index} 
              className={`flex items-start gap-2 sm:gap-3 p-2 sm:p-3 bg-card rounded-lg border cursor-pointer transition-all ${
                selectedProvider === provider ? 'border-primary shadow-md bg-primary/5' : 'hover:shadow-sm hover:border-gray-300'
              }`}
              onClick={() => onProviderSelect(provider)}
              onMouseEnter={() => onProviderHover(provider)}
              onMouseLeave={() => onProviderHover(null)}
            >
              <span className="bg-primary text-primary-foreground text-xs px-1.5 py-0.5 sm:px-2 sm:py-1 rounded flex-shrink-0 mt-0.5">
                {index + 1}
              </span>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-start justify-between gap-2 mb-1">
                  <h4 className="font-medium text-xs sm:text-sm leading-tight break-words">{provider.name}</h4>
                  <div className="text-right flex-shrink-0">
                    <div className="flex items-center gap-1 text-xs text-muted-foreground">
                      <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                      <span>{provider.rating}</span>
                    </div>
                    <p className="text-xs font-medium">{provider.distance} mi</p>
                  </div>
                </div>
                
                <p className="text-xs text-muted-foreground mb-1 truncate">{provider.address}</p>
                <p className="text-xs text-muted-foreground mb-2">{provider.phone}</p>
                
                <Button 
                  size="sm" 
                  variant="outline" 
                  className="text-xs h-6 sm:h-7 w-full"
                  onClick={(e) => {
                    e.stopPropagation();
                    const query = encodeURIComponent(provider.address);
                    window.open(`https://www.google.com/maps/dir/?api=1&destination=${query}`, '_blank');
                  }}
                >
                  Get Directions
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

// Enhanced map component with proper centering
function HealthcareMapWithProviders({
  providers,
  searchQuery,
  searchType,
  mapCenter
}: {
  providers: HealthcareProvider[];
  searchQuery: string;
  searchType: string;
  mapCenter: { lat: number; lng: number };
}) {
  return (
    <HealthcareMapDisplay 
      providers={providers}
      searchQuery={searchQuery}
      searchType={searchType}
      mapCenter={mapCenter}
      selectedProvider={null}
      hoveredProvider={null}
      onProviderSelect={() => {}}
      onProviderHover={() => {}}
      getProviderIcon={() => ''}
    />
  );
}

// Hook for other components to control the map
export function useMaeMapControl() {
  const showMap = (searchResults: HealthcareProvider[], searchQuery: string, searchType: string) => {
    if (typeof window !== 'undefined') {
      const mapEvent = new CustomEvent('showMaeMap', {
        detail: { searchResults, searchQuery, searchType }
      });
      window.dispatchEvent(mapEvent);
    }
  };

  const hideMap = () => {
    if (typeof window !== 'undefined') {
      const mapEvent = new CustomEvent('hideMaeMap');
      window.dispatchEvent(mapEvent);
    }
  };

  return { showMap, hideMap };
}

export default MaeControlledMap;