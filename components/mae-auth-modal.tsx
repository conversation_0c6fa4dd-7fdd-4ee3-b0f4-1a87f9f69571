'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alogContent, DialogHeader, DialogTitle, DialogDescription } from '@/components/ui/dialog'
import { AuthForms } from './auth/auth-forms'
import { useAuth } from '@/lib/auth-provider'

interface MaeAuthModalProps {
  // No props needed - controlled by Mae events
}

export function MaeAuthModal() {
  const { user } = useAuth()
  const [isOpen, setIsOpen] = useState(false)
  const [authMode, setAuthMode] = useState<'signup' | 'login' | 'both'>('both')
  const [message, setMessage] = useState('')
  const [requiredFor, setRequiredFor] = useState('')
  const [prefillEmail, setPrefillEmail] = useState('')

  useEffect(() => {
    console.log('🔐 Mae auth modal: Setting up event listeners')
    
    const handleShowAuth = (event: CustomEvent) => {
      console.log('🔐 Mae auth modal: Received show auth event:', event.detail)
      const { mode, message, required_for, email } = event.detail
      
      setAuthMode(mode || 'both')
      setMessage(message || 'Please sign up or log in to continue with your personalized experience.')
      setRequiredFor(required_for || 'continuing')
      setPrefillEmail(email || '')
      setIsOpen(true)
      
      console.log('🔐 Mae auth modal: Modal should now be open, isOpen:', true)
    }

    const handleHideAuth = () => {
      console.log('🔐 Mae auth modal: Received hide auth event')
      setIsOpen(false)
    }

    // Listen for Mae auth events
    window.addEventListener('mae-show-auth', handleShowAuth as EventListener)
    window.addEventListener('mae-hide-auth', handleHideAuth)

    console.log('🔐 Mae auth modal: Event listeners attached')

    return () => {
      console.log('🔐 Mae auth modal: Removing event listeners')
      window.removeEventListener('mae-show-auth', handleShowAuth as EventListener)
      window.removeEventListener('mae-hide-auth', handleHideAuth)
    }
  }, [])

  // Auto-close when user successfully authenticates
  useEffect(() => {
    if (user && isOpen) {
      console.log('🔐 Mae auth modal: User authenticated, closing modal')
      setIsOpen(false)
    }
  }, [user, isOpen])

  const handleSuccess = () => {
    console.log('🔐 Mae auth modal: Authentication successful')
    setIsOpen(false)
  }

  console.log('🔐 Mae auth modal: Rendering with isOpen:', isOpen, 'authMode:', authMode)

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="text-center">
            {authMode === 'signup' ? 'Create Your Account' : 
             authMode === 'login' ? 'Sign In' : 
             'Sign In or Create Account'}
          </DialogTitle>
          <DialogDescription className="text-center space-y-2">
            <span>{message}</span>
            {requiredFor && (
              <span className="text-sm text-muted-foreground block">
                This is required for {requiredFor}.
              </span>
            )}
          </DialogDescription>
        </DialogHeader>
        
        <div className="mt-4">
          <AuthForms 
            onSuccess={handleSuccess}
            defaultEmail={prefillEmail}
            mode={authMode}
          />
        </div>
      </DialogContent>
    </Dialog>
  )
}