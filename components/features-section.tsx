"use client"

import * as React from "react"
import { Brain, Clock, Shield, Zap, Users, Heart, MapPin } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import { Separator } from "@radix-ui/react-separator"
import { motion, useInView, easeOut } from "framer-motion"
import { useRef } from "react"

const features = [
  // {
  //   icon: Brain,
  //   title: "AI-Powered Answers",
  //   description: "Get instant, personalized pediatric guidance powered by advanced AI algorithms trained on medical literature.",
  //   badge: "Smart AI",
  //   color: "text-blue-500"
  // },
  {
    icon: Clock,
    title: "24/7 Availability",
    description: "Access trusted healthcare guidance anytime, anywhere. No more waiting for office hours or appointments.",
    badge: "Always On",
    color: "text-green-500"
  },
  {
    icon: Shield,
    title: "Privacy Protected",
    description: "Your family's health data is encrypted and secure. You maintain full control over your information.",
    badge: "Secure",
    color: "text-purple-500"
  },
  {
    icon: Zap,
    title: "Instant Insights",
    description: "Smart agents that take action, track symptoms, and coordinate care automatically for your peace of mind.",
    badge: "Fast",
    color: "text-yellow-500"
  },
  {
    icon: Users,
    title: "Family Focused",
    description: "Designed specifically for parents and caregivers with child-centric health tracking and guidance.",
    badge: "Family First",
    color: "text-pink-500"
  },
  {
    icon: Heart,
    title: "Evidence-Based",
    description: "All recommendations are backed by peer-reviewed medical research and pediatric best practices.",
    badge: "Trusted",
    color: "text-red-500"
  },
  {
    icon: MapPin,
    title: "Find Local Providers",
    description: "Locate trusted pediatricians, hospitals, urgent care, and pharmacies in your area with interactive maps.",
    badge: "Local Care",
    color: "text-teal-500"
  }
]

const stats = [
  { value: "10,000+", label: "Families Served" },
  { value: "99.9%", label: "Uptime" },
  { value: "24/7", label: "Support" },
  { value: "HIPAA", label: "Compliant" }
]

export function FeaturesSection() {
  const ref = useRef(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  const headerRef = useRef(null)
  const isHeaderInView = useInView(headerRef, { once: true, margin: "-50px" })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: easeOut,
      },
    },
  }

  return (
    <section className="py-28 mb-16">
      <div className="container mx-auto px-4 space-y-10">
        {/* Header */}
        <motion.div
          ref={headerRef}
          initial={{ opacity: 0, y: 50 }}
          animate={isHeaderInView ? { opacity: 1, y: 0 } : {}}
          transition={{ duration: 0.8, ease: "easeOut" }}
        >
          <motion.h2
            initial={{ opacity: 0, y: 30 }}
            animate={isHeaderInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="text-4xl md:text-4xl font-inter font-light tracking-tight text-center mb-5"
            style={{ lineHeight: '1.4' }}
          >
            <span className="bg-gradient-to-r from-teal-500 to-blue-500 bg-clip-text text-transparent">
              Meet Mae—
            </span> <br />
            an agentic, voice-first AI who listens, triages,<br />
            teaches, and grows with your family over time.
          </motion.h2>
          <motion.p 
            initial={{ opacity: 0, y: 30 }}
            animate={isHeaderInView ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="text-center text-muted-foreground font-inter font-light max-w-3xl mb-16 mx-auto leading-relaxed"
          >
          </motion.p>

          <div className="text-center space-y-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={isHeaderInView ? { opacity: 1, scale: 1 } : {}}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <Badge variant="outline" className="bg-gradient-to-r from-teal-500 to-blue-400 text-white w-fit h-8 mx-auto px-10 mb-10 text-md border-gradient-to-r from-teal-500 to-blue-400 font-inter font-medium">
                Why Choose Our Kidz
              </Badge>
            </motion.div>

            <motion.h2 
              initial={{ opacity: 0, y: 30 }}
              animate={isHeaderInView ? { opacity: 1, y: 0 } : {}}
              transition={{ duration: 0.8, delay: 0.8 }}
              className="text-3xl md:text-4xl font-inter font-light tracking-tight text-center mb-10"
            >
              AI-Powered Pediatric Care, Personalized for Your Child
            </motion.h2>
            <div className="flex flex-col items-center justify-center">
              <motion.p 
                initial={{ opacity: 0, y: 30 }}
                animate={isHeaderInView ? { opacity: 1, y: 0 } : {}}
                transition={{ duration: 0.8, delay: 1.0 }}
                className="text-muted-foreground font-inter font-light text-center max-w-3xl leading-relaxed mb-20"
              >
                Built from the ground up, Our Kidz utilizes a deeply integrated AI layer further enhanced by Model Context Protocol (MCP) support to recognize symptoms, 
                deliver trusted guidance, and connect you to care—all in a heartbeat.
              </motion.p>
            </div>
          </div>
        </motion.div>

        {/* Stats */}
        {/* <div className="grid grid-cols-2 md:grid-cols-4 gap-56">
          {stats.map((stat, index) => (
            <div key={index} className="text-center">
              <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-gray-700 via-teal-600 to-teal-500 dark:from-white dark:to-teal-500 text-transparent bg-clip-text">
                {stat.value}
              </div>
              <div className="text-sm text-muted-foreground mb-10">
                {stat.label}
              </div>
            </div>
          ))}
        </div> */}

        {/* Features Grid */}
        <motion.div
          ref={ref}
          variants={containerVariants}
          initial="hidden"
          animate={isInView ? "visible" : "hidden"}
          className="grid md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {features.map((feature, index) => (
            <motion.div
              key={index}
              variants={itemVariants}
              whileHover={{ 
                y: -10,
                transition: { duration: 0.3, ease: "easeOut" }
              }}
            >
              <Card className="group healthcare-card hover:border-2 hover:border-teal-500 cursor-pointer overflow-hidden h-full transition-all duration-300 hover:shadow-2xl">
                <CardHeader className="space-y-4">
                  <div className="flex items-center justify-between">
                    <motion.div 
                      className={`w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center ${feature.color}`}
                      whileHover={{ 
                        scale: 1.1,
                        rotate: 5,
                        transition: { duration: 0.2 }
                      }}
                    >
                      <feature.icon className="h-6 w-6" />
                    </motion.div>
                    <motion.div
                      initial={{ opacity: 0, scale: 0.8 }}
                      animate={{ opacity: 1, scale: 1 }}
                      transition={{ delay: index * 0.1 + 0.5 }}
                    >
                      <Badge variant="secondary" className="text-xs">
                        {feature.badge}
                      </Badge>
                    </motion.div>
                  </div>
                  <CardTitle className="text-xl group-hover:text-primary transition-colors duration-300">
                    {feature.title}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base leading-loose">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* CTA */}
        <div className=" text-center">
          {/* <div className="inline-flex flex-col sm:flex-row gap-4">
            <Button asChild size="lg" className="btn-healthcare bg-[#cbfbf1] text-black">
              <Link href="/products">
                Explore Features
              </Link>
            </Button>
            <Button variant="outline" size="lg" asChild className="btn-healthcare bg-[#14b8a6] text-white">
              <Link href="/demo">
                Schedule Demo
              </Link>
            </Button>
          </div> */}
        </div>
      </div>
    </section>
  )
}
