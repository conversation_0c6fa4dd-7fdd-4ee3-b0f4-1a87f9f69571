'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Separator } from '@/components/ui/separator'
import { User, Calendar, Phone, Mail, MapPin, Heart, AlertCircle, CheckCircle2, Mic, MicOff } from 'lucide-react'
import { cn } from '@/lib/utils'
import confetti from 'canvas-confetti'
import ProfessionalUserForm from '@/components/professional-user-form'
import { 
  aguiEventListener, 
  AG_UI_EVENTS, 
  OnboardingValidator,
  type FillUserFormPayload,
  type SubmitUserFormPayload,
  type AddFamilyMemberPayload,
  type UpdateFamilyMemberPayload,
  type ValidateFormPayload,
  type OnboardingProgressPayload,
  type EventResponse
} from '@/lib/ag-ui-events'
import { motion, AnimatePresence } from 'framer-motion'
import { useUser } from '@clerk/nextjs'

interface UserFormData {
  email: string
  fullName: string
  role: 'parent' | 'guardian' | 'caregiver'
  phoneNumber: string
  zipCode: string
  dateOfBirth: string
  emergencyContact: {
    name: string
    phoneNumber: string
    relationship: string
  }
  preferences: {
    notifications: boolean
    language: string
  }
}

interface FamilyMemberData {
  id?: string
  name: string
  dateOfBirth: string
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say'
  relationship: string
  medical_conditions: string[]
  allergies: string[]
  medications: {
    name: string
    dosage: string
    frequency: string
  }[]
  additional_notes: string
  is_primary: boolean
}

interface OnboardingInterfaceProps {
  initialStep?: 'welcome' | 'user_info' | 'family_info' | 'complete'
  onComplete?: (data: { user: UserFormData; familyMembers: FamilyMemberData[] }) => void
  isVoiceEnabled?: boolean
}

export function OnboardingInterface({ 
  initialStep = 'welcome', 
  onComplete,
  isVoiceEnabled = false 
}: OnboardingInterfaceProps) {
  // Clerk user
  const { user, isLoaded } = useUser()
  
  // State management
  const [currentStep, setCurrentStep] = useState<'welcome' | 'user_info' | 'family_info' | 'complete'>(initialStep)
  const [userForm, setUserForm] = useState<UserFormData>({
    email: '',
    fullName: '',
    role: 'parent',
    phoneNumber: '',
    zipCode: '',
    dateOfBirth: '',
    emergencyContact: {
      name: '',
      phoneNumber: '',
      relationship: ''
    },
    preferences: {
      notifications: true,
      language: 'en'
    }
  })
  const [familyMembers, setFamilyMembers] = useState<FamilyMemberData[]>([])
  const [validationErrors, setValidationErrors] = useState<string[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [confirmationMessage, setConfirmationMessage] = useState('')
  const [messageType, setMessageType] = useState<'success' | 'error' | 'info'>('info')
  const [progress, setProgress] = useState(25)
  const [fillingField, setFillingField] = useState<string | null>(null)
  const [recentlyFilled, setRecentlyFilled] = useState<Set<string>>(new Set())
  const [currentFamilyMember, setCurrentFamilyMember] = useState<Partial<FamilyMemberData> | null>(null)
  const [showLiveFamilyForm, setShowLiveFamilyForm] = useState(false)
  const [familyFormTimer, setFamilyFormTimer] = useState<NodeJS.Timeout | null>(null)
  const [showPlan, setShowPlan] = useState(false)
  // Progress calculation
  const calculateProgress = useCallback(() => {
    const steps = ['welcome', 'user_info', 'family_info', 'complete']
    const stepIndex = steps.indexOf(currentStep)
    return ((stepIndex + 1) / steps.length) * 100
  }, [currentStep])

  // Pre-populate user form with Clerk user data
  useEffect(() => {
    if (!isLoaded) return // Don't do anything while Clerk is loading

    if (user) {
      console.log('Clerk user detected, pre-populating form')
      // Pre-populate user form with Clerk data
      setUserForm(prev => ({
        ...prev,
        email: user.primaryEmailAddress?.emailAddress || '',
        fullName: user.fullName || user.firstName || user.primaryEmailAddress?.emailAddress?.split('@')[0] || ''
      }))
    }
  }, [user, isLoaded])

  // Update progress when step changes
  useEffect(() => {
    setProgress(calculateProgress())
  }, [currentStep, calculateProgress])

  // Clear messages after timeout
  useEffect(() => {
    if (confirmationMessage) {
      const timer = setTimeout(() => {
        setConfirmationMessage('')
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [confirmationMessage])

  // Initialize component and load temporary data from Mae's navigation
  useEffect(() => {
    console.log('OnboardingInterface initialized', { initialStep, isVoiceEnabled })

    // Load temporary data from sessionStorage (set by global AG-UI handler)
    try {
      const tempUserData = sessionStorage.getItem('mae-temp-user-data')
      const tempFamilyMembers = sessionStorage.getItem('mae-temp-family-members')
      const currentStepFromMae = sessionStorage.getItem('mae-current-step')
      const shouldSubmit = sessionStorage.getItem('mae-should-submit')

      if (tempUserData) {
        const userData = JSON.parse(tempUserData)
        console.log('📥 Loading temporary user data from Mae:', userData)
        setUserForm(prev => ({ ...prev, ...userData }))
        sessionStorage.removeItem('mae-temp-user-data')
      }

      if (tempFamilyMembers) {
        const familyData = JSON.parse(tempFamilyMembers)
        console.log('📥 Loading temporary family members from Mae:', familyData)
        setFamilyMembers(familyData)
        sessionStorage.removeItem('mae-temp-family-members')
      }

      if (currentStepFromMae) {
        console.log('📥 Loading current step from Mae:', currentStepFromMae)
        setCurrentStep(currentStepFromMae as any)
        sessionStorage.removeItem('mae-current-step')
      }

      if (shouldSubmit === 'true') {
        console.log('📥 Mae requested form submission - will submit after data loads')
        sessionStorage.removeItem('mae-should-submit')
        // Submit form after a brief delay to allow data to load
        setTimeout(() => {
          console.log('🤖 Auto-submitting form as requested by Mae')
          // Trigger form submission programmatically
          setIsSubmitting(true)
        }, 1000)
      }

      // Check if Mae should continue her session
      const shouldContinueSession = sessionStorage.getItem('mae-continue-session')
      if (shouldContinueSession === 'true') {
        console.log('📥 Mae session should continue - will restart voice interface')
        sessionStorage.removeItem('mae-continue-session')

        // Notify parent component or trigger voice restart
        setTimeout(() => {
          console.log('🎤 Attempting to continue Mae session')
          // Dispatch a custom event to restart Mae
          if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('mae-continue-session'))
          }
        }, 1500)
      }
    } catch (error) {
      console.error('Error loading temporary data from Mae:', error)
    }

    // If voice is enabled, show a welcome message and auto-advance if Mae starts onboarding
    if (isVoiceEnabled) {
      setConfirmationMessage('Mae is ready to help you with onboarding!')
      setMessageType('info')
      
      // Auto-advance from welcome step when Mae starts onboarding
      if (currentStep === 'welcome') {
        console.log('🚀 Mae onboarding detected - auto-advancing from welcome step')
        setTimeout(() => {
          setCurrentStep('user_info')
          setConfirmationMessage('Mae is starting the onboarding process with you!')
          setMessageType('success')
        }, 1500)
      }
    }
  }, [initialStep, isVoiceEnabled, currentStep])

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (familyFormTimer) {
        clearTimeout(familyFormTimer)
      }
    }
  }, [familyFormTimer])

  // Confetti celebration function
  const celebrateCompletion = useCallback(() => {
    // Fire confetti from different directions
    const duration = 3000;
    const animationEnd = Date.now() + duration;
    const defaults = { startVelocity: 30, spread: 360, ticks: 60, zIndex: 0 };

    function randomInRange(min: number, max: number) {
      return Math.random() * (max - min) + min;
    }

    const interval: NodeJS.Timeout = setInterval(function() {
      const timeLeft = animationEnd - Date.now();

      if (timeLeft <= 0) {
        return clearInterval(interval);
      }

      const particleCount = 50 * (timeLeft / duration);
      
      // Fire from left side
      confetti({
        ...defaults,
        particleCount,
        origin: { x: randomInRange(0.1, 0.3), y: Math.random() - 0.2 }
      });
      
      // Fire from right side
      confetti({
        ...defaults,
        particleCount,
        origin: { x: randomInRange(0.7, 0.9), y: Math.random() - 0.2 }
      });
    }, 250);
    
    // Add extra celebration bursts
    setTimeout(() => {
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      });
    }, 500);
    
    setTimeout(() => {
      confetti({
        particleCount: 50,
        angle: 60,
        spread: 55,
        origin: { x: 0 }
      });
      confetti({
        particleCount: 50,
        angle: 120,
        spread: 55,
        origin: { x: 1 }
      });
    }, 1000);
  }, [])

  // Event handlers
  const handleFillUserForm = useCallback(async (payload: FillUserFormPayload, requestId: string): Promise<EventResponse> => {
    try {
      console.log('📝 Filling user form field:', payload, { isVoiceEnabled })

      // Validate payload structure
      if (!payload || typeof payload !== 'object') {
        console.error('Invalid payload received:', payload)
        return {
          success: false,
          error: 'Invalid payload: payload is null or not an object'
        }
      }

      if (!payload.field) {
        console.error('Missing field in payload:', payload)
        return {
          success: false,
          error: 'Invalid payload: field is required'
        }
      }

      if (payload.value === undefined || payload.value === null) {
        console.error('Missing value in payload:', payload)
        return {
          success: false,
          error: 'Invalid payload: value is required'
        }
      }

      // If voice is not enabled, still process but don't update UI
      if (!isVoiceEnabled) {
        return {
          success: true,
          message: `Voice not enabled - would update ${payload.field}`,
          data: { field: payload.field, value: payload.value }
        }
      }

      const newUserForm = { ...userForm }

      if (payload.field === 'emergency_contact') {
        if (typeof payload.value === 'object') {
          newUserForm.emergencyContact = { ...newUserForm.emergencyContact, ...payload.value }
        }
      } else {
        (newUserForm as any)[payload.field] = payload.value
      }

      setUserForm(newUserForm)

      // Validate if requested
      if (payload.validate) {
        const validation = OnboardingValidator.validateUserData(newUserForm)
        if (!validation.valid) {
          setValidationErrors(validation.errors)
          return {
            success: false,
            error: 'Validation failed',
            validationErrors: validation.errors
          }
        }
      }

      return {
        success: true,
        message: `Updated ${payload.field} successfully`,
        data: newUserForm
      }
    } catch (error) {
      console.error('Error filling user form:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }, [userForm, isVoiceEnabled])

  const handleSubmitUserForm = useCallback(async (payload: SubmitUserFormPayload, requestId: string): Promise<EventResponse> => {
    try {
      console.log('📤 Submitting user form:', payload)
      setIsSubmitting(true)
      
      // Validate unless explicitly skipped
      if (payload.validate !== false && !payload.skipValidation) {
        const validation = OnboardingValidator.validateUserData(userForm)
        if (!validation.valid) {
          setValidationErrors(validation.errors)
          setIsSubmitting(false)
          return {
            success: false,
            error: 'Validation failed',
            validationErrors: validation.errors
          }
        }
      }
      
      // Clear validation errors
      setValidationErrors([])
      
      // API call to create/update user
      const response = await fetch('/api/onboarding/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(userForm)
      })
      
      const result = await response.json()
      
      if (!response.ok) {
        setIsSubmitting(false)
        return {
          success: false,
          error: result.error || 'Failed to submit user form'
        }
      }
      
      // Update progress to family info step
      setCurrentStep('family_info')
      setConfirmationMessage('User information saved successfully!')
      setMessageType('success')
      setIsSubmitting(false)
      
      return {
        success: true,
        message: 'User form submitted successfully',
        data: result.user
      }
    } catch (error) {
      console.error('Error submitting user form:', error)
      setIsSubmitting(false)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }, [userForm])

  const handleAddFamilyMember = useCallback(async (payload: AddFamilyMemberPayload, requestId: string): Promise<EventResponse> => {
    try {
      console.log('👨‍👩‍👧‍👦 Adding family member:', payload)

      // Clear any existing timer
      if (familyFormTimer) {
        clearTimeout(familyFormTimer)
        setFamilyFormTimer(null)
      }

      // Show live family form when Mae starts collecting family member data
      if (!showLiveFamilyForm) {
        setShowLiveFamilyForm(true)
        console.log('📝 Showing live family member form as Mae collects data')
      }
      
      // Always update the current family member being built
      setCurrentFamilyMember(prev => ({
        ...prev,
        ...payload,
        name: payload.name || '',
        date_of_birth: payload.date_of_birth || '',
        gender: payload.gender || undefined,
        relationship: payload.relationship || '',
        medical_conditions: [],
        allergies: [],
        medications: [],
        additional_notes: '',
        is_primary: false,
      }))
      console.log('📝 Updating live family member form with new data:', payload)

      // Validate payload structure
      if (!payload || typeof payload !== 'object') {
        console.error('Invalid payload received:', payload)
        return {
          success: false,
          error: 'Invalid payload: payload is null or not an object'
        }
      }

      // if (!payload.name) {
      //   console.error('Missing name in payload:', payload)
      //   return {
      //     success: false,
      //     error: 'Invalid payload: name is required'
      //   }
      // }

      if (!payload.date_of_birth) {
        console.error('Missing date_of_birth in payload:', payload)
        return {
          success: false,
          error: 'Invalid payload: date_of_birth is required'
        }
      }

      if (!payload.relationship) {
        console.error('Missing relationship in payload:', payload)
        return {
          success: false,
          error: 'Invalid payload: relationship is required'
        }
      }

      // Validate family member data
      const validation = OnboardingValidator.validateFamilyMemberData(payload)
      if (!validation.valid) {
        return {
          success: false,
          error: 'Validation failed',
          validationErrors: validation.errors
        }
      }

      const newMember: FamilyMemberData = {
        id: `temp-${Date.now()}`, // Temporary ID, will be replaced by server
        name: payload.name,
        dateOfBirth: payload.date_of_birth,
        gender: payload.gender,
        relationship: payload.relationship,
        medical_conditions: payload.medical_conditions || [],
        allergies: payload.allergies || [],
        medications: payload.medications || [],
        additional_notes: payload.additional_notes || '',
        is_primary: payload.is_primary || false
      }

      setFamilyMembers(prev => [...prev, newMember])
      setConfirmationMessage(`Added ${payload.name} as a family member`)
      setMessageType('success')
      
      // Keep live form visible longer and only hide when explicitly told to
      // This gives users time to see the completed information
      const timer = setTimeout(() => {
        setShowLiveFamilyForm(false)
        setCurrentFamilyMember(null)
        setFamilyFormTimer(null)
        console.log('✅ Family member added successfully - hiding live form')
      }, 8000) // Increased to 8 seconds
      
      setFamilyFormTimer(timer)

      return {
        success: true,
        message: `Successfully added ${payload.name}`,
        data: newMember
      }
    } catch (error) {
      console.error('Error adding family member:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }, [showLiveFamilyForm, familyFormTimer])

  const handleUpdateFamilyMember = useCallback(async (payload: UpdateFamilyMemberPayload, requestId: string): Promise<EventResponse> => {
    try {
      console.log('✏️ Updating family member:', payload)
      
      setFamilyMembers(prev => prev.map(member => 
        member.id === payload.memberId 
          ? { ...member, ...payload.updates }
          : member
      ))
      
      return {
        success: true,
        message: 'Family member updated successfully'
      }
    } catch (error) {
      console.error('Error updating family member:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }, [])

  const handleValidateForm = useCallback(async (payload: ValidateFormPayload, requestId: string): Promise<EventResponse> => {
    try {
      console.log('✅ Validating form:', payload)
      
      let validation: { valid: boolean; errors: string[] }
      
      if (payload.formType === 'user') {
        validation = OnboardingValidator.validateUserData(userForm)
      } else {
        // For family member validation, we'd need the specific member data
        validation = { valid: true, errors: [] }
      }
      
      if (payload.showErrors && !validation.valid) {
        setValidationErrors(validation.errors)
      }
      
      return {
        success: validation.valid,
        validationErrors: validation.errors,
        data: validation
      }
    } catch (error) {
      console.error('Error validating form:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }, [userForm])

  const handleUpdateProgress = useCallback(async (payload: OnboardingProgressPayload, requestId: string): Promise<EventResponse> => {
    try {
      console.log('📊 Updating progress:', payload, { isVoiceEnabled })

      // Always handle progress updates, but only update UI if voice enabled
      if (isVoiceEnabled) {
        setCurrentStep(payload.step)

        if (payload.step === 'complete') {
          setConfirmationMessage('🎉 Welcome to Our Kidz! Your family profile has been created successfully!')
          setMessageType('success')
          
          // Trigger confetti celebration
          celebrateCompletion()
          
          // Call onComplete but don't redirect - let user see the completion
          if (onComplete) {
            // Don't actually redirect, just show completion
            console.log('🎉 Onboarding completed, showing celebration instead of redirecting')
          }
        }
      }

      return {
        success: true,
        message: `Progress updated to ${payload.step}`,
        data: { step: payload.step, voiceEnabled: isVoiceEnabled }
      }
    } catch (error) {
      console.error('Error updating progress:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }, [userForm, familyMembers, onComplete, isVoiceEnabled])

  const handleShowConfirmation = useCallback(async (payload: { message: string; type?: 'success' | 'error' | 'info' }, requestId: string): Promise<EventResponse> => {
    try {
      console.log('📢 Showing confirmation:', payload)
      
      setConfirmationMessage(payload.message)
      setMessageType(payload.type || 'info')
      
      return {
        success: true,
        message: 'Confirmation shown'
      }
    } catch (error) {
      console.error('Error showing confirmation:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }, [])

  const handleClearForm = useCallback(async (payload: { formType: 'user' | 'family_member' }, requestId: string): Promise<EventResponse> => {
    try {
      console.log('🧹 Clearing form:', payload)
      
      if (payload.formType === 'user') {
        setUserForm({
          email: '',
          fullName: '',
          role: 'parent',
          phoneNumber: '',
          zipCode: '',
          dateOfBirth: '',
          emergencyContact: {
            name: '',
            phoneNumber: '',
            relationship: ''
          },
          preferences: {
            notifications: true,
            language: 'en'
          }
        })
      } else {
        setFamilyMembers([])
      }
      
      setValidationErrors([])
      
      return {
        success: true,
        message: 'Form cleared successfully'
      }
    } catch (error) {
      console.error('Error clearing form:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }, [])

  const handleResetOnboarding = useCallback(async (payload: any, requestId: string): Promise<EventResponse> => {
    try {
      console.log('🔄 Resetting onboarding')
      
      setCurrentStep('welcome')
      setUserForm({
        email: '',
        fullName: '',
        role: 'parent',
        phoneNumber: '',
        zipCode: '',
        dateOfBirth: '',
        emergencyContact: {
          name: '',
          phoneNumber: '',
          relationship: ''
        },
        preferences: {
          notifications: true,
          language: 'en'
        }
      })
      setFamilyMembers([])
      setValidationErrors([])
      setConfirmationMessage('')
      
      return {
        success: true,
        message: 'Onboarding reset successfully'
      }
    } catch (error) {
      console.error('Error resetting onboarding:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }, [])

  // Set up event listeners - register them to override global handlers on onboarding page
  useEffect(() => {
    // On onboarding page, we want the onboarding interface to handle events directly
    // This overrides the global handlers
    console.log('🎯 Registering onboarding-specific AG-UI event listeners')

    aguiEventListener.addEventListener(AG_UI_EVENTS.FILL_USER_FORM, handleFillUserForm)
    aguiEventListener.addEventListener(AG_UI_EVENTS.SUBMIT_USER_FORM, handleSubmitUserForm)
    aguiEventListener.addEventListener(AG_UI_EVENTS.ADD_FAMILY_MEMBER, handleAddFamilyMember)
    aguiEventListener.addEventListener(AG_UI_EVENTS.UPDATE_FAMILY_MEMBER, handleUpdateFamilyMember)
    aguiEventListener.addEventListener(AG_UI_EVENTS.VALIDATE_FORM, handleValidateForm)
    aguiEventListener.addEventListener(AG_UI_EVENTS.UPDATE_PROGRESS, handleUpdateProgress)
    aguiEventListener.addEventListener(AG_UI_EVENTS.SHOW_CONFIRMATION, handleShowConfirmation)
    aguiEventListener.addEventListener(AG_UI_EVENTS.CLEAR_FORM, handleClearForm)
    aguiEventListener.addEventListener(AG_UI_EVENTS.RESET_ONBOARDING, handleResetOnboarding)

    console.log('🎯 Onboarding AG-UI event listeners registered', { isVoiceEnabled })

    return () => {
      // Remove only the specific listeners this component added
      // Don't remove all listeners since the global handler also uses them
      aguiEventListener.removeEventListener(AG_UI_EVENTS.FILL_USER_FORM)
      aguiEventListener.removeEventListener(AG_UI_EVENTS.SUBMIT_USER_FORM)
      aguiEventListener.removeEventListener(AG_UI_EVENTS.ADD_FAMILY_MEMBER)
      aguiEventListener.removeEventListener(AG_UI_EVENTS.UPDATE_FAMILY_MEMBER)
      aguiEventListener.removeEventListener(AG_UI_EVENTS.VALIDATE_FORM)
      aguiEventListener.removeEventListener(AG_UI_EVENTS.UPDATE_PROGRESS)
      aguiEventListener.removeEventListener(AG_UI_EVENTS.SHOW_CONFIRMATION)
      aguiEventListener.removeEventListener(AG_UI_EVENTS.CLEAR_FORM)
      aguiEventListener.removeEventListener(AG_UI_EVENTS.RESET_ONBOARDING)
      console.log('🎯 Onboarding-specific AG-UI event listeners removed')
    }
  }, [
    handleFillUserForm,
    handleSubmitUserForm,
    handleAddFamilyMember,
    handleUpdateFamilyMember,
    handleValidateForm,
    handleUpdateProgress,
    handleShowConfirmation,
    handleClearForm,
    handleResetOnboarding
  ])

  

  // Render functions
  const renderProgress = () => (
    <div className="mb-8">
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm font-medium">Onboarding Progress</span>
        <span className="text-sm text-gray-600">{Math.round(progress)}%</span>
      </div>
      <Progress value={progress} className="h-2" />
      <div className="flex justify-between mt-2 text-xs text-gray-500">
        <span>Sign Up</span>
        <span>Welcome</span>
        <span>Personal Info</span>
        <span>Family Info</span>
        <span>Complete</span>
      </div>
    </div>
  )

  const renderVoiceIndicator = () => (
    isVoiceEnabled && (
      <div className="flex items-center gap-2 mb-4 p-3 bg-blue-50 rounded-lg">
        <Mic className="h-4 w-4 text-blue-600" />
        <span className="text-sm text-blue-800">Voice guidance enabled - Mae will help you complete this form</span>
      </div>
    )
  )

  const renderConfirmationMessage = () => (
    confirmationMessage && (
      <Alert className={cn(
        "mb-4",
        messageType === 'success' && "border-green-500 text-green-700",
        messageType === 'error' && "border-red-500 text-red-700",
        messageType === 'info' && "border-blue-500 text-blue-700"
      )}>
        {messageType === 'success' && <CheckCircle2 className="h-4 w-4" />}
        {messageType === 'error' && <AlertCircle className="h-4 w-4" />}
        {messageType === 'info' && <AlertCircle className="h-4 w-4" />}
        <AlertDescription>{confirmationMessage}</AlertDescription>
      </Alert>
    )
  )

  const renderValidationErrors = () => (
    validationErrors.length > 0 && (
      <Alert className="mb-4 border-red-500">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          <ul className="list-disc list-inside">
            {validationErrors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        </AlertDescription>
      </Alert>
    )
  )

  const renderLiveFamilyMemberForm = () => (
    showLiveFamilyForm && currentFamilyMember && (
      <div className="mb-6 p-4 bg-gradient-to-r from-blue-50 to-teal-50 border-2 border-blue-200 rounded-lg">
        <div className="flex items-center gap-2 mb-3">
          <Mic className="h-4 w-4 text-blue-600 animate-pulse" />
          <span className="text-sm font-medium text-blue-800">Mae is collecting family member information...</span>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="font-medium text-gray-600">Name:</span>
              <span className={cn(
                "px-2 py-1 rounded",
                currentFamilyMember.name ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-500"
              )}>
                {currentFamilyMember.name || 'Waiting...'}
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <span className="font-medium text-gray-600">Date of Birth:</span>
              <span className={cn(
                "px-2 py-1 rounded",
                currentFamilyMember.dateOfBirth ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-500"
              )}>
                {currentFamilyMember.dateOfBirth || 'Waiting...'}
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <span className="font-medium text-gray-600">Gender:</span>
              <span className={cn(
                "px-2 py-1 rounded",
                currentFamilyMember.gender ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-500"
              )}>
                {currentFamilyMember.gender || 'Waiting...'}
              </span>
            </div>
            
            <div className="flex items-center gap-2">
              <span className="font-medium text-gray-600">Relationship:</span>
              <span className={cn(
                "px-2 py-1 rounded",
                currentFamilyMember.relationship ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-500"
              )}>
                {currentFamilyMember.relationship || 'Waiting...'}
              </span>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-start gap-2">
              <span className="font-medium text-gray-600">Medical Conditions:</span>
              <div className="flex flex-wrap gap-1">
                {currentFamilyMember.medical_conditions && currentFamilyMember.medical_conditions.length > 0 ? (
                  currentFamilyMember.medical_conditions.map((condition, index) => (
                    <span key={index} className="px-2 py-1 bg-yellow-100 text-yellow-800 rounded text-xs">
                      {condition}
                    </span>
                  ))
                ) : (
                  <span className="px-2 py-1 bg-gray-100 text-gray-500 rounded text-xs">None specified</span>
                )}
              </div>
            </div>
            
            <div className="flex items-start gap-2">
              <span className="font-medium text-gray-600">Allergies:</span>
              <div className="flex flex-wrap gap-1">
                {currentFamilyMember.allergies && currentFamilyMember.allergies.length > 0 ? (
                  currentFamilyMember.allergies.map((allergy, index) => (
                    <span key={index} className="px-2 py-1 bg-red-100 text-red-800 rounded text-xs">
                      {allergy}
                    </span>
                  ))
                ) : (
                  <span className="px-2 py-1 bg-gray-100 text-gray-500 rounded text-xs">None specified</span>
                )}
              </div>
            </div>
            
            {currentFamilyMember.additional_notes && (
              <div className="flex items-start gap-2">
                <span className="font-medium text-gray-600">Notes:</span>
                <span className="px-2 py-1 bg-purple-100 text-purple-800 rounded text-xs">
                  {currentFamilyMember.additional_notes}
                </span>
              </div>
            )}
          </div>
        </div>
        
        <div className="mt-3 text-xs text-blue-700">
          💡 This information is being filled in real-time as Mae listens to you
        </div>
      </div>
    )
  )

  // Step rendering

  const renderWelcomeStep = () => (
    <Card className="healthcare-card border-l-4 border-l-teal-500">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Heart className="h-5 w-5 text-teal-600" />
          Welcome to Our Kidz
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-gray-600 mb-6">
          Let&apos;s get you set up with your personalized parenting assistant. Mae will guide you through 
          the process and help you create your family profile.
        </p>
        <Button 
          onClick={() => setCurrentStep('user_info')}
          className="w-full"
          disabled={isSubmitting}
        >
          Get Started
        </Button>
      </CardContent>
    </Card>
  )

  const renderUserInfoStep = () => (
    <ProfessionalUserForm
      onComplete={async (data) => {
        // Map the form data to our userForm structure
        const updatedForm = {
          ...userForm,
          email: data.email,
          fullName: data.fullName,
          phoneNumber: data.phoneNumber,  
          zipCode: data.zipCode,
          dateOfBirth: data.dateOfBirth,
          role: (data.role?.toLowerCase() as 'parent' | 'guardian' | 'caregiver') || 'parent',
          emergencyContact: {
            name: data.emergencyName,
            phoneNumber: data.emergencyPhone,
            relationship: data.emergencyRelationship
          }
        }
        setUserForm(updatedForm)
        
        // Submit the form data
        await handleSubmitUserForm({}, 'manual')
      }}
      onBack={() => setCurrentStep('welcome')}
      familyMembers={familyMembers.map(member => ({
        id: member.id || '',
        name: member.name,
        dateOfBirth: member.dateOfBirth || '',
        relationship: member.relationship,
        age: member.dateOfBirth ? new Date().getFullYear() - new Date(member.dateOfBirth).getFullYear() : 0
      }))}
    />
  )

  const renderFamilyInfoStep = () => (
    <Card className="healthcare-card border-l-4 border-l-teal-500">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Heart className="h-5 w-5 text-teal-600" />
          Family Members
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {familyMembers.map((member, index) => (
          <div key={member.id || `member-${index}`} className="p-4 border rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <h4 className="font-medium">{member.name}</h4>
              <Badge variant={member.is_primary ? "default" : "secondary"}>
                {member.is_primary ? 'Primary' : member.relationship}
              </Badge>
            </div>
            <div className="grid grid-cols-2 gap-2 text-sm text-gray-600">
              <div>Born: {member.dateOfBirth}</div>
              <div>Gender: {member.gender || 'Not specified'}</div>
              {member.medical_conditions.length > 0 && (
                <div className="col-span-2">
                  Medical: {member.medical_conditions.join(', ')}
                </div>
              )}
              {member.allergies.length > 0 && (
                <div className="col-span-2">
                  Allergies: {member.allergies.join(', ')}
                </div>
              )}
            </div>
          </div>
        ))}
        
        {familyMembers.length === 0 && (
          <div className="text-center py-8 text-gray-500">
            <Heart className="h-12 w-12 mx-auto mb-4 text-gray-300" />
            <p>No family members added yet</p>
            <p className="text-sm">Mae will help you add your children and family members via voice</p>
          </div>
        )}

        <div className="flex gap-4 pt-4">
          <Button variant="outline" onClick={() => setCurrentStep('user_info')}>
            Back
          </Button>
          <Button 
            onClick={() => setCurrentStep('complete')}
            className="flex-1"
            disabled={familyMembers.length === 0}
          >
            Complete Setup
          </Button>
        </div>
      </CardContent>
    </Card>
  )

  const renderCompleteStep = () => (
    <Card className="healthcare-card border-l-4 border-l-green-500">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <CheckCircle2 className="h-5 w-5 text-green-600" />
          Welcome to Our Kidz!
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-center py-8">
          <div className="mb-4">
            <CheckCircle2 className="h-16 w-16 mx-auto text-green-600 mb-4" />
            <h3 className="text-xl font-semibold mb-2">Setup Complete!</h3>
            <p className="text-gray-600">
              Your family profile has been created successfully. Mae is now ready to help you 
              with personalized parenting guidance and support.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
            <div className="p-4 bg-teal-50 rounded-lg">
              <User className="h-5 w-5 text-teal-600 mb-2" />
              <h4 className="font-medium">{userForm.fullName}</h4>
              <p className="text-sm text-gray-600">{userForm.email}</p>
            </div>
            <div className="p-4 bg-blue-50 rounded-lg">
              <Heart className="h-5 w-5 text-blue-600 mb-2" />
              <h4 className="font-medium">{familyMembers.length} Family Member{familyMembers.length !== 1 ? 's' : ''}</h4>
              <p className="text-sm text-gray-600">
                {familyMembers.map(m => m.name).join(', ')}
              </p>
            </div>
          </div>
          
          <div className="flex gap-4 mt-6">
            <Button 
              onClick={() => window.location.href = '/'}
              variant="outline"
              className="flex-1"
            >
              Back to Home
            </Button>
            <Button 
              onClick={() => {
                celebrateCompletion()
                setConfirmationMessage('🎉 Thanks for setting up your Our Kidz account!')
                setMessageType('success')
              }}
              className="flex-1"
            >
              Celebrate Again! 🎉
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )


  if (!isLoaded) {
    return (
      <div className="max-w-2xl mx-auto p-6">
        <Card>
          <CardContent className="p-8 text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-teal-600 mx-auto mb-4"></div>
            <p>Loading authentication...</p>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="max-w-2xl mx-auto p-6">
      {renderProgress()}
      {renderVoiceIndicator()}
      {renderConfirmationMessage()}
      {renderValidationErrors()}
      {renderLiveFamilyMemberForm()}

      {currentStep === 'welcome' && renderWelcomeStep()}
      {currentStep === 'user_info' && renderUserInfoStep()}
      {currentStep === 'family_info' && renderFamilyInfoStep()}
      {currentStep === 'complete' && renderCompleteStep()}
    </div>
  )
}