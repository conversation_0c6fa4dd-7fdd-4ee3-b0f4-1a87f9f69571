"use client"

import * as React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Download, Printer } from "lucide-react"

type LegalActionsProps = {
  downloadPath: string
  fileName?: string
}

export function LegalActions({ downloadPath, fileName }: LegalActionsProps) {
  const handlePrint = React.useCallback(() => {
    if (typeof window !== "undefined") {
      window.print()
    }
  }, [])

  return (
    <div className="flex items-center gap-2">
      <a href={downloadPath} download={fileName}>
        <Button variant="outline" className="flex items-center gap-2" aria-label="Download document">
          <Download className="h-4 w-4" />
          <span className="hidden sm:inline">Download</span>
        </Button>
      </a>
      <Button onClick={handlePrint} variant="ghost" className="flex items-center gap-2" aria-label="Print or save as PDF">
        <Printer className="h-4 w-4" />
        <span className="hidden sm:inline">Print / Save PDF</span>
      </Button>
    </div>
  )
}


