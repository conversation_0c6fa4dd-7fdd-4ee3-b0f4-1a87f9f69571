"use client"

import React, { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Button } from '@/components/ui/button'
import { 
  Heart, 
  User, 
  Users, 
  CheckCircle2, 
  Mic,
  MessageSquare,
  Sparkles,
  ArrowRight,
  Clock,
  Zap,
  Brain,
  Activity,
  ChevronRight,
  Volume2,
  Wand2
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useGlobalMaeSession } from '@/components/global-mae-provider'
import { useMaeActivityTracker } from '@/hooks/use-mae-activity-tracker'

interface OnboardingStep {
  id: string
  label: string
  icon: React.ComponentType<any>
  description: string
  maeCapabilities: string[]
  voicePrompts: string[]
}

const onboardingSteps: OnboardingStep[] = [
  { 
    id: 'welcome', 
    label: 'Welcome', 
    icon: Heart, 
    description: 'Get started with Our Kidz',
    maeCapabilities: ['Voice guidance', 'Setup assistance', 'Platform introduction'],
    voicePrompts: ['Start my onboarding', 'Tell me about Our Kidz', 'What do I need to do?']
  },
  { 
    id: 'user_info', 
    label: 'Your Information', 
    icon: User, 
    description: 'Tell us about yourself',
    maeCapabilities: ['Voice form filling', 'Data validation', 'Smart suggestions'],
    voicePrompts: ['My name is...', 'My email is...', 'I live in zip code...']
  },
  { 
    id: 'family_info', 
    label: 'Family Members', 
    icon: Users, 
    description: 'Add your family',
    maeCapabilities: ['Family member creation', 'Age calculation', 'Profile management'],
    voicePrompts: ['I have a daughter named...', 'Add my 5-year-old son', 'My children are...']
  },
  { 
    id: 'complete', 
    label: 'Complete', 
    icon: CheckCircle2, 
    description: 'All set!',
    maeCapabilities: ['Profile summary', 'Next steps guidance', 'Platform navigation'],
    voicePrompts: ['Show me my profile', 'What can I do now?', 'Take me to the dashboard']
  },
]

interface MaeOnboardingProgressProps {
  className?: string
  currentStepIndex: number
  completedSteps: string[]
  onStepClick?: (index: number) => void
  showMaeGuidance?: boolean
}

export function MaeOnboardingProgress({
  className,
  currentStepIndex,
  completedSteps,
  onStepClick,
  showMaeGuidance = true
}: MaeOnboardingProgressProps) {
  const { sessionState, isSessionActive } = useGlobalMaeSession()
  const maeActivity = useMaeActivityTracker()
  const [maeIsProcessing, setMaeIsProcessing] = useState(false)

  const currentStep = onboardingSteps[currentStepIndex]
  const progressPercentage = ((currentStepIndex + 1) / onboardingSteps.length) * 100

  // Track Mae processing state
  useEffect(() => {
    const hasActiveActivity = maeActivity.activities.some(a => a.status === 'active')
    setMaeIsProcessing(hasActiveActivity)
  }, [maeActivity.activities])

  return (
    <motion.div
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      className={cn("space-y-4", className)}
    >
      {/* Compact Progress Header */}
      <Card className="border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center gap-2">
              <Activity className="h-4 w-4 text-primary" />
              Progress
            </CardTitle>
            <Badge variant="outline" className="border-primary/50 text-primary text-xs">
              {currentStepIndex + 1}/{onboardingSteps.length}
            </Badge>
          </div>
          <Progress value={progressPercentage} className="h-1.5 mt-2" />
        </CardHeader>
      </Card>

      {/* Compact Step List */}
      <div className="space-y-2">
        {onboardingSteps.map((step, index) => {
          const Icon = step.icon
          const isActive = index === currentStepIndex
          const isCompleted = completedSteps.includes(step.id)
          const isAccessible = index <= completedSteps.length || isActive

          return (
            <motion.button
              key={step.id}
              initial={{ opacity: 0, y: 5 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05 }}
              onClick={() => {
                if (isAccessible && onStepClick) {
                  onStepClick(index)
                }
              }}
              disabled={!isAccessible}
              className={cn(
                "w-full text-left p-3 rounded-lg transition-all duration-200 flex items-center gap-3",
                isActive && "bg-primary/10 border border-primary/30 shadow-md",
                isCompleted && !isActive && "bg-green-500/5 border border-green-500/20 hover:bg-green-500/10",
                !isActive && !isCompleted && !isAccessible && "opacity-50 cursor-not-allowed",
                isAccessible && !isActive && "hover:bg-muted/50"
              )}
            >
              <motion.div
                className={cn(
                  "w-8 h-8 rounded-lg flex items-center justify-center transition-all duration-300 flex-shrink-0",
                  isActive && "bg-gradient-to-br from-primary to-primary/70 shadow-md",
                  isCompleted && !isActive && "bg-gradient-to-br from-green-500 to-green-600",
                  !isActive && !isCompleted && "bg-muted"
                )}
                whileHover={isAccessible ? { scale: 1.05 } : {}}
                whileTap={isAccessible ? { scale: 0.95 } : {}}
              >
                {isCompleted ? (
                  <CheckCircle2 className="h-4 w-4 text-white" />
                ) : (
                  <Icon className={cn(
                    "h-4 w-4 transition-colors",
                    isActive ? "text-primary-foreground" : "text-muted-foreground"
                  )} />
                )}
              </motion.div>

              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <h3 className={cn(
                    "font-medium text-sm transition-colors truncate",
                    isActive ? "text-foreground" : "text-muted-foreground"
                  )}>
                    {step.label}
                  </h3>
                  {isActive && maeIsProcessing && (
                    <Badge variant="outline" className="border-primary/50 text-primary animate-pulse text-xs">
                      <Brain className="h-2 w-2 mr-1" />
                      Mae
                    </Badge>
                  )}
                </div>
                <p className="text-xs text-muted-foreground truncate">
                  {step.description}
                </p>
              </div>
            </motion.button>
          )
        })}
      </div>
    </motion.div>
  )
}
