"use client"

import { motion } from "framer-motion"

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg"
  className?: string
}

export function LoadingSpinner({ size = "md", className = "" }: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "w-4 h-4",
    md: "w-8 h-8",
    lg: "w-12 h-12"
  }

  return (
    <motion.div
      className={`${sizeClasses[size]} ${className}`}
      animate={{ rotate: 360 }}
      transition={{
        duration: 1,
        repeat: Infinity,
        ease: "linear"
      }}
    >
      <div className="w-full h-full border-2 border-teal-200 border-t-teal-500 rounded-full" />
    </motion.div>
  )
}

export function PulsingDot({ className = "" }: { className?: string }) {
  return (
    <motion.div
      className={`w-2 h-2 bg-teal-500 rounded-full ${className}`}
      animate={{
        scale: [1, 1.2, 1],
        opacity: [0.5, 1, 0.5]
      }}
      transition={{
        duration: 1.5,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    />
  )
}

export function ThreeDotLoader({ className = "" }: { className?: string }) {
  const containerVariants = {
    start: {
      transition: {
        staggerChildren: 0.2,
      },
    },
    end: {
      transition: {
        staggerChildren: 0.2,
      },
    },
  }

  const circleVariants = {
    start: {
      y: "0%",
    },
    end: {
      y: "100%",
    },
  }

  const circleTransition = {
    duration: 0.5,
    repeat: Infinity,
    repeatType: "reverse" as const,
    ease: "easeInOut" as const,
  }

  return (
    <motion.div
      className={`flex space-x-1 ${className}`}
      variants={containerVariants}
      initial="start"
      animate="end"
    >
      {[...Array(3)].map((_, index) => (
        <motion.div
          key={index}
          className="w-2 h-2 bg-teal-500 rounded-full"
          variants={circleVariants}
          transition={circleTransition}
        />
      ))}
    </motion.div>
  )
}