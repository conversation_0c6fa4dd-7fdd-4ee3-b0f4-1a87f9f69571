"use client"

import React, { useState } from 'react'
import { handleEmailConversation } from '../lib/email-function-tool'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Textarea } from './ui/textarea'

export function EmailDebugComponent() {
  const [email, setEmail] = useState('')
  const [question, setQuestion] = useState('My 2-year-old has been fussy during bedtime. What can I do?')
  const [response, setResponse] = useState('Hello! I understand bedtime can be challenging. Here are some strategies: 1) Establish a consistent routine, 2) Create a calm environment, 3) Ensure they\'re not overtired. Would you like me to send you more detailed guidance via email?')
  const [isLoading, setIsLoading] = useState(false)
  const [result, setResult] = useState<any>(null)
  const [envStatus, setEnvStatus] = useState<any>(null)

  const testEmail = async () => {
    if (!email || !question || !response) {
      alert('Please fill in all fields')
      return
    }

    setIsLoading(true)
    setResult(null)

    try {
      const emailResult = await handleEmailConversation({
        recipient_email: email,
        user_question: question,
        ai_response: response,
        subject: 'Test Email from Our Kidz Debug',
        include_context: true,
        conversation_context: 'This is a test email sent from the debug component.'
      })

      setResult(emailResult)
    } catch (error) {
      setResult({
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const checkEnvironment = async () => {
    try {
      const response = await fetch('/api/check-smtp')
      const status = await response.json()
      setEnvStatus(status)
    } catch (error) {
      setEnvStatus({ error: 'Failed to check environment' })
    }
  }

  React.useEffect(() => {
    checkEnvironment()
  }, [])

  return (
    <div className="max-w-2xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <h2 className="text-2xl font-bold mb-6 text-teal-600">Email Function Debug Tool</h2>
      
      {envStatus && (
        <div className="mb-6 p-4 bg-gray-100 rounded-lg">
          <h3 className="font-bold mb-2">Environment Status:</h3>
          <pre className="text-xs overflow-auto">
            {JSON.stringify(envStatus, null, 2)}
          </pre>
        </div>
      )}

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-2">Email Address:</label>
          <Input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="<EMAIL>"
            className="w-full"
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">User Question:</label>
          <Textarea
            value={question}
            onChange={(e) => setQuestion(e.target.value)}
            placeholder="Enter user question..."
            className="w-full"
            rows={3}
          />
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">AI Response:</label>
          <Textarea
            value={response}
            onChange={(e) => setResponse(e.target.value)}
            placeholder="Enter AI response..."
            className="w-full"
            rows={5}
          />
        </div>

        <Button 
          onClick={testEmail} 
          disabled={isLoading}
          className="w-full bg-teal-600 hover:bg-teal-700"
        >
          {isLoading ? 'Sending Test Email...' : 'Send Test Email'}
        </Button>

        {result && (
          <div className={`mt-4 p-4 rounded-lg ${result.success ? 'bg-green-100 border border-green-400' : 'bg-red-100 border border-red-400'}`}>
            <h3 className="font-bold mb-2">Result:</h3>
            <pre className="text-sm overflow-auto">
              {JSON.stringify(result, null, 2)}
            </pre>
          </div>
        )}
      </div>

      <div className="mt-6 text-sm text-gray-600">
        <p><strong>Note:</strong> This is a debug tool to test the email functionality. Make sure your environment variables are configured correctly:</p>
        <ul className="list-disc list-inside mt-2">
          <li>SMTP_HOST, SMTP_PORT, SMTP_USER, SMTP_PASS</li>
          <li>FROM_EMAIL, FROM_NAME</li>
        </ul>
      </div>
    </div>
  )
}