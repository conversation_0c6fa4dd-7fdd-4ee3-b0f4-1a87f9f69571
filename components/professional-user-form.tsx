"use client"

import React, { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'
import { 
  aguiEventListener, 
  AG_UI_EVENTS, 
  type FillUserFormPayload,
  type EventResponse
} from '@/lib/ag-ui-events'
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  Shield,
  Heart,
  CheckCircle2,
  Users,
  Star,
  Building,
  Baby
} from 'lucide-react'

interface FormData {
  email: string
  fullName: string
  phoneNumber: string
  zipCode: string
  dateOfBirth: string
  role: string
  emergencyName: string
  emergencyPhone: string
  emergencyRelationship: string
}

interface FamilyMember {
  id: string
  name: string
  age: number
  relationship: string
}

interface ProfessionalUserFormProps {
  onComplete?: (data: FormData) => void
  onBack?: () => void
  familyMembers?: FamilyMember[]
}

// Sparkle component for corner animations
const CornerSparkle = ({ corner }: { corner: 'top-left' | 'top-right' | 'bottom-left' | 'bottom-right' }) => {
  const getPosition = () => {
    switch (corner) {
      case 'top-left': return { top: '10px', left: '10px' }
      case 'top-right': return { top: '10px', right: '10px' }
      case 'bottom-left': return { bottom: '10px', left: '10px' }
      case 'bottom-right': return { bottom: '10px', right: '10px' }
    }
  }

  return (
    <motion.div
      className="absolute pointer-events-none"
      style={getPosition()}
      animate={{
        rotate: [0, 360],
        scale: [0.8, 1, 0.8],
        opacity: [0.3, 0.7, 0.3]
      }}
      transition={{
        duration: 4,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    >
      <Star className="h-4 w-4 text-primary fill-primary/30" />
    </motion.div>
  )
}

// Floating orb component
const FloatingOrb = ({ 
  x, 
  y, 
  size, 
  delay = 0 
}: { 
  x: number
  y: number
  size: number
  delay?: number
}) => {
  return (
    <motion.div
      className="absolute rounded-full bg-primary/10 blur-xl opacity-30"
      style={{
        width: size,
        height: size,
        left: `${x}%`,
        top: `${y}%`,
      }}
      animate={{
        x: [0, 20, -20, 0],
        y: [0, -20, 20, 0],
        scale: [1, 1.1, 0.9, 1],
      }}
      transition={{
        duration: 15,
        delay,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    />
  )
}

// Real-time profile display
const ProfileDisplay = ({ formData, familyMembers }: { formData: Partial<FormData>, familyMembers: FamilyMember[] }) => {
  return (
    <Card className="h-full border-primary/20 bg-card/80 backdrop-blur-sm">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg">
          <User className="h-5 w-5 text-primary" />
          Family Profile
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-1 p-4 mx-full h-auto">
        {/* User Information */}
        <div className="space-y-3 mx-full">
          <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">Primary User</h4>
          <div className="grid gap-2">
            {formData.fullName && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                className="flex items-center gap-2"
              >
                <User className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{formData.fullName}</span>
                {formData.role && (
                  <Badge variant="secondary" className="text-xs">
                    {formData.role}
                  </Badge>
                )}
              </motion.div>
            )}
            {formData.email && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.1 }}
                className="flex items-center gap-2"
              >
                <Mail className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{formData.email}</span>
              </motion.div>
            )}
            {formData.phoneNumber && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.2 }}
                className="flex items-center gap-2"
              >
                <Phone className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{formData.phoneNumber}</span>
              </motion.div>
            )}
            {formData.zipCode && (
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
                className="flex items-center gap-2"
              >
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm">{formData.zipCode}</span>
              </motion.div>
            )}
          </div>
        </div>

        {/* Emergency Contact */}
        {(formData.emergencyName || formData.emergencyPhone) && (
          <>
            <Separator />
            <div className="space-y-3">
              <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">Emergency Contact</h4>
              <div className="grid gap-2">
                {formData.emergencyName && (
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    className="flex items-center gap-2"
                  >
                    <Heart className="h-4 w-4 text-red-500" />
                    <span className="text-sm">{formData.emergencyName}</span>
                    {formData.emergencyRelationship && (
                      <Badge variant="outline" className="text-xs">
                        {formData.emergencyRelationship}
                      </Badge>
                    )}
                  </motion.div>
                )}
                {formData.emergencyPhone && (
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 }}
                    className="flex items-center gap-2"
                  >
                    <Phone className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">{formData.emergencyPhone}</span>
                  </motion.div>
                )}
              </div>
            </div>
          </>
        )}

        {/* Family Members */}
        {familyMembers.length > 0 && (
          <>
            <Separator />
            <div className="space-y-3">
              <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">Family Members</h4>
              <div className="space-y-2">
                {familyMembers.map((member, index) => (
                  <motion.div
                    key={member.id}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    className="flex items-center gap-2 p-2 rounded-lg bg-muted/30"
                  >
                    <Baby className="h-4 w-4 text-primary" />
                    <span className="text-sm font-medium">{member.name}</span>
                    <Badge variant="secondary" className="text-xs">
                      Age {member.age}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      {member.relationship}
                    </Badge>
                  </motion.div>
                ))}
              </div>
            </div>
          </>
        )}

        {/* Mermaid Diagram Placeholder */}
        {(formData.fullName || familyMembers.length > 0) && (
          <>
            <Separator />
            <div className="space-y-3">
              <h4 className="font-semibold text-sm text-muted-foreground uppercase tracking-wide">Family Structure</h4>
              <div className="bg-muted/20 rounded-lg p-4 min-h-[120px] flex items-center justify-center">
                <div className="text-center space-y-2">
                  <Building className="h-8 w-8 text-muted-foreground mx-auto" />
                  <p className="text-xs text-muted-foreground">
                    Family diagram will appear here as members are added
                  </p>
                </div>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}

export default function ProfessionalUserForm({ onComplete, onBack, familyMembers = [] }: ProfessionalUserFormProps) {
  const [formData, setFormData] = useState<Partial<FormData>>({})
  const [currentStep, setCurrentStep] = useState(0)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // AG-UI Event Handler for Mae voice interactions
  const handleFillUserForm = useCallback(async (payload: FillUserFormPayload, requestId: string): Promise<EventResponse> => {
    try {
      console.log('🎤 Mae filling user form field:', payload)
      
      if (!payload || typeof payload !== 'object') {
        return { success: false, error: 'Invalid payload: payload is null or not an object' }
      }

      if (!payload.field) {
        return { success: false, error: 'Invalid payload: field is required' }
      }

      if (payload.value === undefined || payload.value === null) {
        return { success: false, error: 'Invalid payload: value is required' }
      }

      // Update form data
      setFormData(prev => ({
        ...prev,
        [payload.field]: payload.value
      }))

      // Auto-advance to next field if current field is completed
      const fieldIndex = allFields.findIndex(field => field.key === payload.field)
      if (fieldIndex !== -1 && fieldIndex === currentStep) {
        setTimeout(() => {
          if (currentStep < allFields.length - 1) {
            setCurrentStep(currentStep + 1)
          }
        }, 500)
      }

      return {
        success: true,
        message: `Updated ${payload.field} successfully`,
        data: { field: payload.field, value: payload.value }
      }
    } catch (error) {
      console.error('Error filling user form:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  }, [formData, currentStep])

  // Set up AG-UI event listeners
  useEffect(() => {
    aguiEventListener.addEventListener(
      AG_UI_EVENTS.FILL_USER_FORM,
      handleFillUserForm
    )

    return () => {
      aguiEventListener.removeEventListener(AG_UI_EVENTS.FILL_USER_FORM)
    }
  }, [handleFillUserForm])

  const formFields = [
    { key: 'email', label: 'Email Address', type: 'email', icon: Mail, required: true },
    { key: 'fullName', label: 'Full Name', type: 'text', icon: User, required: true },
    { key: 'phoneNumber', label: 'Phone Number', type: 'tel', icon: Phone },
    { key: 'zipCode', label: 'ZIP Code', type: 'text', icon: MapPin },
    { key: 'date_of_birth', label: 'Date of Birth', type: 'date', icon: Calendar },
    { key: 'role', label: 'Role', type: 'select', icon: Shield, options: ['Parent', 'Guardian', 'Caregiver', 'Other'] },
    { key: 'emergencyName', label: 'Emergency Contact Name', type: 'text', icon: Heart },
    { key: 'emergencyPhone', label: 'Emergency Phone', type: 'tel', icon: Phone },
    { key: 'emergencyRelationship', label: 'Relationship', type: 'text', icon: Users },
  ]

  const allFields = formFields
  const isLastStep = currentStep >= allFields.length - 1

  const handleFieldChange = (key: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const handleNext = () => {
    if (currentStep < allFields.length - 1) {
      setCurrentStep(currentStep + 1)
    } else {
      handleSubmit()
    }
  }

  const handleSubmit = async () => {
    setIsSubmitting(true)
    try {
      if (onComplete) {
        await onComplete(formData as FormData)
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  const currentField = allFields[currentStep]
  const progress = ((currentStep + 1) / allFields.length) * 100

  return (
    <div className="h-[80vh] max-h-[600px] w-full grid grid-cols-1 lg:grid-cols-2 gap-8 relative overflow-hidden">
      {/* Background effects */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* Corner sparkles */}
        <CornerSparkle corner="top-left" />
        <CornerSparkle corner="top-right" />
        <CornerSparkle corner="bottom-left" />
        <CornerSparkle corner="bottom-right" />
        
        {/* Floating orbs */}
        <FloatingOrb x={10} y={20} size={100} delay={0} />
        <FloatingOrb x={80} y={70} size={80} delay={2} />
        <FloatingOrb x={60} y={40} size={60} delay={4} />
      </div>

      {/* Left Side - Form Section */}
      <div className="relative z-10 flex flex-col">
        <Card className="flex-1 border-border/50 bg-card/80 backdrop-blur-sm">
          <CardHeader className="px-8 py-4">
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2 text-sm font-medium text-muted-foreground">
                <currentField.icon className="h-4 w-4 text-primary" />
                User Information
              </CardTitle>
              <Badge variant="outline" className="text-xs px-2 py-1">
                {currentStep + 1} of {allFields.length}
              </Badge>
            </div>
            <div className="w-full bg-muted rounded-full h-2 mt-4">
              <motion.div
                className="bg-primary h-2 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.3 }}
              />
            </div>
          </CardHeader>
          <CardContent className="flex-1 flex flex-col px-8 py-8">
            <AnimatePresence mode="wait">
              <motion.div
                key={currentStep}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="flex-1 flex flex-col justify-center"
              >
                <div className="space-y-4 w-full">
                  <div className="mb-6">
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-primary/20 to-primary/10 rounded-xl flex items-center justify-center">
                        <currentField.icon className="h-6 w-6 text-primary" />
                      </div>
                      <div>
                        <Label className="text-xl font-semibold text-foreground block">{currentField.label}</Label>
                        {currentField.required && (
                          <p className="text-sm text-muted-foreground mt-1">Required field</p>
                        )}
                      </div>
                    </div>
                  </div>

                  {currentField.type === 'select' ? (
                    <Select
                      value={formData[currentField.key as keyof FormData] || ''}
                      onValueChange={(value) => handleFieldChange(currentField.key, value)}
                    >
                      <SelectTrigger className="h-12 text-base border-primary/30 focus:border-primary w-full px-4">
                        <SelectValue placeholder={`Select your ${currentField.label.toLowerCase()}`} />
                      </SelectTrigger>
                      <SelectContent>
                        {currentField.options?.map(option => (
                          <SelectItem key={option} value={option.toLowerCase()} className="text-base">
                            {option}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  ) : (
                    <Input
                      type={currentField.type}
                      value={formData[currentField.key as keyof FormData] || ''}
                      onChange={(e) => handleFieldChange(currentField.key, e.target.value)}
                      onKeyPress={(e) => e.key === 'Enter' && handleNext()}
                      placeholder={`Enter your ${currentField.label.toLowerCase()}`}
                      className="h-12 text-base border-primary/30 focus:border-primary w-full px-4"
                      autoFocus
                    />
                  )}
                </div>
              </motion.div>
            </AnimatePresence>

            <div className="flex gap-4 mt-8 w-full">
              <Button
                variant="outline"
                onClick={currentStep > 0 ? () => setCurrentStep(currentStep - 1) : onBack}
                className="flex-1 h-10 text-sm"
              >
                {currentStep > 0 ? 'Previous' : 'Back'}
              </Button>
              <Button
                onClick={handleNext}
                disabled={isSubmitting || (currentField.required && !formData[currentField.key as keyof FormData])}
                className="flex-1 h-10 bg-primary hover:bg-primary/90 text-sm"
              >
                {isSubmitting ? 'Saving...' : isLastStep ? 'Complete' : 'Next'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Right Side - Profile Display */}
      <div className="relative z-10">
        <ProfileDisplay formData={formData} familyMembers={familyMembers} />
      </div>
    </div>
  )
}