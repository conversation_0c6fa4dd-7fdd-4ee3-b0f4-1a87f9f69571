"use client"

import React, { useEffect, useRef } from 'react'
import { useMaeActivityTracker } from '@/hooks/use-mae-activity-tracker'

/**
 * Mae Session Bridge Component
 * 
 * This component bridges the Mae session from the landing page to the onboarding page,
 * ensuring that session status, voice activity, and tool tracking remain connected
 * across page navigation.
 */
interface MaeSessionBridgeProps {
  onSessionStatusChange?: (status: any) => void
}

export function MaeSessionBridge({ onSessionStatusChange }: MaeSessionBridgeProps) {
  const maeActivity = useMaeActivityTracker()
  const connectionCheckInterval = useRef<NodeJS.Timeout>()
  const lastActivityTime = useRef<number>(Date.now())

  useEffect(() => {
    console.log('🌉 Mae Session Bridge initialized')

    // Check if Mae session continued from landing page
    const sessionContinued = sessionStorage.getItem('mae-continue-session')
    const conversationContext = sessionStorage.getItem('mae-conversation-context')
    
    if (sessionContinued === 'true') {
      console.log('🔗 Connecting to preserved Mae session...')
      maeActivity.setConnectionStatus('connected')
      
      if (conversationContext) {
        const context = JSON.parse(conversationContext)
        maeActivity.addActivity({
          type: 'processing',
          name: 'Session Bridged',
          description: `Connected to Mae session: ${context.message || 'Continuing conversation'}`,
          status: 'completed'
        })
      }
      
      // Clear the session storage to prevent re-initialization
      sessionStorage.removeItem('mae-continue-session')
      sessionStorage.removeItem('mae-conversation-context')
    }

    // Listen for Mae session events from the parent window/frame
    const handleMaeSessionUpdate = (event: CustomEvent) => {
      const { type, data } = event.detail
      
      switch (type) {
        case 'session_status':
          maeActivity.setConnectionStatus(data.status)
          break
          
        case 'voice_activity':
          maeActivity.setVoiceState(data.isListening, data.isSpeaking)
          if (data.isListening || data.isSpeaking) {
            lastActivityTime.current = Date.now()
          }
          break
          
        case 'tool_activity':
          maeActivity.setCurrentTool(data.toolName)
          maeActivity.addActivity({
            type: 'tool',
            name: data.toolName,
            description: data.description || `Using ${data.toolName}`,
            status: data.status || 'active'
          })
          break
          
        case 'function_call':
          maeActivity.addActivity({
            type: 'function',
            name: data.functionName,
            description: data.description || `Calling ${data.functionName}`,
            status: data.status || 'active'
          })
          break
      }
    }

    // Listen for Mae restart events (when session continues to onboarding)
    const handleMaeRestart = (event: CustomEvent) => {
      console.log('🔄 Mae restart event received:', event.detail)
      maeActivity.setConnectionStatus('connected')
      maeActivity.addActivity({
        type: 'processing',
        name: 'Session Restored',
        description: event.detail.message || 'Mae session continued on onboarding page',
        status: 'completed'
      })
    }

    // Listen for Mae audio context changes from the global session
    const handleAudioContextChange = (event: CustomEvent) => {
      const { isRecording, status, error } = event.detail
      
      if (error) {
        maeActivity.setConnectionStatus('disconnected')
        maeActivity.addActivity({
          type: 'processing',
          name: 'Session Error',
          description: error,
          status: 'error'
        })
      } else if (isRecording !== undefined) {
        maeActivity.setVoiceState(isRecording, false) // Recording means listening
        lastActivityTime.current = Date.now()
      }
    }

    // Periodically check for Mae session activity from global context
    const checkMaeGlobalSession = () => {
      // Check if there's a global Mae session active
      const globalMaeSession = (window as any).__mae_global_session
      
      if (globalMaeSession) {
        // Extract session state from global Mae
        const { isRecording, isConnected, status, error } = globalMaeSession
        
        if (isConnected) {
          maeActivity.setConnectionStatus('connected')
          maeActivity.setVoiceState(isRecording || false, false)
          
          // Update last activity time
          if (isRecording) {
            lastActivityTime.current = Date.now()
          }
        } else if (error) {
          maeActivity.setConnectionStatus('disconnected')
        }
      } else {
        // Check if session should be considered stale
        const timeSinceActivity = Date.now() - lastActivityTime.current
        if (timeSinceActivity > 60000) { // 1 minute of inactivity
          console.log('🕒 Mae session appears stale, marking as disconnected')
          maeActivity.setConnectionStatus('disconnected')
        }
      }
    }

    // Set up periodic connection check
    connectionCheckInterval.current = setInterval(checkMaeGlobalSession, 5000) // Check every 5 seconds

    // Register event listeners
    if (typeof window !== 'undefined') {
      window.addEventListener('mae-session-update', handleMaeSessionUpdate as EventListener)
      window.addEventListener('mae-restart-with-context', handleMaeRestart as EventListener)
      window.addEventListener('mae-audio-context-change', handleAudioContextChange as EventListener)
      
      // Try to connect to global Mae session immediately
      checkMaeGlobalSession()
    }

    return () => {
      if (connectionCheckInterval.current) {
        clearInterval(connectionCheckInterval.current)
      }
      
      if (typeof window !== 'undefined') {
        window.removeEventListener('mae-session-update', handleMaeSessionUpdate as EventListener)
        window.removeEventListener('mae-restart-with-context', handleMaeRestart as EventListener)
        window.removeEventListener('mae-audio-context-change', handleAudioContextChange as EventListener)
      }
    }
  }, []) // Empty dependency array to run only once

  // Expose session control methods
  useEffect(() => {
    const handleSessionControl = (event: CustomEvent) => {
      const { action } = event.detail
      
      switch (action) {
        case 'pause':
          console.log('⏸️ Pausing Mae session...')
          maeActivity.setProcessing(false)
          maeActivity.setVoiceState(false, false)
          
          // Try to pause global Mae session
          const globalMaeSession = (window as any).__mae_global_session
          if (globalMaeSession?.stopRecording) {
            globalMaeSession.stopRecording()
          }
          
          maeActivity.addActivity({
            type: 'processing',
            name: 'Session Paused',
            description: 'Mae session paused by user',
            status: 'completed'
          })
          break
          
        case 'end':
          console.log('🛑 Ending Mae session...')
          maeActivity.setConnectionStatus('disconnected')
          maeActivity.setVoiceState(false, false)
          
          // Try to end global Mae session
          const globalSession = (window as any).__mae_global_session
          if (globalSession?.stopRecording) {
            globalSession.stopRecording()
          }
          
          // Clear session storage
          sessionStorage.removeItem('mae-continue-session')
          sessionStorage.removeItem('mae-conversation-context')
          
          maeActivity.addActivity({
            type: 'processing',
            name: 'Session Ended',
            description: 'Mae session ended by user',
            status: 'completed'
          })
          break
      }
    }

    if (typeof window !== 'undefined') {
      window.addEventListener('mae-session-control', handleSessionControl as EventListener)
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('mae-session-control', handleSessionControl as EventListener)
      }
    }
  }, []) // Empty dependency array to run only once

  // This component doesn't render anything - it's a bridge
  return null
}