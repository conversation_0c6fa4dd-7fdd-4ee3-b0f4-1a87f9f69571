"use client"

import * as React from "react"
import { useState, useEffect } from "react"
import { Mic, Upload, X, Power } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import Image from "next/image"
import { useGeminiLive } from "@/hooks/use-gemini-live"
import { useUser } from "@clerk/nextjs"
import { sendFABState } from "@/lib/cross-tab-communication"
import { useGlobalMaeSession } from "@/components/global-mae-provider"

interface GlobalFABProps {
  className?: string
}

export function GlobalFAB({ className = "" }: GlobalFABProps) {
  const { user } = useUser()
  const { sessionState, updateSessionState, continuationMessage } = useGlobalMaeSession()
  const [isVisible, setIsVisible] = useState(false)
  const [showFloatingOptions, setShowFloatingOptions] = useState(false)
  const [uploadingFile, setUploadingFile] = useState(false)
  const [uploadedFiles, setUploadedFiles] = useState<Array<{uri: string, name: string, mimeType: string}>>([])
  
  const {
    isRecording,
    status,
    error,
    isConnected,
    startRecording,
    stopRecording,
    sendFile,
    initClient,
    reinitWithFiles
  } = useGeminiLive({
    uploadedFiles,
    userId: user?.id,
    userEmail: user?.emailAddresses?.[0]?.emailAddress
  })

  // Check for floating state on mount
  useEffect(() => {
    const checkFloatingState = () => {
      const isFloating = sessionStorage.getItem('mae_fab_floating') === 'true'
      const fabVisible = sessionStorage.getItem('mae_fab_visible') === 'true'
      const activatedFromLanding = sessionStorage.getItem('mae_fab_activated_from_landing') === 'true'
      const currentPath = window.location.pathname

      console.log('🎈 GlobalFAB: Checking floating state:', {
        isFloating,
        fabVisible,
        activatedFromLanding,
        currentPath
      })

      // Don't show FAB on auth pages
      if (currentPath.startsWith('/auth')) {
        console.log('🔒 Auth page detected - hiding FAB')
        setIsVisible(false)
        return
      }

      // Show FAB logic:
      // 1. If we're on landing page (/): Only show if it was activated from landing page
      // 2. If we're on other pages (excluding auth): Show if FAB was previously activated
      const shouldShow = (isFloating || fabVisible) && (
        currentPath === '/' ? activatedFromLanding : true
      )

      if (shouldShow) {
        setIsVisible(true)
        // Initialize client if FAB should be visible
        initClient()
      }
    }

    checkFloatingState()

    // Listen for floating state changes from other components
    const handleFABStateChange = (event: CustomEvent) => {
      console.log('🎈 GlobalFAB: State change received:', event.detail)
      const { visible, floating } = event.detail
      const currentPath = window.location.pathname

      // Don't show FAB on auth pages, even when explicitly requested
      if (currentPath.startsWith('/auth')) {
        console.log('🔒 Auth page detected - preventing FAB activation')
        return
      }

      if (visible) {
        // Show FAB when explicitly requested (e.g., main button clicked)
        setIsVisible(true)
        sessionStorage.setItem('mae_fab_visible', 'true')
        sessionStorage.setItem('mae_fab_floating', 'true')
        // Mark that FAB was activated from landing page
        if (currentPath === '/') {
          sessionStorage.setItem('mae_fab_activated_from_landing', 'true')
        }
        // Sync state across tabs
        sendFABState(true, true)
      } else {
        setIsVisible(false)
        sessionStorage.removeItem('mae_fab_visible')
        sessionStorage.removeItem('mae_fab_floating')
        sessionStorage.removeItem('mae_fab_activated_from_landing')
        // Sync state across tabs
        sendFABState(false, false)
      }
    }

    window.addEventListener('mae-fab-state-change', handleFABStateChange as EventListener)

    return () => {
      window.removeEventListener('mae-fab-state-change', handleFABStateChange as EventListener)
    }
  }, [initClient])

  const handleMicClick = async () => {
    console.log('🎤 GlobalFAB: Mic button clicked, isRecording:', isRecording)

    if (isRecording) {
      console.log('🛑 Stopping recording...')
      stopRecording()
      
      // Update global session state
      updateSessionState({ isRecording: false })
    } else {
      console.log('🎤 Starting recording...')
      
      try {
        // Check if microphone is supported
        if (!navigator.mediaDevices?.getUserMedia) {
          throw new Error('Microphone access is not supported in this browser. Please use Chrome, Firefox, or Safari.')
        }

        // Check current permission status
        try {
          const permissionStatus = await navigator.permissions.query({ name: 'microphone' as PermissionName })
          
          if (permissionStatus.state === 'denied') {
            throw new Error('Microphone access was denied. Please enable microphone permissions in your browser settings and refresh the page.')
          }
        } catch (permError) {
          // Some browsers might not support permissions API, continue anyway
          console.warn('Could not check microphone permissions:', permError)
        }
        
        // Use continuation message or default greeting
        const initialMessage = continuationMessage || "Hi Mae! I'm ready to talk about my parenting questions and pediatric health concerns.";
        
        await startRecording(initialMessage)
        
        // Update global session state
        updateSessionState({ 
          isConnected: true, 
          isRecording: true,
          sessionId: Date.now().toString()
        })
        console.log('✅ Recording started successfully with continuation message')
      } catch (error) {
        console.error('❌ Failed to start recording:', error)
        
        // Show user-friendly error message
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred'
        if (errorMessage.includes('Permission denied') || errorMessage.includes('denied')) {
          alert('🎤 Microphone Permission Required\n\nTo talk with Mae, please:\n1. Click "Allow" when your browser asks for microphone access\n2. Or enable microphone permissions in your browser settings\n3. Refresh the page and try again')
        } else {
          alert(`❌ Voice Chat Error\n\n${errorMessage}`)
        }
      }
    }
  }

  // Convert file to base64
  const fileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => {
        const result = reader.result as string
        const base64 = result.split(',')[1]
        resolve(base64)
      }
      reader.onerror = error => reject(error)
    })
  }

  const handleFileUpload = async () => {
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*,audio/*,.pdf,.doc,.docx,.txt,.csv,.json'
    input.onchange = async (e) => {
      const file = (e.target as HTMLInputElement).files?.[0]
      if (!file) return

      console.log('📁 File selected:', file.name)
      setUploadingFile(true)

      try {
        const base64Data = await fileToBase64(file)
        const mimeType = file.type || 'application/octet-stream'
        
        console.log('📁 File converted to base64, sending to Mae...')
        
        if (isConnected) {
          sendFile(base64Data, mimeType)
          console.log(`✅ File sent to Mae: ${file.name}`)
          
          const acknowledgmentMessage = `I just uploaded a file called "${file.name}". Please acknowledge that you received it and ask if I would like you to analyze it in detail.`
          
          setTimeout(async () => {
            if (!isRecording) {
              await startRecording(acknowledgmentMessage)
            }
          }, 500)
          
        } else {
          console.log('❌ Mae not connected - cannot send file')
        }
        
      } catch (error) {
        console.error('❌ File processing error:', error)
      } finally {
        setUploadingFile(false)
      }
    }
    input.click()
  }

  const handleDisconnect = () => {
    const confirmDisconnect = window.confirm(
      "Are you sure you want to disconnect from Mae?\n\nThis will end your current session and you'll need to start over."
    )

    if (confirmDisconnect) {
      setIsVisible(false)
      setShowFloatingOptions(false)
      sessionStorage.removeItem('mae_fab_visible')
      sessionStorage.removeItem('mae_fab_floating')
      sessionStorage.removeItem('mae_fab_activated_from_landing')
      
      if (isRecording) {
        stopRecording()
      }

      // Update global session state to end session
      updateSessionState({
        isConnected: false,
        isRecording: false,
        isSpeaking: false,
        sessionId: undefined,
        conversationContext: undefined,
        error: undefined
      })

      // Sync state across tabs
      sendFABState(false, false)

      // Notify other components that FAB was reset
      window.dispatchEvent(new CustomEvent('mae-fab-reset'))

      console.log('🔌 Mae session disconnected by user')
    } else {
      // User cancelled, close the options menu
      setShowFloatingOptions(false)
      console.log('🔌 Mae disconnect cancelled by user')
    }
  }

  if (!isVisible) {
    return null
  }

  return (
    <AnimatePresence>
      <motion.div
        className={`fixed z-50 ${className}`}
        style={{ 
          right: '24px',
          bottom: '24px',
          transformOrigin: 'center center'
        }}
        initial={{ 
          opacity: 0,
          scale: 0.5
        }}
        animate={{ 
          opacity: 1, 
          scale: 1
        }}
        exit={{ 
          opacity: 0, 
          scale: 0.2 
        }}
        transition={{ 
          type: "spring",
          stiffness: 100,
          damping: 20,
          mass: 0.8,
          duration: 1.0
        }}
      >
        <div className="relative flex flex-col items-center">
          {/* Expanded Options */}
          <AnimatePresence>
            {showFloatingOptions && (
              <motion.div
                initial={{ opacity: 0, scale: 0.8, y: 10 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.8, y: 10 }}
                transition={{ duration: 0.2 }}
                className="absolute bottom-20 flex flex-col items-center gap-3"
              >
                {/* Disconnect Button */}
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={handleDisconnect}
                  className="w-12 h-12 bg-red-500 hover:bg-red-600 text-white rounded-full shadow-lg flex items-center justify-center"
                  title="Disconnect from Mae"
                >
                  <Power className="w-5 h-5" />
                </motion.button>

                {/* File Upload Button */}
                <motion.button
                  whileHover={{ scale: uploadingFile ? 1 : 1.1 }}
                  whileTap={{ scale: uploadingFile ? 1 : 0.9 }}
                  onClick={uploadingFile ? undefined : handleFileUpload}
                  disabled={uploadingFile}
                  className={`w-12 h-12 ${uploadingFile ? 'bg-blue-300' : 'bg-blue-500 hover:bg-blue-600'} text-white rounded-full shadow-lg flex items-center justify-center ${uploadingFile ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                  title={uploadingFile ? "Uploading..." : "Upload File for Mae to Analyze"}
                >
                  {uploadingFile ? (
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                      className="w-5 h-5 border-2 border-white border-t-transparent rounded-full"
                    />
                  ) : (
                    <Upload className="w-5 h-5" />
                  )}
                </motion.button>

                {/* Options Toggle Button */}
                <motion.button
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  onClick={() => setShowFloatingOptions(!showFloatingOptions)}
                  className="w-12 h-12 bg-gray-600 hover:bg-gray-700 text-white rounded-full shadow-lg flex items-center justify-center"
                  title="Hide Options"
                >
                  <motion.div
                    animate={{ rotate: 90 }}
                    transition={{ duration: 0.2 }}
                    className="text-white font-bold"
                  >
                    ⋯
                  </motion.div>
                </motion.button>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Main Button Container */}
          <div className="flex flex-col items-center gap-3">
            {/* Options Toggle Button - only show when options are hidden */}
            {!showFloatingOptions && (
              <motion.button
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                onClick={() => setShowFloatingOptions(true)}
                className="w-12 h-12 bg-gray-600 hover:bg-gray-700 text-white rounded-full shadow-lg flex items-center justify-center"
                title="More Options"
              >
                <motion.div
                  animate={{ rotate: 0 }}
                  transition={{ duration: 0.2 }}
                  className="text-white font-bold"
                >
                  ⋯
                </motion.div>
              </motion.button>
            )}

            {/* Main Floating Button */}
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleMicClick}
              className="w-16 h-16 bg-transparent hover:bg-gray-900 text-white rounded-full shadow-xl flex items-center justify-center"
              animate={isRecording ? {
                scale: [1, 1.1, 1],
                boxShadow: [
                  "0 4px 20px rgba(20, 184, 166, 0.3)",
                  "0 4px 30px rgba(20, 184, 166, 0.6)",
                  "0 4px 20px rgba(20, 184, 166, 0.3)"
                ]
              } : {}}
              transition={{ duration: 1, repeat: isRecording ? Infinity : 0 }}
              title={isRecording ? "Stop Recording" : "Start Recording"}
            >
              <Image
                src="/OKdarkTsp.png"
                alt="Our Kidz Logo"
                width={32}
                height={32}
                className="w-8 h-8 object-contain"
              />
            </motion.button>
          </div>

          {/* Recording Indicator */}
          {isRecording && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center"
            >
              <motion.div
                animate={{ scale: [1, 1.2, 1] }}
                transition={{ duration: 1, repeat: Infinity }}
                className="w-3 h-3 bg-white rounded-full"
              />
            </motion.div>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  )
}
