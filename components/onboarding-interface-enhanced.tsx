/**
 * Enhanced Onboarding Interface with Personalized Mae Integration
 * 
 * This component integrates the personalized Mae session management with the
 * onboarding process, providing a seamless transition from account creation
 * to personalized AI assistance.
 */

'use client'

import { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { 
  User, 
  Users, 
  CheckCircle2, 
  Mic, 
  MicOff, 
  Sparkles, 
  MessageSquare,
  ArrowRight,
  Brain,
  Heart,
  AlertCircle
} from 'lucide-react'
import { cn } from '@/lib/utils'
import confetti from 'canvas-confetti'
import { motion, AnimatePresence } from 'framer-motion'
import { useAuth } from '@/lib/auth-provider'
import { CollapsibleAuth } from '@/components/auth/collapsible-auth'
import { useGeminiLivePersonalized } from '@/hooks/use-gemini-live-personalized'
import { sessionManager } from '@/lib/session-management'
import { PersonalizedMaeInterface } from '@/components/personalized-mae-interface'

type OnboardingStep = 'authentication' | 'user_info' | 'family_info' | 'mae_introduction' | 'complete'

interface OnboardingInterfaceEnhancedProps {
  className?: string
  onComplete?: (userData: any) => void
  enableMaeIntegration?: boolean
}

export function OnboardingInterfaceEnhanced({ 
  className,
  onComplete,
  enableMaeIntegration = true
}: OnboardingInterfaceEnhancedProps) {
  const { user: authUser, loading: authLoading } = useAuth()
  const [currentStep, setCurrentStep] = useState<OnboardingStep>('authentication')
  const [onboardingData, setOnboardingData] = useState<any>({})
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState('')
  const [showMaeDemo, setShowMaeDemo] = useState(false)

  // Personalized Mae hook for the final step
  const {
    status: maeStatus,
    isConnected: maeConnected,
    isRecording: maeRecording,
    userContext,
    isLoadingContext,
    initClient: initMaeClient,
    startRecording: startMaeRecording,
    stopRecording: stopMaeRecording
  } = useGeminiLivePersonalized({
    authUserId: authUser?.id,
    autoLoadContext: true,
    saveConversations: true
  })

  // Calculate progress
  const steps = ['authentication', 'user_info', 'family_info', 'mae_introduction', 'complete']
  const progress = (steps.indexOf(currentStep) / (steps.length - 1)) * 100

  // Handle authentication completion
  useEffect(() => {
    if (authUser && currentStep === 'authentication') {
      console.log('✅ User authenticated, moving to user info step')
      setCurrentStep('user_info')
    }
  }, [authUser, currentStep])

  // Trigger confetti on completion
  useEffect(() => {
    if (currentStep === 'complete') {
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      })
    }
  }, [currentStep])

  // Handle Mae introduction step initialization
  useEffect(() => {
    if (currentStep === 'mae_introduction' && enableMaeIntegration && authUser?.id) {
      console.log('🤖 Initializing Mae for introduction step')
      if (!maeConnected && !isLoadingContext) {
        initMaeClient()
      }
    }
  }, [currentStep, enableMaeIntegration, authUser?.id, maeConnected, isLoadingContext, initMaeClient])

  const handleStepComplete = useCallback((stepData: any) => {
    setOnboardingData((prev: any) => ({ ...prev, ...stepData }))
    
    const nextSteps: Record<OnboardingStep, OnboardingStep> = {
      authentication: 'user_info',
      user_info: 'family_info',
      family_info: enableMaeIntegration ? 'mae_introduction' : 'complete',
      mae_introduction: 'complete',
      complete: 'complete'
    }
    
    const nextStep = nextSteps[currentStep]
    console.log(`✅ Step '${currentStep}' completed, moving to '${nextStep}'`)
    setCurrentStep(nextStep)
    
    if (nextStep === 'complete' && onComplete) {
      onComplete({ ...onboardingData, ...stepData })
    }
  }, [currentStep, enableMaeIntegration, onboardingData, onComplete])

  const handleMaeIntroduction = useCallback(async () => {
    if (!maeConnected) {
      await initMaeClient()
    }
    
    // Start Mae with a personalized onboarding message
    const introMessage = userContext 
      ? `Hello Mae! I just completed my onboarding for Our Kidz. My name is ${userContext.user.name} and I have ${userContext.family_members.length} family member${userContext.family_members.length !== 1 ? 's' : ''}: ${userContext.family_members.map(m => m.name).join(', ')}. I'm excited to start using Our Kidz with your help!`
      : "Hello Mae! I just completed my onboarding for Our Kidz. I'm excited to start using the platform with your help!"
    
    await startMaeRecording(introMessage)
  }, [maeConnected, initMaeClient, startMaeRecording, userContext])

  // Render different steps
  const renderStep = () => {
    switch (currentStep) {
      case 'authentication':
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Welcome to Our Kidz</h2>
              <p className="text-gray-600">
                Let&apos;s start by creating your account or signing in to continue.
              </p>
            </div>
            <CollapsibleAuth 
              defaultOpen={true}
              onAuthSuccess={() => console.log('Auth successful, proceeding to next step')}
            />
          </motion.div>
        )

      case 'user_info':
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Tell Us About Yourself</h2>
              <p className="text-gray-600">
                Help us personalize your experience by sharing some basic information.
              </p>
            </div>
            
            <Card>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <CollapsibleAuth 
                    hideWhenAuthenticated={false}
                    className="mb-4"
                  />
                  
                  {authUser && (
                    <div className="flex items-center gap-2 text-green-600 mb-4">
                      <CheckCircle2 className="w-5 h-5" />
                      <span className="text-sm">Ready to continue with {authUser?.email}</span>
                    </div>
                  )}
                  
                  <Alert>
                    <User className="h-4 w-4" />
                    <AlertDescription>
                      Your basic profile information has been automatically set up. 
                      Mae will use this information to provide personalized guidance for your family.
                    </AlertDescription>
                  </Alert>
                  
                  <div className="flex justify-end">
                    <Button onClick={() => handleStepComplete({})}>
                      Continue to Family Information
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )

      case 'family_info':
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Your Family Members</h2>
              <p className="text-gray-600">
                Add your children and family members so Mae can provide tailored advice.
              </p>
            </div>
            
            <Card>
              <CardContent className="p-6">
                <div className="space-y-4">
                  <Alert>
                    <Users className="h-4 w-4" />
                    <AlertDescription>
                      You can add family members now or do this later with Mae&apos;s voice assistance. 
                      Mae can help you add children through natural conversation.
                    </AlertDescription>
                  </Alert>
                  
                  <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <Mic className="w-4 h-4 text-blue-600" />
                      <span className="font-medium text-blue-900">Voice-Powered Setup</span>
                    </div>
                    <p className="text-sm text-blue-800">
                      In the next step, you can tell Mae about your children using natural speech. 
                      Just say something like &quot;I have a 3-year-old daughter named Emma and a 6-year-old son named Jake.&quot;
                    </p>
                  </div>
                  
                  <div className="flex justify-between">
                    <Button 
                      variant="outline" 
                      onClick={() => setCurrentStep('user_info')}
                    >
                      Back
                    </Button>
                    <Button onClick={() => handleStepComplete({})}>
                      {enableMaeIntegration ? 'Meet Mae' : 'Complete Setup'}
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        )

      case 'mae_introduction':
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            className="space-y-6"
          >
            <div className="text-center">
              <div className="flex items-center justify-center gap-2 mb-4">
                <Sparkles className="w-8 h-8 text-teal-500" />
                <h2 className="text-2xl font-bold text-gray-900">Meet Mae, Your AI Assistant</h2>
              </div>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Mae is your personalized AI health coach. She knows about your family and is ready to help 
                with parenting questions, health concerns, and family guidance.
              </p>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Mae Features */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Brain className="w-5 h-5 text-purple-500" />
                    Mae&apos;s Capabilities
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center gap-2 text-sm">
                    <CheckCircle2 className="w-4 h-4 text-green-500" />
                    <span>Personalized advice for your family</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <CheckCircle2 className="w-4 h-4 text-green-500" />
                    <span>Remembers your children&apos;s names and ages</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <CheckCircle2 className="w-4 h-4 text-green-500" />
                    <span>Provides evidence-based health information</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <CheckCircle2 className="w-4 h-4 text-green-500" />
                    <span>Helps find local healthcare providers</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <CheckCircle2 className="w-4 h-4 text-green-500" />
                    <span>Sends conversation summaries via email</span>
                  </div>
                  
                  {userContext && (
                    <div className="mt-4 p-3 bg-teal-50 border border-teal-200 rounded">
                      <p className="text-sm text-teal-800">
                        <strong>Personalized for you:</strong> Mae knows you&apos;re {userContext.user.name} 
                        {userContext.family_members.length > 0 && (
                          <span> and will remember {userContext.family_members.map(m => m.name).join(', ')}</span>
                        )}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>
              
              {/* Mae Interface */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <MessageSquare className="w-5 h-5 text-teal-500" />
                    Try Mae Now
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  {isLoadingContext ? (
                    <div className="flex items-center gap-2 text-gray-600 py-8">
                      <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-teal-600"></div>
                      <span>Loading your personalized Mae session...</span>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      <div className="text-center">
                        <Button
                          onClick={handleMaeIntroduction}
                          disabled={maeRecording || isLoadingContext}
                          className={`w-16 h-16 rounded-full ${
                            maeRecording
                              ? 'bg-red-500 hover:bg-red-600 animate-pulse'
                              : 'bg-teal-500 hover:bg-teal-600'
                          }`}
                        >
                          {maeRecording ? (
                            <MicOff className="w-8 h-8 text-white" />
                          ) : (
                            <Mic className="w-8 h-8 text-white" />
                          )}
                        </Button>
                        
                        <div className="mt-3">
                          <p className="text-sm font-medium">
                            {maeRecording 
                              ? 'Listening... introduce yourself to Mae!'
                              : 'Click to introduce yourself to Mae'
                            }
                          </p>
                          <p className="text-xs text-gray-500 mt-1">
                            {maeStatus}
                          </p>
                        </div>
                      </div>
                      
                      <div className="bg-gray-50 rounded-lg p-3">
                        <p className="text-xs text-gray-600">
                          <strong>Suggested intro:</strong> &quot;Hi Mae! I just completed my onboarding. 
                          I&apos;m excited to start using Our Kidz with your help!&quot;
                        </p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
            
            <div className="flex justify-between">
              <Button 
                variant="outline" 
                onClick={() => setCurrentStep('family_info')}
              >
                Back
              </Button>
              <Button onClick={() => handleStepComplete({})}>
                Complete Onboarding
                <CheckCircle2 className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </motion.div>
        )

      case 'complete':
        return (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center space-y-6"
          >
            <div className="flex items-center justify-center gap-2 mb-4">
              <CheckCircle2 className="w-12 h-12 text-green-500" />
              <h2 className="text-3xl font-bold text-gray-900">Welcome to Our Kidz!</h2>
            </div>
            
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Your account is set up and Mae is ready to help with all your parenting questions. 
              Start a conversation anytime!
            </p>
            
            <Card className="max-w-2xl mx-auto">
              <CardContent className="p-6">
                <h3 className="font-medium text-lg mb-4">What&apos;s Next?</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div className="flex items-center gap-2">
                    <MessageSquare className="w-4 h-4 text-teal-500" />
                    <span>Start chatting with Mae</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Users className="w-4 h-4 text-blue-500" />
                    <span>Add more family members</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Heart className="w-4 h-4 text-red-500" />
                    <span>Get personalized health advice</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Sparkles className="w-4 h-4 text-purple-500" />
                    <span>Explore Our Kidz features</span>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            {enableMaeIntegration && (
              <div className="mt-8">
                <Button
                  onClick={() => setShowMaeDemo(true)}
                  className="bg-teal-500 hover:bg-teal-600"
                >
                  Continue with Mae
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              </div>
            )}
          </motion.div>
        )

      default:
        return null
    }
  }

  if (showMaeDemo) {
    return (
      <div className={cn("max-w-6xl mx-auto", className)}>
        <PersonalizedMaeInterface 
          showUserContext={true}
          autoStart={false}
        />
      </div>
    )
  }

  return (
    <div className={cn("max-w-4xl mx-auto", className)}>
      {/* Progress Header */}
      <div className="mb-8">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Account Setup</h1>
            <p className="text-gray-600">
              {currentStep === 'complete' 
                ? 'Setup complete!' 
                : `Step ${steps.indexOf(currentStep) + 1} of ${steps.length}`
              }
            </p>
          </div>
          <Badge variant="outline" className="text-xs">
            {enableMaeIntegration ? 'With Mae Integration' : 'Standard Setup'}
          </Badge>
        </div>
        
        <Progress value={progress} className="h-2" />
        
        <div className="flex justify-between mt-2 text-xs text-gray-500">
          <span>Authentication</span>
          <span>Profile</span>
          <span>Family</span>
          {enableMaeIntegration && <span>Mae</span>}
          <span>Complete</span>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <Alert className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Step Content */}
      <AnimatePresence mode="wait">
        {renderStep()}
      </AnimatePresence>
    </div>
  )
}