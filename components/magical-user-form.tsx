"use client"

import React, { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence, useSpring, useTransform } from 'framer-motion'
import { Card } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { But<PERSON> } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { cn } from '@/lib/utils'
import { 
  Sparkles, 
  Star, 
  Mail, 
  User, 
  Phone, 
  MapPin, 
  Calendar,
  Shield,
  Heart,
  Check,
  ArrowRight,
  Wand2,
  Zap
} from 'lucide-react'

interface FormData {
  email: string
  fullName: string
  phoneNumber: string
  zipCode: string
  dateOfBirth: string
  role: string
  emergencyName: string
  emergencyPhone: string
  emergencyRelationship: string
}

interface MagicalUserFormProps {
  onComplete?: (data: FormData) => void
  onBack?: () => void
}

const fieldConfig = [
  { 
    id: 'email', 
    label: 'Email Address', 
    icon: Mail, 
    type: 'email',
    placeholder: '<EMAIL>',
    color: 'from-blue-500 to-cyan-500'
  },
  { 
    id: 'fullName', 
    label: 'Full Name', 
    icon: User, 
    type: 'text',
    placeholder: 'Your magical name',
    color: 'from-purple-500 to-pink-500'
  },
  { 
    id: 'phoneNumber', 
    label: 'Phone Number', 
    icon: Phone, 
    type: 'tel',
    placeholder: '(*************',
    color: 'from-green-500 to-emerald-500'
  },
  { 
    id: 'zipCode', 
    label: 'ZIP Code', 
    icon: MapPin, 
    type: 'text',
    placeholder: '12345',
    color: 'from-orange-500 to-red-500'
  },
  { 
    id: 'dateOfBirth', 
    label: 'Date of Birth', 
    icon: Calendar, 
    type: 'date',
    placeholder: 'mm/dd/yyyy',
    color: 'from-indigo-500 to-blue-500'
  },
  { 
    id: 'role', 
    label: 'Role', 
    icon: Shield, 
    type: 'select',
    placeholder: 'Select your role',
    options: ['Parent', 'Guardian', 'Caregiver', 'Other'],
    color: 'from-teal-500 to-cyan-500'
  }
]

// Sparkle component for background effects
const Sparkle = ({ delay = 0 }: { delay?: number }) => {
  const randomX = Math.random() * 100
  const randomY = Math.random() * 100
  const randomDuration = 3 + Math.random() * 4
  
  return (
    <motion.div
      className="absolute pointer-events-none"
      initial={{ 
        x: `${randomX}%`, 
        y: `${randomY}%`,
        scale: 0,
        opacity: 0 
      }}
      animate={{ 
        scale: [0, 1, 0],
        opacity: [0, 1, 0],
        rotate: [0, 180, 360]
      }}
      transition={{
        duration: randomDuration,
        delay: delay,
        repeat: Infinity,
        repeatDelay: Math.random() * 5
      }}
    >
      <Sparkles className="h-4 w-4 text-primary/30" />
    </motion.div>
  )
}

// Floating orb component
const FloatingOrb = ({ 
  x, 
  y, 
  size, 
  color, 
  delay = 0 
}: { 
  x: number
  y: number
  size: number
  color: string
  delay?: number
}) => {
  return (
    <motion.div
      className={cn(
        "absolute rounded-full blur-xl opacity-20",
        color
      )}
      style={{
        width: size,
        height: size,
        left: `${x}%`,
        top: `${y}%`,
      }}
      animate={{
        x: [0, 30, -30, 0],
        y: [0, -30, 30, 0],
        scale: [1, 1.2, 0.8, 1],
      }}
      transition={{
        duration: 20,
        delay,
        repeat: Infinity,
        ease: "easeInOut"
      }}
    />
  )
}

export default function MagicalUserForm({ onComplete, onBack }: MagicalUserFormProps) {
  const [currentField, setCurrentField] = useState(0)
  const [completedFields, setCompletedFields] = useState<number[]>([])
  const [showEmergencyContact, setShowEmergencyContact] = useState(false)
  const [formData, setFormData] = useState<FormData>({
    email: '',
    fullName: '',
    phoneNumber: '',
    zipCode: '',
    dateOfBirth: '',
    role: '',
    emergencyName: '',
    emergencyPhone: '',
    emergencyRelationship: ''
  })

  const handleFieldComplete = (fieldId: string, value: string) => {
    setFormData(prev => ({ ...prev, [fieldId]: value }))
    
    if (!completedFields.includes(currentField)) {
      setCompletedFields([...completedFields, currentField])
    }
    
    if (currentField < fieldConfig.length - 1) {
      setTimeout(() => setCurrentField(currentField + 1), 300)
    } else if (!showEmergencyContact) {
      setTimeout(() => setShowEmergencyContact(true), 300)
    }
  }

  const handleSubmit = () => {
    if (onComplete) {
      onComplete(formData)
    }
  }

  const progressPercentage = ((completedFields.length + (showEmergencyContact ? 1 : 0)) / (fieldConfig.length + 1)) * 100

  return (
    <div className="relative min-h-[600px] w-full overflow-hidden rounded-2xl bg-gradient-to-br from-background via-primary/5 to-background p-8">
      {/* Animated background elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Floating orbs */}
        <FloatingOrb x={10} y={20} size={200} color="bg-primary" delay={0} />
        <FloatingOrb x={70} y={60} size={150} color="bg-purple-500" delay={2} />
        <FloatingOrb x={50} y={30} size={100} color="bg-cyan-500" delay={4} />
        
        {/* Sparkles */}
        {[...Array(15)].map((_, i) => (
          <Sparkle key={i} delay={i * 0.5} />
        ))}
        
        {/* Grid pattern */}
        <div className="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-5" />
      </div>

      {/* Content */}
      <div className="relative z-10">
        {/* Header with magical title */}
        <motion.div 
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <div className="inline-flex items-center gap-2 mb-4">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            >
              <Wand2 className="h-6 w-6 text-primary" />
            </motion.div>
            <h2 className="text-3xl font-bold bg-gradient-to-r from-primary via-purple-500 to-primary bg-clip-text text-transparent">
              Let's Create Your Magical Profile
            </h2>
            <motion.div
              animate={{ rotate: -360 }}
              transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            >
              <Wand2 className="h-6 w-6 text-primary" />
            </motion.div>
          </div>
          
          {/* Progress constellation */}
          <div className="flex justify-center gap-2">
            {[...Array(fieldConfig.length + 1)].map((_, i) => (
              <motion.div
                key={i}
                initial={{ scale: 0 }}
                animate={{ 
                  scale: completedFields.includes(i) || (i === fieldConfig.length && showEmergencyContact) ? 1 : 0.5,
                  opacity: completedFields.includes(i) || (i === fieldConfig.length && showEmergencyContact) ? 1 : 0.3
                }}
                transition={{ type: "spring", stiffness: 500, damping: 30 }}
              >
                <Star 
                  className={cn(
                    "h-5 w-5 transition-all",
                    completedFields.includes(i) || (i === fieldConfig.length && showEmergencyContact)
                      ? "text-primary fill-primary" 
                      : "text-muted-foreground"
                  )}
                />
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Main form area with floating bubbles */}
        <div className="relative min-h-[400px] flex items-center justify-center">
          <AnimatePresence mode="wait">
            {!showEmergencyContact ? (
              <motion.div
                key={currentField}
                initial={{ opacity: 0, scale: 0.8, y: 50 }}
                animate={{ opacity: 1, scale: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.8, y: -50 }}
                transition={{ type: "spring", stiffness: 300, damping: 25 }}
                className="w-full max-w-md"
              >
                <MagicalField
                  field={fieldConfig[currentField]}
                  value={formData[fieldConfig[currentField].id as keyof FormData]}
                  onComplete={(value) => handleFieldComplete(fieldConfig[currentField].id, value)}
                  isActive={true}
                />
              </motion.div>
            ) : (
              <motion.div
                key="emergency"
                initial={{ opacity: 0, scale: 0.8, rotateY: 180 }}
                animate={{ opacity: 1, scale: 1, rotateY: 0 }}
                transition={{ type: "spring", stiffness: 200, damping: 20 }}
                className="w-full max-w-lg"
              >
                <EmergencyContactSection
                  data={{
                    emergencyName: formData.emergencyName,
                    emergencyPhone: formData.emergencyPhone,
                    emergencyRelationship: formData.emergencyRelationship
                  }}
                  onChange={(field, value) => setFormData(prev => ({ ...prev, [field]: value }))}
                  onComplete={handleSubmit}
                  onBack={onBack}
                />
              </motion.div>
            )}
          </AnimatePresence>

          {/* Completed fields orbit */}
          <div className="absolute inset-0 pointer-events-none">
            {completedFields.map((fieldIndex, i) => {
              const angle = (i * 360) / completedFields.length
              const radius = 150
              const x = Math.cos((angle * Math.PI) / 180) * radius
              const y = Math.sin((angle * Math.PI) / 180) * radius
              const Icon = fieldConfig[fieldIndex].icon
              
              return (
                <motion.div
                  key={fieldIndex}
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{ 
                    scale: 1, 
                    opacity: 0.7,
                    x: x,
                    y: y
                  }}
                  transition={{ type: "spring", stiffness: 200, damping: 20 }}
                  className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2"
                >
                  <div className="w-12 h-12 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 border border-primary/30 flex items-center justify-center">
                    <Icon className="h-5 w-5 text-primary" />
                  </div>
                </motion.div>
              )
            })}
          </div>
        </div>

        {/* Navigation hint */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1 }}
          className="text-center mt-8"
        >
          <p className="text-sm text-muted-foreground">
            Press Enter or click Continue to proceed ✨
          </p>
        </motion.div>
      </div>
    </div>
  )
}

// Individual magical field component
function MagicalField({ 
  field, 
  value, 
  onComplete, 
  isActive 
}: {
  field: typeof fieldConfig[0]
  value: string
  onComplete: (value: string) => void
  isActive: boolean
}) {
  const [localValue, setLocalValue] = useState(value)
  const [isFocused, setIsFocused] = useState(false)
  const Icon = field.icon

  const handleSubmit = () => {
    if (localValue.trim()) {
      onComplete(localValue)
    }
  }

  return (
    <motion.div
      className="relative"
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      {/* Glow effect */}
      <motion.div
        className={cn(
          "absolute inset-0 rounded-2xl bg-gradient-to-r opacity-30 blur-xl",
          field.color
        )}
        animate={{
          opacity: isFocused ? 0.5 : 0.3,
          scale: isFocused ? 1.1 : 1
        }}
      />

      <Card className="relative border-primary/20 bg-card/80 backdrop-blur-xl p-6 shadow-2xl">
        {/* Icon with animation */}
        <motion.div
          className="flex justify-center mb-4"
          animate={{
            rotate: isFocused ? [0, -10, 10, -10, 10, 0] : 0,
            scale: isFocused ? 1.1 : 1
          }}
          transition={{ duration: 0.5 }}
        >
          <div className={cn(
            "w-16 h-16 rounded-2xl bg-gradient-to-br flex items-center justify-center shadow-lg",
            field.color
          )}>
            <Icon className="h-8 w-8 text-white" />
          </div>
        </motion.div>

        {/* Label */}
        <Label className="text-center block mb-4 text-lg font-semibold">
          {field.label}
        </Label>

        {/* Input field */}
        {field.type === 'select' ? (
          <Select value={localValue} onValueChange={setLocalValue}>
            <SelectTrigger 
              className="w-full h-12 text-center border-primary/30 focus:border-primary"
              onFocus={() => setIsFocused(true)}
              onBlur={() => setIsFocused(false)}
            >
              <SelectValue placeholder={field.placeholder} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map(option => (
                <SelectItem key={option} value={option.toLowerCase()}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        ) : (
          <Input
            type={field.type}
            value={localValue}
            onChange={(e) => setLocalValue(e.target.value)}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            onKeyPress={(e) => e.key === 'Enter' && handleSubmit()}
            placeholder={field.placeholder}
            className="w-full h-12 text-center text-lg border-primary/30 focus:border-primary bg-background/50"
          />
        )}

        {/* Continue button */}
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: localValue ? 1 : 0, y: localValue ? 0 : 10 }}
          className="mt-4"
        >
          <Button
            onClick={handleSubmit}
            className="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 shadow-lg"
          >
            <Sparkles className="h-4 w-4 mr-2" />
            Continue
            <ArrowRight className="h-4 w-4 ml-2" />
          </Button>
        </motion.div>
      </Card>
    </motion.div>
  )
}

// Emergency contact section
function EmergencyContactSection({ 
  data, 
  onChange, 
  onComplete,
  onBack 
}: {
  data: {
    emergencyName: string
    emergencyPhone: string
    emergencyRelationship: string
  }
  onChange: (field: string, value: string) => void
  onComplete: () => void
  onBack?: () => void
}) {
  const allFieldsFilled = data.emergencyName && data.emergencyPhone && data.emergencyRelationship

  return (
    <Card className="border-primary/20 bg-card/80 backdrop-blur-xl p-8 shadow-2xl">
      <div className="text-center mb-6">
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ type: "spring", stiffness: 300, damping: 20 }}
          className="inline-flex w-16 h-16 rounded-2xl bg-gradient-to-br from-red-500 to-pink-500 items-center justify-center mb-4"
        >
          <Heart className="h-8 w-8 text-white" />
        </motion.div>
        <h3 className="text-2xl font-bold mb-2">Emergency Contact</h3>
        <p className="text-muted-foreground">Who should we contact in case of emergency?</p>
      </div>

      <div className="space-y-4">
        <div>
          <Label>Name</Label>
          <Input
            value={data.emergencyName}
            onChange={(e) => onChange('emergencyName', e.target.value)}
            placeholder="Emergency contact name"
            className="border-primary/30 focus:border-primary"
          />
        </div>
        
        <div>
          <Label>Phone</Label>
          <Input
            type="tel"
            value={data.emergencyPhone}
            onChange={(e) => onChange('emergencyPhone', e.target.value)}
            placeholder="(*************"
            className="border-primary/30 focus:border-primary"
          />
        </div>
        
        <div>
          <Label>Relationship</Label>
          <Input
            value={data.emergencyRelationship}
            onChange={(e) => onChange('emergencyRelationship', e.target.value)}
            placeholder="Spouse, Parent, etc."
            className="border-primary/30 focus:border-primary"
          />
        </div>
      </div>

      <div className="flex gap-3 mt-6">
        {onBack && (
          <Button variant="outline" onClick={onBack} className="flex-1">
            Back
          </Button>
        )}
        <motion.div
          className="flex-1"
          animate={{
            scale: allFieldsFilled ? [1, 1.02, 1] : 1,
          }}
          transition={{
            duration: 1.5,
            repeat: allFieldsFilled ? Infinity : 0,
            repeatType: "reverse"
          }}
        >
          <Button
            onClick={onComplete}
            disabled={!allFieldsFilled}
            className="w-full bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 shadow-lg disabled:opacity-50"
          >
            <Zap className="h-4 w-4 mr-2" />
            Complete Magic
            <Check className="h-4 w-4 ml-2" />
          </Button>
        </motion.div>
      </div>
    </Card>
  )
}