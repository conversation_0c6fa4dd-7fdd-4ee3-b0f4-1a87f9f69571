"use client"

import React, { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  MessageCircle, 
  Mic, 
  Settings, 
  Activity,
  Brain,
  Loader2,
  Pause,
  Square,
  Play,
} from 'lucide-react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'


interface MaeActivity {
  id: string
  type: 'tool' | 'function' | 'processing' | 'listening' | 'speaking'
  name: string
  description: string
  status: 'active' | 'completed' | 'pending' | 'error'
  timestamp: number
  duration?: number
}

interface MaeSessionStatusProps {
  sessionActive?: boolean
  isListening?: boolean
  isSpeaking?: boolean
  currentTool?: string
  onboardingStep?: string
  onPauseSession?: () => void
  onEndSession?: () => void
  onStartSession?: () => void
}

export function EnhancedMaeStatus({ 
  sessionActive = true,
  isListening = false,
  isSpeaking = false,
  currentTool,
  onboardingStep = 'welcome',
  onPauseSession,
  onEndSession,
  onStartSession
}: MaeSessionStatusProps) {
  const [activities, setActivities] = useState<MaeActivity[]>([])
  const [connectionStatus, setConnectionStatus] = useState<'connected' | 'connecting' | 'disconnected'>('connected')
  const [voiceLevel, setVoiceLevel] = useState(0)
  const [processingStatus, setProcessingStatus] = useState('')
  const [isPaused, setIsPaused] = useState(false)
  const audioLevelRef = useRef<number>(0)
  const [audioLevels, setAudioLevels] = useState<number[]>(new Array(12).fill(0))

  // Debug voice state props
  useEffect(() => {
    console.log('🎙️ Enhanced Mae Status - Voice props updated:', {
      sessionActive,
      isListening,
      isSpeaking,
      currentTool,
      voiceLevel
    })
  }, [sessionActive, isListening, isSpeaking, currentTool, voiceLevel])

  // Direct voice state backup for reliable animation
  const [directVoiceState, setDirectVoiceState] = useState({ listening: false, speaking: false })

  // Direct event listener for voice state changes (backup)
  useEffect(() => {
    const handleDirectVoiceStateChange = (event: CustomEvent) => {
      const { listening, speaking } = event.detail
      console.log('🎙️ Enhanced Mae Status - Direct voice state change:', { listening, speaking })
      setDirectVoiceState({ listening: listening || false, speaking: speaking || false })
    }

    if (typeof window !== 'undefined') {
      window.addEventListener('mae-voice-state-change', handleDirectVoiceStateChange as EventListener)
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('mae-voice-state-change', handleDirectVoiceStateChange as EventListener)
      }
    }
  }, [])

  // Listen for audio level updates from Mae sessions
  useEffect(() => {
    const handleAudioLevel = (event: CustomEvent) => {
      const { level } = event.detail
      audioLevelRef.current = level || 0
      setVoiceLevel(level || 0)
      
      // Generate dynamic audio levels for visualization
      if (actualIsListening || actualIsSpeaking) {
        const newLevels = Array.from({ length: 12 }, (_, i) => {
          const baseLevel = level || 0.3
          const variation = Math.random() * 0.4 + 0.3 // 0.3 to 0.7
          const frequencyModifier = Math.sin((Date.now() / 100) + i * 0.5) * 0.2 + 0.8
          return Math.min(1, baseLevel * variation * frequencyModifier)
        })
        setAudioLevels(newLevels)
      } else {
        setAudioLevels(new Array(12).fill(0.1))
      }
    }

    const handleMaeSessionStatus = (event: CustomEvent) => {
      const { status, details } = event.detail
      setConnectionStatus(status)
      if (details) {
        setProcessingStatus(details)
      }
    }

    if (typeof window !== 'undefined') {
      window.addEventListener('mae-audio-level', handleAudioLevel as EventListener)
      window.addEventListener('mae-session-status', handleMaeSessionStatus as EventListener)
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('mae-audio-level', handleAudioLevel as EventListener)
        window.removeEventListener('mae-session-status', handleMaeSessionStatus as EventListener)
      }
    }
  }, [])

  // Fallback animation when no real audio levels are available
  useEffect(() => {
    let animationFrame: number
    
    if (actualIsListening || actualIsSpeaking) {
      const animate = () => {
        // Generate fallback animation levels if no real audio data
        const hasRecentAudioData = audioLevels.some(level => level > 0.2)
        if (!hasRecentAudioData) {
          const newLevels = Array.from({ length: 12 }, (_, i) => {
            const time = Date.now() / 1000
            const frequency = 2 + i * 0.3 // Different frequency for each bar
            const amplitude = 0.3 + Math.sin(time * frequency) * 0.4
            return Math.max(0.1, Math.min(1, amplitude))
          })
          setAudioLevels(newLevels)
        }
        animationFrame = requestAnimationFrame(animate)
      }
      
      animate()
    } else {
      // Reset to idle state
      setAudioLevels(new Array(12).fill(0.1))
    }
    
    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame)
      }
    }
  }, [isListening, isSpeaking])

  // Determine actual voice state (use direct state as fallback)
  const actualIsListening = isListening || directVoiceState.listening
  const actualIsSpeaking = isSpeaking || directVoiceState.speaking

  // Listen for Mae function tool activities
  useEffect(() => {
    const handleToolActivity = (event: CustomEvent) => {
      const { toolName, status, description } = event.detail
      
      const newActivity: MaeActivity = {
        id: Date.now().toString(),
        type: 'tool',
        name: toolName,
        description: description || `Using ${toolName}`,
        status: status || 'active',
        timestamp: Date.now()
      }

      setActivities(prev => [newActivity, ...prev.slice(0, 4)]) // Keep last 5 activities
    }

    const handleFunctionCall = (event: CustomEvent) => {
      const { functionName, parameters, status } = event.detail
      
      const newActivity: MaeActivity = {
        id: Date.now().toString(),
        type: 'function',
        name: functionName,
        description: `Processing ${functionName.replace(/_/g, ' ')}`,
        status: status || 'active',
        timestamp: Date.now()
      }

      setActivities(prev => [newActivity, ...prev.slice(0, 4)])
    }

    const handleProcessingUpdate = (event: CustomEvent) => {
      setProcessingStatus(event.detail.message || '')
    }

    // Listen for various Mae events
    if (typeof window !== 'undefined') {
      window.addEventListener('mae-tool-activity', handleToolActivity as EventListener)
      window.addEventListener('mae-function-call', handleFunctionCall as EventListener)
      window.addEventListener('mae-processing-update', handleProcessingUpdate as EventListener)
    }

    return () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('mae-tool-activity', handleToolActivity as EventListener)
        window.removeEventListener('mae-function-call', handleFunctionCall as EventListener)
        window.removeEventListener('mae-processing-update', handleProcessingUpdate as EventListener)
      }
    }
  }, [])

  // Simulate some initial activities
  // useEffect(() => {
  //   const initialActivities: MaeActivity[] = [
  //     {
  //       id: '1',
  //       type: 'processing',
  //       name: 'session_initialized',
  //       description: 'Mae session continued from landing page',
  //       status: 'completed',
  //       timestamp: Date.now() - 10000
  //     },
  //     {
  //       id: '2',
  //       type: 'function',
  //       name: 'get_onboarding_progress',
  //       description: 'Checking current onboarding progress',
  //       status: 'completed',
  //       timestamp: Date.now() - 8000
  //     }
  //   ]
  //   setActivities(initialActivities)
  // }, [])

  // Session control handlers
  const handlePauseSession = () => {
    setIsPaused(!isPaused)
    
    if (onPauseSession) {
      onPauseSession()
    }
    
    // Dispatch session control event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('mae-session-control', {
        detail: { action: isPaused ? 'resume' : 'pause' }
      }))
    }
  }

  const handleEndSession = () => {
    if (onEndSession) {
      onEndSession()
    }
    
    // Dispatch session control event
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('mae-session-control', {
        detail: { action: 'end' }
      }))
    }
    
    setConnectionStatus('disconnected')
    setIsPaused(false)
  }

  const getActivityIcon = (activity: MaeActivity) => {
    switch (activity.type) {
      case 'tool':
        return <Settings className="h-3 w-3" />
      case 'function':
        return <Brain className="h-3 w-3" />
      case 'processing':
        return <Activity className="h-3 w-3" />
      case 'listening':
        return <Mic className="h-3 w-3" />
      case 'speaking':
        return <MessageCircle className="h-3 w-3" />
      default:
        return <Activity className="h-3 w-3" />
    }
  }

  const getActivityColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-blue-500'
      case 'completed':
        return 'text-green-500'
      case 'pending':
        return 'text-yellow-500'
      case 'error':
        return 'text-red-500'
      default:
        return 'text-gray-500'
    }
  }

  const WaveformAnimation = () => {
    const isActive = actualIsSpeaking || actualIsListening
    const bars = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]

    return (
      <div className="flex items-center justify-center gap-1 h-12 px-2">
        {bars.map(barIndex => {
          // Use real audio levels if available, otherwise fallback to static heights
          const audioLevel = audioLevels[barIndex] || 0.1
          const minHeight = 4
          const maxHeight = 32
          const dynamicHeight = isActive ? 
            Math.max(minHeight, audioLevel * maxHeight) : 
            minHeight

          // Enhanced animation based on voice state
          const animationClass = isActive ? 
            (actualIsSpeaking ? 
              "animate-pulse" : 
              "animate-pulse"
            ) : ""

          const colorClass = isActive ?
            (actualIsSpeaking ?
              "bg-gradient-to-t from-teal-400 via-blue-400 to-blue-500" :
              "bg-gradient-to-t from-green-400 via-teal-400 to-teal-500"
            ) :
            "bg-gray-400"

          return (
            <div
              key={barIndex}
              className={cn(
                "w-2 rounded-full transition-all duration-200 ease-out transform",
                colorClass,
                animationClass,
                isActive && "shadow-sm"
              )}
              style={{
                height: `${dynamicHeight}px`,
                animationDelay: `${barIndex * 80}ms`,
                animationDuration: actualIsSpeaking ? '0.8s' : '1.2s',
                transform: isActive ? `scaleY(${0.8 + audioLevel * 0.4})` : 'scaleY(1)'
              }}
            />
          )
        })}
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {/* Main Mae Status Card */}
      <Card className="healthcare-card border-l-4 border-l-blue-500">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center gap-2">
            <motion.div
              animate={{ 
                scale: sessionActive ? [1, 1.1, 1] : 1,
                rotate: sessionActive ? [0, 5, -5, 0] : 0
              }}
              transition={{ 
                duration: 2, 
                repeat: sessionActive ? Infinity : 0,
                repeatType: "reverse"
              }}
            >
              <MessageCircle className={cn(
                "h-4 w-4",
                sessionActive ? "text-teal-500" : "text-gray-400"
              )} />
            </motion.div>
            Mae Session Status
            <Badge variant={sessionActive ? "default" : "secondary"} className="ml-auto">
              {connectionStatus === 'connected' ? 'Active' : 
               connectionStatus === 'connecting' ? 'Connecting' : 'Offline'}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Voice Activity Visualization */}
          <div className="text-center space-y-3">
            <div className="relative">
              <WaveformAnimation />
              <div className="flex items-center justify-center gap-2 mt-2">
                <div className={cn(
                  "w-2 h-2 rounded-full",
                  actualIsSpeaking ? "bg-teal-500 animate-pulse" :
                  actualIsListening ? "bg-green-500 animate-pulse" :
                  sessionActive ? "bg-blue-500" : "bg-gray-400"
                )} />
                <span className="text-sm text-muted-foreground">
                  {actualIsSpeaking ? "Mae is speaking..." :
                   actualIsListening ? "Mae is listening..." :
                   sessionActive ? "Mae is ready" : "Mae is offline"}
                </span>
              </div>
            </div>

            {/* Current Processing Status */}
            {processingStatus && (
              <motion.div
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                className="p-2 bg-blue-50 border border-blue-200 rounded-lg"
              >
                <div className="flex items-center gap-2">
                  <Loader2 className="h-3 w-3 animate-spin text-blue-600" />
                  <span className="text-xs text-blue-700">{processingStatus}</span>
                </div>
              </motion.div>
            )}
          </div>

          {/* Current Tool/Function */}
          {currentTool && (
            <Alert className="border-teal-200 bg-teal-50">
              <Activity className="h-4 w-4 text-teal-600" />
              <AlertDescription className="text-teal-700">
                Currently using: <strong>{currentTool.replace(/_/g, ' ')}</strong>
              </AlertDescription>
            </Alert>
          )}

          {/* Session Control Buttons */}
          {sessionActive ? (
            <div className="flex items-center justify-center gap-2 pt-2">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handlePauseSession}
                className={cn(
                  "flex items-center gap-1 px-3 py-1.5 rounded-lg text-xs font-medium transition-all",
                  isPaused 
                    ? "bg-green-100 text-green-700 hover:bg-green-200" 
                    : "bg-yellow-100 text-yellow-700 hover:bg-yellow-200"
                )}
              >
                {isPaused ? (
                  <>
                    <Play className="h-3 w-3" />
                    Resume
                  </>
                ) : (
                  <>
                    <Pause className="h-3 w-3" />
                    Pause
                  </>
                )}
              </motion.button>
              
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={handleEndSession}
                className="flex items-center gap-1 px-3 py-1.5 rounded-lg text-xs font-medium bg-red-100 text-red-700 hover:bg-red-200 transition-all"
              >
                <Square className="h-3 w-3" />
                End Session
              </motion.button>
            </div>
          ) : (
            <div className="flex items-center justify-center gap-2 pt-2">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => {
                  if (onStartSession) {
                    onStartSession();
                  }
                }}
                className="flex items-center gap-1 px-4 py-2 rounded-lg text-sm font-medium bg-primary text-primary-foreground hover:bg-primary/90 transition-all"
              >
                <Play className="h-4 w-4" />
                Start Session with Mae
              </motion.button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Activities */}
      {/* <Card className="healthcare-card border-l-4 border-l-blue-500">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center gap-2">
            <Activity className="h-4 w-4 text-primary" />
            Recent Activities
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 max-h-48 overflow-y-auto">
            <AnimatePresence>
              {activities.length > 0 ? activities.map((activity, index) => (
                <motion.div
                  key={activity.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-start gap-3 p-2 rounded-lg bg-muted/30"
                >
                  <div className={cn("mt-0.5", getActivityColor(activity.status))}>
                    {activity.status === 'active' ? (
                      <motion.div
                        animate={{ rotate: 360 }}
                        transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
                      >
                        {getActivityIcon(activity)}
                      </motion.div>
                    ) : (
                      getActivityIcon(activity)
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <span className="text-xs font-medium truncate">
                        {activity.name.replace(/_/g, ' ')}
                      </span>
                      <Badge variant="outline" className="text-xs">
                        {activity.type}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground mt-1">
                      {activity.description}
                    </p>
                    <span className="text-xs text-muted-foreground">
                      {new Date(activity.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                </motion.div>
              )) : (
                <div className="text-center py-4">
                  <p className="text-xs text-muted-foreground">No recent activities</p>
                </div>
              )}
            </AnimatePresence>
          </div>
        </CardContent>
      </Card> */}

      {/* How Mae Helps - Enhanced */}
      {/* <Card className="healthcare-card">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm flex items-center gap-2">
            <Heart className="h-4 w-4 text-primary" />
            How Mae Helps
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="grid grid-cols-1 gap-2">
              <motion.div 
                className="flex items-center gap-2 p-2 bg-green-50 border border-green-200 rounded-lg"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <Mic className="h-4 w-4 text-green-600" />
                <div>
                  <span className="text-sm font-medium text-green-600">Voice Guidance</span>
                  <p className="text-xs text-green-700">Natural conversation flow</p>
                </div>
              </motion.div>

              <motion.div 
                className="flex items-center gap-2 p-2 bg-blue-50 border border-blue-200 rounded-lg"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <Database className="h-4 w-4 text-blue-600" />
                <div>
                  <span className="text-sm font-medium text-blue-600">Smart Data</span>
                  <p className="text-xs text-blue-700">Secure information handling</p>
                </div>
              </motion.div>

              <motion.div 
                className="flex items-center gap-2 p-2 bg-purple-50 border border-purple-200 rounded-lg"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 400, damping: 10 }}
              >
                <Users className="h-4 w-4 text-purple-600" />
                <div>
                  <span className="text-sm font-medium text-purple-600">Family Setup</span>
                  <p className="text-xs text-purple-700">Easy member addition</p>
                </div>
              </motion.div>
            </div>

            <div className="mt-3 p-3 bg-gradient-to-r from-teal-50 to-blue-50 border border-teal-200 rounded-lg">
              <p className="text-xs text-teal-700">
                <strong>Tip:</strong> Mae responds to natural speech. Just say what you need 
                and she'll guide you through each step of the onboarding process.
              </p>
            </div>
          </div>
        </CardContent>
      </Card> */}


    </div>
  )
}