"use client"

import React, { useState, useEffect, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Card, 
  CardContent, 
  CardHeader, 
  CardTitle,
  CardDescription 
} from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Separator } from '@/components/ui/separator'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  Mic, 
  MicOff, 
  MessageSquare, 
  Sparkles, 
  Activity, 
  Zap, 
  Brain,
  Volume2,
  VolumeX,
  Settings,
  HelpCircle,
  CheckCircle2,
  AlertCircle,
  Loader2,
  Wand2,
  Users,
  User,
  Heart,
  ChevronRight
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { useGlobalMaeSession } from '@/components/global-mae-provider'

interface MaeControlPanelProps {
  className?: string
  currentStep?: string
  onStepChange?: (step: string) => void
  showAdvancedControls?: boolean
}

export function MaeControlPanel({ 
  className,
  currentStep = 'welcome',
  onStepChange,
  showAdvancedControls = false
}: MaeControlPanelProps) {
  const { sessionState, isSessionActive } = useGlobalMaeSession()
  const [isExpanded, setIsExpanded] = useState(false)
  const [maeStatus, setMaeStatus] = useState<'idle' | 'listening' | 'processing' | 'speaking'>('idle')
  const [voiceLevel, setVoiceLevel] = useState(0)
  const [lastInteraction, setLastInteraction] = useState<Date | null>(null)

  // Simulate voice level animation when Mae is listening
  useEffect(() => {
    let interval: NodeJS.Timeout
    if (sessionState?.isRecording) {
      setMaeStatus('listening')
      interval = setInterval(() => {
        setVoiceLevel(Math.random() * 100)
      }, 100)
    } else {
      setMaeStatus('idle')
      setVoiceLevel(0)
    }
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [sessionState?.isRecording])

  // Mae capabilities for current step
  const getMaeCapabilities = useCallback(() => {
    const baseCapabilities = [
      { icon: MessageSquare, label: 'Voice Guidance', active: true },
      { icon: Brain, label: 'Smart Assistance', active: true },
      { icon: Zap, label: 'Quick Actions', active: isSessionActive }
    ]

    switch (currentStep) {
      case 'welcome':
        return [
          ...baseCapabilities,
          { icon: Wand2, label: 'Setup Wizard', active: true },
          { icon: HelpCircle, label: 'Getting Started', active: true }
        ]
      case 'user_info':
        return [
          ...baseCapabilities,
          { icon: User, label: 'Profile Setup', active: true },
          { icon: CheckCircle2, label: 'Form Validation', active: true }
        ]
      case 'family_info':
        return [
          ...baseCapabilities,
          { icon: Users, label: 'Family Management', active: true },
          { icon: Heart, label: 'Child Profiles', active: true }
        ]
      default:
        return baseCapabilities
    }
  }, [currentStep, isSessionActive])

  const handleMaeToggle = () => {
    if (isSessionActive) {
      // Dispatch Mae disconnect event
      window.dispatchEvent(new CustomEvent('mae-disconnect', {
        detail: { reason: 'user_requested' }
      }))
    } else {
      // Dispatch Mae connect event
      window.dispatchEvent(new CustomEvent('mae-connect', {
        detail: { step: currentStep }
      }))
    }
  }

  const handleQuickAction = (action: string) => {
    setLastInteraction(new Date())
    
    // Dispatch AG-UI events based on action
    switch (action) {
      case 'help':
        window.dispatchEvent(new CustomEvent('mae-help-request', {
          detail: { step: currentStep, type: 'general' }
        }))
        break
      case 'skip':
        if (onStepChange) {
          const steps = ['welcome', 'user_info', 'family_info', 'complete']
          const currentIndex = steps.indexOf(currentStep)
          if (currentIndex < steps.length - 1) {
            onStepChange(steps[currentIndex + 1])
          }
        }
        break
      case 'voice_demo':
        window.dispatchEvent(new CustomEvent('mae-voice-demo', {
          detail: { step: currentStep }
        }))
        break
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={cn("space-y-4", className)}
    >
      {/* Main Mae Status Card */}
      <Card className={cn(
        "border-2 transition-all duration-300",
        isSessionActive 
          ? "border-primary/50 bg-gradient-to-br from-primary/5 to-primary/10 shadow-lg shadow-primary/10" 
          : "border-border/50 bg-card/50"
      )}>
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <motion.div
                animate={{ 
                  scale: isSessionActive ? [1, 1.1, 1] : 1,
                  rotate: isSessionActive ? [0, 5, -5, 0] : 0
                }}
                transition={{ 
                  duration: 2, 
                  repeat: isSessionActive ? Infinity : 0,
                  ease: "easeInOut"
                }}
                className={cn(
                  "w-12 h-12 rounded-full flex items-center justify-center transition-all",
                  isSessionActive 
                    ? "bg-gradient-to-br from-primary to-primary/70 shadow-lg shadow-primary/25" 
                    : "bg-muted"
                )}
              >
                <Sparkles className={cn(
                  "h-6 w-6 transition-colors",
                  isSessionActive ? "text-primary-foreground" : "text-muted-foreground"
                )} />
              </motion.div>
              <div>
                <CardTitle className="text-lg">Mae AI Assistant</CardTitle>
                <CardDescription className="flex items-center gap-2">
                  <div className={cn(
                    "w-2 h-2 rounded-full transition-colors",
                    isSessionActive ? "bg-green-500 animate-pulse" : "bg-gray-400"
                  )} />
                  {isSessionActive ? 'Connected & Ready' : 'Disconnected'}
                </CardDescription>
              </div>
            </div>
            <Button
              variant={isSessionActive ? "destructive" : "default"}
              size="sm"
              onClick={handleMaeToggle}
              className="transition-all duration-200"
            >
              {isSessionActive ? (
                <>
                  <MicOff className="h-4 w-4 mr-2" />
                  Disconnect
                </>
              ) : (
                <>
                  <Mic className="h-4 w-4 mr-2" />
                  Connect
                </>
              )}
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Voice Level Indicator */}
          <AnimatePresence>
            {sessionState?.isRecording && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="space-y-2"
              >
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Volume2 className="h-4 w-4" />
                  <span>Listening...</span>
                </div>
                <div className="flex items-center gap-1">
                  {Array.from({ length: 20 }, (_, i) => (
                    <motion.div
                      key={i}
                      className={cn(
                        "w-1 bg-primary rounded-full transition-all",
                        voiceLevel > (i * 5) ? "h-4 opacity-100" : "h-2 opacity-30"
                      )}
                      animate={{
                        height: voiceLevel > (i * 5) ? 16 : 8,
                        opacity: voiceLevel > (i * 5) ? 1 : 0.3
                      }}
                      transition={{ duration: 0.1 }}
                    />
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Mae Capabilities */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">Current Capabilities</span>
            </div>
            <div className="grid grid-cols-2 gap-2">
              {getMaeCapabilities().map((capability, index) => {
                const Icon = capability.icon
                return (
                  <motion.div
                    key={capability.label}
                    initial={{ opacity: 0, scale: 0.9 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: index * 0.1 }}
                    className={cn(
                      "flex items-center gap-2 p-2 rounded-lg border transition-all",
                      capability.active 
                        ? "bg-primary/10 border-primary/30 text-primary" 
                        : "bg-muted/50 border-border/50 text-muted-foreground"
                    )}
                  >
                    <Icon className="h-3 w-3" />
                    <span className="text-xs font-medium">{capability.label}</span>
                  </motion.div>
                )
              })}
            </div>
          </div>

          {/* Quick Actions */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-primary" />
              <span className="text-sm font-medium">Quick Actions</span>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickAction('help')}
                className="text-xs"
              >
                <HelpCircle className="h-3 w-3 mr-1" />
                Get Help
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => handleQuickAction('voice_demo')}
                className="text-xs"
                disabled={!isSessionActive}
              >
                <Mic className="h-3 w-3 mr-1" />
                Voice Demo
              </Button>
              {currentStep !== 'complete' && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleQuickAction('skip')}
                  className="text-xs"
                >
                  <CheckCircle2 className="h-3 w-3 mr-1" />
                  Skip Step
                </Button>
              )}
            </div>
          </div>

          {/* Last Interaction */}
          {lastInteraction && (
            <div className="text-xs text-muted-foreground">
              Last interaction: {lastInteraction.toLocaleTimeString()}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Advanced Controls (Expandable) */}
      {showAdvancedControls && (
        <Card className="border-border/50">
          <CardHeader 
            className="cursor-pointer"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <div className="flex items-center justify-between">
              <CardTitle className="text-base flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Advanced Controls
              </CardTitle>
              <motion.div
                animate={{ rotate: isExpanded ? 180 : 0 }}
                transition={{ duration: 0.2 }}
              >
                <ChevronRight className="h-4 w-4" />
              </motion.div>
            </div>
          </CardHeader>
          
          <AnimatePresence>
            {isExpanded && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.2 }}
              >
                <CardContent className="space-y-4">
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Advanced controls allow fine-tuning Mae's behavior during onboarding.
                    </AlertDescription>
                  </Alert>
                  
                  <div className="grid grid-cols-2 gap-2">
                    <Button variant="outline" size="sm" className="text-xs">
                      <Brain className="h-3 w-3 mr-1" />
                      Debug Mode
                    </Button>
                    <Button variant="outline" size="sm" className="text-xs">
                      <Activity className="h-3 w-3 mr-1" />
                      Session Logs
                    </Button>
                  </div>
                </CardContent>
              </motion.div>
            )}
          </AnimatePresence>
        </Card>
      )}
    </motion.div>
  )
}
