'use client';

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Wrapper } from '@googlemaps/react-wrapper';
import { useTheme } from 'next-themes';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Phone, MapPin, Star, Clock, Navigation } from 'lucide-react';
import { HealthcareProvider, LocationSearchResult } from '@/lib/location-function-tools';
import { useMapsApiKey } from '@/hooks/use-maps-api-key';

// Function to get custom provider icons
function getCustomProviderIcon(searchType: string, index: number, strokeColor: string) {
  const iconColor = '#00bba7';
  const textColor = 'white';
  
  let iconSvg = '';
  
  switch (searchType) {
    case 'pharmacies':
      iconSvg = `<circle cx="16" cy="16" r="12" fill="${iconColor}" stroke="${strokeColor}" stroke-width="2"/>
                 <circle cx="16" cy="10" r="3" fill="${textColor}"/>
                 <rect x="13" y="18" width="6" height="4" rx="2" fill="${textColor}"/>`;
      break;
    case 'hospitals':
      iconSvg = `<circle cx="16" cy="16" r="12" fill="${iconColor}" stroke="${strokeColor}" stroke-width="2"/>
                 <path d="M16 8v8M12 12h8" stroke="${textColor}" stroke-width="2" stroke-linecap="round"/>`;
      break;
    case 'urgent_care':
      iconSvg = `<circle cx="16" cy="16" r="12" fill="${iconColor}" stroke="${strokeColor}" stroke-width="2"/>
                 <path d="M16 8v8M12 12h8" stroke="${textColor}" stroke-width="1.5" stroke-linecap="round"/>
                 <circle cx="16" cy="16" r="3" fill="none" stroke="${textColor}" stroke-width="1"/>`;
      break;
    default: // pediatricians
      iconSvg = `<circle cx="16" cy="16" r="12" fill="${iconColor}" stroke="${strokeColor}" stroke-width="2"/>
                 <path d="M12 14c0-2.2 1.8-4 4-4s4 1.8 4 4" stroke="${textColor}" stroke-width="1.5" stroke-linecap="round"/>
                 <circle cx="16" cy="11" r="2" fill="${textColor}"/>`;
      break;
  }

  return 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
    <svg width="32" height="32" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
      ${iconSvg}
      <text x="16" y="26" text-anchor="middle" fill="white" font-size="10" font-weight="bold">${index + 1}</text>
    </svg>
  `);
}

// Google Maps component
function GoogleMap({
  center,
  zoom,
  providers,
  onProviderSelect
}: {
  center: { lat: number; lng: number };
  zoom: number;
  providers: HealthcareProvider[];
  onProviderSelect: (provider: HealthcareProvider) => void;
}) {
  const ref = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [markers, setMarkers] = useState<google.maps.Marker[]>([]);
  const { theme, resolvedTheme } = useTheme();

  useEffect(() => {
    if (ref.current && !map) {
      const isDark = resolvedTheme === 'dark';
      const newMap = new google.maps.Map(ref.current, {
        center,
        zoom,
        styles: [
          {
            featureType: 'poi.medical',
            elementType: 'geometry',
            stylers: [{ color: '#00bba7' }]
          },
          // Dark mode styles
          ...(isDark ? [
            {
              featureType: 'all',
              elementType: 'geometry',
              stylers: [{ color: '#1a1a1a' }]
            },
            {
              featureType: 'all',
              elementType: 'labels.text.stroke',
              stylers: [{ color: '#1a1a1a' }]
            },
            {
              featureType: 'all',
              elementType: 'labels.text.fill',
              stylers: [{ color: '#ffffff' }]
            },
            {
              featureType: 'water',
              elementType: 'geometry',
              stylers: [{ color: '#2c2c54' }]
            },
            {
              featureType: 'road',
              elementType: 'geometry',
              stylers: [{ color: '#2c2c2c' }]
            },
            {
              featureType: 'road',
              elementType: 'geometry.stroke',
              stylers: [{ color: '#383838' }]
            }
          ] : [])
        ]
      });
      setMap(newMap);
    }
  }, [center, zoom, map, resolvedTheme]);

  useEffect(() => {
    if (map && providers.length > 0) {
      // Clear existing markers
      markers.forEach(marker => marker.setMap(null));

      const isDark = resolvedTheme === 'dark';
      const strokeColor = isDark ? '#374151' : 'white';

      // Create new markers with custom icons
      const newMarkers = providers.map((provider, index) => {
        const marker = new google.maps.Marker({
          position: provider.coordinates,
          map,
          title: provider.name,
          icon: {
            url: getCustomProviderIcon('pediatricians', index, strokeColor) // Default to pediatricians for now
          }
        });

        marker.addListener('click', () => {
          onProviderSelect(provider);
        });

        return marker;
      });

      setMarkers(newMarkers);

      // Fit map to show all markers
      if (newMarkers.length > 0) {
        const bounds = new google.maps.LatLngBounds();
        providers.forEach(provider => {
          bounds.extend(provider.coordinates);
        });
        map.fitBounds(bounds);
      }
    }
  }, [map, providers, onProviderSelect, markers, resolvedTheme]);

  return <div ref={ref} style={{ width: '100%', height: '400px' }} />;
}

// State interface for healthcare search
interface HealthcareSearchState {
  searchQuery: string;
  searchType: 'pediatricians' | 'hospitals' | 'urgent_care' | 'pharmacies';
  results: HealthcareProvider[];
  selectedProvider: HealthcareProvider | null;
  userLocation: { lat: number; lng: number } | null;
  isLoading: boolean;
  error: string | null;
}

// Provider card component
function ProviderCard({ 
  provider, 
  index, 
  onSelect,
  onGetDirections 
}: { 
  provider: HealthcareProvider;
  index: number;
  onSelect: () => void;
  onGetDirections: () => void;
}) {
  return (
    <Card className="mb-4 cursor-pointer hover:shadow-md transition-shadow" onClick={onSelect}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <Badge variant="secondary" className="bg-primary text-primary-foreground">
              {index + 1}
            </Badge>
            <CardTitle className="text-lg">{provider.name}</CardTitle>
          </div>
          <div className="flex items-center gap-1">
            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
            <span className="text-sm font-medium">{provider.rating}</span>
            <span className="text-xs text-muted-foreground">({provider.reviews_count})</span>
          </div>
        </div>
        <CardDescription className="flex items-center gap-1">
          <MapPin className="h-4 w-4" />
          {provider.address} • {provider.distance} miles away
        </CardDescription>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="flex flex-wrap gap-2 mb-3">
          {provider.specializations?.map((spec, i) => (
            <Badge key={i} variant="outline" className="text-xs">{spec}</Badge>
          ))}
          {provider.pediatric_services?.map((service, i) => (
            <Badge key={i} variant="outline" className="text-xs bg-blue-50">{service}</Badge>
          ))}
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <Phone className="h-4 w-4" />
              {provider.phone}
            </div>
            {provider.hours && (
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                {Object.values(provider.hours)[0]}
              </div>
            )}
          </div>
          <Button 
            size="sm" 
            variant="outline"
            onClick={(e) => {
              e.stopPropagation();
              onGetDirections();
            }}
          >
            <Navigation className="h-4 w-4 mr-1" />
            Directions
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

// Main Healthcare Map component
export function HealthcareMap() {
  const { apiKey: mapsApiKey, loading: mapsLoading, error: mapsError } = useMapsApiKey();
  // Regular React state management
  const [state, setState] = useState<HealthcareSearchState>({
    searchQuery: '',
    searchType: 'pediatricians',
    results: [],
    selectedProvider: null,
    userLocation: null,
    isLoading: false,
    error: null
  });

  // Get user's current location
  const getCurrentLocation = useCallback(async () => {
    if (!navigator.geolocation) {
      setState(prev => ({
        ...prev,
        error: 'Geolocation is not supported by this browser.'
      }));
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Check if we're on HTTPS or localhost
      if (typeof window !== 'undefined' &&
          window.location.protocol !== 'https:' &&
          window.location.hostname !== 'localhost') {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Location access requires a secure connection (HTTPS).'
        }));
        return;
      }

      // Check permission status if available
      if ('permissions' in navigator) {
        try {
          const permission = await navigator.permissions.query({ name: 'geolocation' });
          if (permission.state === 'denied') {
            setState(prev => ({
              ...prev,
              isLoading: false,
              error: 'Location access is blocked. Please enable location permissions in your browser settings and refresh the page.'
            }));
            return;
          }
        } catch (permError) {
          console.log('Permission API not available, proceeding with geolocation request');
        }
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          const newLocation = {
            lat: position.coords.latitude,
            lng: position.coords.longitude
          };
          setState(prev => ({
            ...prev,
            userLocation: newLocation,
            isLoading: false,
            error: null
          }));
          console.log('✅ Got user location:', newLocation);
        },
        (error) => {
          console.error('❌ Error getting location:', error);
          let errorMessage = 'Could not get your location. ';

          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage += 'Please enable location permissions in your browser settings and refresh the page.';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage += 'Location information is unavailable. Please try again or enter your location manually.';
              break;
            case error.TIMEOUT:
              errorMessage += 'Location request timed out. Please try again.';
              break;
            default:
              errorMessage += 'An unknown error occurred. Please try again.';
              break;
          }

          setState(prev => ({
            ...prev,
            isLoading: false,
            error: errorMessage
          }));
        },
        {
          enableHighAccuracy: true,
          timeout: 15000, // Increased timeout
          maximumAge: 60000
        }
      );
    } catch (error) {
      console.error('Location access error:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Could not access location services. Please check permissions and try again.'
      }));
    }
  }, []);

  // Handle provider selection
  const handleProviderSelect = useCallback((provider: HealthcareProvider) => {
    setState(prev => ({
      ...prev,
      selectedProvider: provider
    }));
  }, []);

  // Handle getting directions
  const handleGetDirections = useCallback((provider: HealthcareProvider) => {
    const query = encodeURIComponent(provider.address);
    window.open(`https://www.google.com/maps/dir/?api=1&destination=${query}`, '_blank');
  }, []);

  // This function is now handled by the real Google Places API integration
  // Remove the demo search functionality

  // No demo search needed - real API integration only

  const mapCenter = state.userLocation || { lat: 40.7128, lng: -74.0060 };
  
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold text-primary">
          Find Healthcare Providers Near You
        </h2>
        <Button
          onClick={getCurrentLocation}
          variant="outline"
          disabled={state.isLoading}
        >
          <MapPin className="h-4 w-4 mr-2" />
          {state.isLoading ? 'Getting Location...' : 'Use My Location'}
        </Button>
      </div>

      {state.error && (
        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4 text-destructive">
          {state.error}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Map Section */}
        <div className="order-2 lg:order-1">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                Map View
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Wrapper apiKey={mapsApiKey || 'demo'}>
                <GoogleMap
                  center={mapCenter}
                  zoom={12}
                  providers={state.results}
                  onProviderSelect={handleProviderSelect}
                />
              </Wrapper>
            </CardContent>
          </Card>
        </div>

        {/* Results Section */}
        <div className="order-1 lg:order-2">
          <Card>
            <CardHeader>
              <CardTitle>
                {state.results.length > 0 
                  ? `${state.results.length} Provider${state.results.length > 1 ? 's' : ''} Found`
                  : 'Search Results'
                }
              </CardTitle>
              {state.searchQuery && (
                <CardDescription>
                  Showing {state.searchType.replace('_', ' ')} near {state.searchQuery}
                </CardDescription>
              )}
            </CardHeader>
            <CardContent className="max-h-96 overflow-y-auto">
              {state.isLoading ? (
                <div className="text-center py-8 text-muted-foreground">
                  Searching for providers...
                </div>
              ) : state.results.length > 0 ? (
                state.results.map((provider, index) => (
                  <ProviderCard
                    key={index}
                    provider={provider}
                    index={index}
                    onSelect={() => handleProviderSelect(provider)}
                    onGetDirections={() => handleGetDirections(provider)}
                  />
                ))
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  Ask Mae to find healthcare providers near you!
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Selected Provider Details */}
      {state.selectedProvider && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              Selected Provider: {state.selectedProvider.name}
              <Button
                size="sm"
                onClick={() => handleGetDirections(state.selectedProvider!)}
              >
                <Navigation className="h-4 w-4 mr-2" />
                Get Directions
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-medium mb-2">Contact Information</h4>
                <p className="text-sm text-muted-foreground mb-1">
                  📍 {state.selectedProvider.address}
                </p>
                <p className="text-sm text-muted-foreground mb-1">
                  📞 {state.selectedProvider.phone}
                </p>
                {state.selectedProvider.website && (
                  <p className="text-sm">
                    <a
                      href={state.selectedProvider.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-primary hover:underline"
                    >
                      Visit Website
                    </a>
                  </p>
                )}
              </div>
              
              <div>
                <h4 className="font-medium mb-2">Services & Hours</h4>
                {state.selectedProvider.pediatric_services && (
                  <div className="mb-2">
                    <p className="text-xs font-medium text-muted-foreground mb-1">Services:</p>
                    <div className="flex flex-wrap gap-1">
                      {state.selectedProvider.pediatric_services.map((service, i) => (
                        <Badge key={i} variant="secondary" className="text-xs">
                          {service}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
                
                {state.selectedProvider.hours && (
                  <div>
                    <p className="text-xs font-medium text-muted-foreground mb-1">Hours:</p>
                    {Object.entries(state.selectedProvider.hours).map(([day, hours]) => (
                      <p key={day} className="text-sm">
                        <span className="font-medium">{day}:</span> {hours}
                      </p>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default HealthcareMap;