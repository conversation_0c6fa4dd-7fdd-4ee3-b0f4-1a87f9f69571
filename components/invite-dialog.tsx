'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { UserPlus, X, Mail, MessageSquare, Gift } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface InviteDialogProps {
  children: React.ReactNode
}

export function InviteDialog({ children }: InviteDialogProps) {
  const [open, setOpen] = useState(false)
  const [emails, setEmails] = useState<string[]>([])
  const [currentEmail, setCurrentEmail] = useState('')
  const [personalMessage, setPersonalMessage] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const { toast } = useToast()

  const addEmail = () => {
    const email = currentEmail.trim()
    if (email && isValidEmail(email) && !emails.includes(email)) {
      setEmails([...emails, email])
      setCurrentEmail('')
    }
  }

  const removeEmail = (emailToRemove: string) => {
    setEmails(emails.filter(email => email !== emailToRemove))
  }

  const isValidEmail = (email: string) => {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault()
      addEmail()
    }
  }

  const sendInvites = async () => {
    if (emails.length === 0) {
      toast({
        title: "No emails to send",
        description: "Please add at least one email address.",
        variant: "destructive"
      })
      return
    }

    setIsLoading(true)
    try {
      const response = await fetch('/api/send-invites', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          emails,
          personalMessage
        })
      })

      if (!response.ok) {
        throw new Error('Failed to send invites')
      }

      toast({
        title: "Invites sent successfully! 🎉",
        description: `${emails.length} invite${emails.length > 1 ? 's' : ''} sent. Each friend gets 1 free month when they sign up!`
      })

      // Reset form
      setEmails([])
      setPersonalMessage('')
      setOpen(false)
    } catch (error) {
      toast({
        title: "Failed to send invites",
        description: "Please try again later.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {children}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Gift className="w-5 h-5 text-teal-500" />
            Invite Friends & Get Rewarded
          </DialogTitle>
          <DialogDescription>
            Share Our Kidz with friends and family. Every signup adds 1 free month to both your subscriptions!
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Benefits Section */}
          <div className="bg-gradient-to-r from-teal-50 to-blue-50 border border-teal-200 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-2">🎁 Referral Benefits</h4>
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• You get 1 free months added to your subscription</li>
              <li>• Your friend gets 1 free months when they sign up</li>
              <li>• No limit on how many friends you can invite</li>
            </ul>
          </div>

          {/* Email Input */}
          <div className="space-y-2">
            <Label htmlFor="email">Friend&apos;s Email Addresses</Label>
            <div className="flex gap-2">
              <Input
                id="email"
                type="email"
                placeholder="Enter email address"
                value={currentEmail}
                onChange={(e) => setCurrentEmail(e.target.value)}
                onKeyPress={handleKeyPress}
                className="flex-1"
              />
              <Button 
                onClick={addEmail} 
                variant="outline" 
                size="sm"
                disabled={!currentEmail.trim() || !isValidEmail(currentEmail.trim())}
              >
                Add
              </Button>
            </div>
            <p className="text-xs text-gray-500">Press Enter or comma to add multiple emails</p>
          </div>

          {/* Email Tags */}
          {emails.length > 0 && (
            <div className="space-y-2">
              <Label>Inviting ({emails.length})</Label>
              <div className="flex flex-wrap gap-2">
                {emails.map((email) => (
                  <Badge key={email} variant="secondary" className="flex items-center gap-1">
                    <Mail className="w-3 h-3" />
                    {email}
                    <button
                      onClick={() => removeEmail(email)}
                      className="ml-1 hover:bg-gray-200 rounded-full p-0.5"
                    >
                      <X className="w-3 h-3" />
                    </button>
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Personal Message */}
          <div className="space-y-2">
            <Label htmlFor="message">Personal Message (Optional)</Label>
            <Textarea
              id="message"
              placeholder="Add a personal note to your invitation..."
              value={personalMessage}
              onChange={(e) => setPersonalMessage(e.target.value)}
              rows={3}
              maxLength={500}
            />
            <p className="text-xs text-gray-500">{personalMessage.length}/500 characters</p>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button 
              onClick={sendInvites} 
              disabled={emails.length === 0 || isLoading}
              className="flex-1 bg-teal-500 hover:bg-teal-600"
            >
              {isLoading ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Sending...
                </>
              ) : (
                <>
                  <UserPlus className="w-4 h-4 mr-2" />
                  Send {emails.length} Invite{emails.length !== 1 ? 's' : ''}
                </>
              )}
            </Button>
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
