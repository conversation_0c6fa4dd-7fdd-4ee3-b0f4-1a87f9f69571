'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'

/**
 * Global Navigation Handler Component
 * 
 * This component handles <PERSON>'s navigation events globally across the app
 * to preserve voice sessions during page transitions.
 */
export function GlobalNavigationHandler() {
  const router = useRouter()

  useEffect(() => {
    console.log('🚀 Global Mae navigation handler initialized')

    const handleMaeNavigation = (event: CustomEvent) => {
      console.log('🚀 Mae navigation event received:', event.detail)
      
      const { message, preserveSession } = event.detail
      
      if (preserveSession) {
        console.log('🎤 Preserving voice session during navigation')
        
        // Store additional context for seamless continuation
        sessionStorage.setItem('mae-session-preservation', JSON.stringify({
          preservedAt: Date.now(),
          navigationReason: message || 'Onboarding navigation',
          preserveSession: true
        }))
        
        // No need for complex session transfer - just rely on event continuation
        
        // Use Next.js router for client-side navigation (no page reload)
        router.push('/onboarding')
        
        // Dispatch continuation event after navigation completes
        setTimeout(() => {
          console.log('🎤 Dispatching session continuation event')
          window.dispatchEvent(new CustomEvent('mae-continue-session', {
            detail: {
              preservedSession: true,
              navigationMessage: message
            }
          }))
        }, 100)
      } else {
        // Fallback to hard navigation if session preservation not requested
        window.location.href = '/onboarding'
      }
    }

    // Listen for Mae navigation events
    window.addEventListener('mae-navigate-to-onboarding', handleMaeNavigation as EventListener)
    
    // Set up global flag to indicate navigation handlers are active
    ;(window as any).__mae_navigation_handlers_active = true
    
    return () => {
      window.removeEventListener('mae-navigate-to-onboarding', handleMaeNavigation as EventListener)
      ;(window as any).__mae_navigation_handlers_active = false
    }
  }, [router])

  // This component doesn't render anything
  return null
}

/**
 * Enhanced Session Continuation Handler
 * 
 * Handles Mae session continuation events on the onboarding page
 */
export function MaeSessionContinuationHandler() {
  useEffect(() => {
    const handleSessionContinuation = (event: CustomEvent) => {
      console.log('🎤 Mae session continuation event received:', event.detail)
      
      const preservationData = sessionStorage.getItem('mae-session-preservation')
      const conversationContext = sessionStorage.getItem('mae-conversation-context')
      
      if (preservationData && conversationContext) {
        const preservation = JSON.parse(preservationData)
        const context = JSON.parse(conversationContext)
        
        console.log('📥 Restoring Mae session context:', { preservation, context })
        
        // Dispatch event to restart Mae with context
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('mae-restart-with-context', {
            detail: {
              preservedSession: true,
              context: context,
              preservation: preservation,
              message: context.message || 'Continuing our conversation about onboarding...'
            }
          }))
        }, 500)
        
        // Clean up session storage
        sessionStorage.removeItem('mae-session-preservation')
        sessionStorage.removeItem('mae-conversation-context')
      }
    }

    // Listen for session continuation events
    window.addEventListener('mae-continue-session', handleSessionContinuation as EventListener)
    
    return () => {
      window.removeEventListener('mae-continue-session', handleSessionContinuation as EventListener)
    }
  }, [])

  // This component doesn't render anything
  return null
}