"use client"

import React from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Calendar, Clock, Users, Video, MessageSquare } from 'lucide-react'
import { motion } from 'framer-motion'

interface FoundersHuddleProps {
  className?: string
}

export function FoundersHuddle({ className = '' }: FoundersHuddleProps) {
  const handleBookCall = () => {
    // Open calendar booking link
    window.open('https://cal.read.ai/our-kids/15-min', '_blank')
  }

  return (
    <Card className={`healthcare-card hover:border-2 hover:border-teal-500 transition-all duration-300 ${className}`}>
      <CardHeader className="text-center font-inter font-light">
        <div className="flex justify-center mb-4">
          <motion.div
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ duration: 0.2 }}
            className="w-16 h-16 bg-gradient-to-r from-teal-500 to-blue-500 rounded-full flex items-center justify-center shadow-lg"
          >
            <MessageSquare className="w-8 h-8 text-white" />
          </motion.div>
        </div>
        <CardTitle className="text-xl font-inter font-light bg-gradient-to-r from-teal-500 to-blue-500 text-transparent bg-clip-text">
          Founders 15-Minute Huddle
        </CardTitle>
        <CardDescription className="font-inter font-light">
          Connect directly with our founder to share feedback and shape the future of Our Kidz
        </CardDescription>
      </CardHeader>

      <CardContent className="text-center space-y-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 max-w-md mx-auto">
          <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
            <Clock className="w-4 h-4 text-teal-500" />
            <span className="font-inter font-light">15 minutes</span>
          </div>
          <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
            <Calendar className="w-4 h-4 text-teal-500" />
            <span className="font-inter font-light">Available daily</span>
          </div>
          <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
            <Video className="w-4 h-4 text-teal-500" />
            <span className="font-inter font-light">Video call</span>
          </div>
          <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
            <Users className="w-4 h-4 text-teal-500" />
            <span className="font-inter font-light">One-on-one</span>
          </div>
        </div>

        <div className="bg-gradient-to-r from-teal-50 to-blue-50 dark:from-teal-950/20 dark:to-blue-950/20 rounded-lg p-4 border border-teal-200 dark:border-teal-800">
          <p className="text-sm text-muted-foreground font-inter font-light mb-3">
            <strong className="text-foreground">What to expect:</strong>
          </p>
          <ul className="text-sm text-muted-foreground font-inter font-light space-y-1 text-left max-w-sm mx-auto">
            <li>• Share your parenting challenges and needs</li>
            <li>• Influence our product roadmap</li>
            <li>• Get early access to new features</li>
            <li>• Direct line to our founding team</li>
          </ul>
        </div>

        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Button
            onClick={handleBookCall}
            size="lg"
            className="bg-gradient-to-r from-teal-500 to-blue-500 hover:from-teal-600 hover:to-blue-600 text-white font-inter font-medium px-8"
          >
            <Calendar className="w-4 h-4 mr-2" />
            Book Your 15-Minute Call
          </Button>
        </motion.div>

        <Badge variant="outline" className="bg-gradient-to-r from-teal-500 to-blue-400 text-white border-0 font-inter font-medium">
          Limited spots available daily
        </Badge>
      </CardContent>
    </Card>
  )
}

export default FoundersHuddle
