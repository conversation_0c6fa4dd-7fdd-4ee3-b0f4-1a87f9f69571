const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

/**
 * Monitoring script for onboarding database statistics
 * Run with: node scripts/monitor-onboarding.js
 */

// Supabase client configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || `https://${process.env.SUPABASE_PROJECT_ID}.supabase.co`
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function monitorOnboarding() {
  try {
    console.log('📊 Onboarding Database Statistics')
    console.log('=' .repeat(50))

    // Monitor onboarding completion rates
    console.log('\n👥 User Statistics:')
    const { data: allUsers, error: userError } = await supabaseAdmin
      .from('users')
      .select('onboarding_completed')

    if (userError) {
      console.error('❌ Error fetching user stats:', userError)
    } else {
      const totalUsers = allUsers.length
      const completedUsers = allUsers.filter(user => user.onboarding_completed).length
      const completionRate = totalUsers > 0 ? ((completedUsers / totalUsers) * 100).toFixed(2) : 0

      console.log(`   Total Users: ${totalUsers}`)
      console.log(`   Completed Onboarding: ${completedUsers}`)
      console.log(`   Completion Rate: ${completionRate}%`)
    }

    // Monitor family member statistics
    console.log('\n👨‍👩‍👧‍👦 Family Statistics:')
    const { data: familyStats, error: familyError } = await supabaseAdmin
      .from('family_members')
      .select('user_id')

    if (familyError) {
      console.error('❌ Error fetching family stats:', familyError)
    } else {
      const uniqueUsers = new Set(familyStats.map(fm => fm.user_id)).size
      const totalMembers = familyStats.length
      const avgFamilySize = uniqueUsers > 0 ? (totalMembers / uniqueUsers).toFixed(2) : 0

      console.log(`   Users with Family Members: ${uniqueUsers}`)
      console.log(`   Total Family Members: ${totalMembers}`)
      console.log(`   Average Family Size: ${avgFamilySize}`)
    }

    // Monitor onboarding session activity
    console.log('\n📝 Session Activity:')
    const { data: sessionStats, error: sessionError } = await supabaseAdmin
      .from('onboarding_sessions')
      .select('current_step, completed_at')

    if (sessionError) {
      console.error('❌ Error fetching session stats:', sessionError)
    } else {
      const stepCounts = {}
      let completedSessions = 0

      sessionStats.forEach(session => {
        stepCounts[session.current_step] = (stepCounts[session.current_step] || 0) + 1
        if (session.completed_at) completedSessions++
      })

      console.log(`   Total Sessions: ${sessionStats.length}`)
      console.log(`   Completed Sessions: ${completedSessions}`)
      console.log('   Step Distribution:')
      Object.entries(stepCounts).forEach(([step, count]) => {
        console.log(`     ${step}: ${count}`)
      })
    }

    // Check for expired sessions
    console.log('\n⏰ Session Health:')
    const { data: expiredSessions, error: expiredError } = await supabaseAdmin
      .from('onboarding_sessions')
      .select('id')
      .lt('expires_at', new Date().toISOString())
      .is('completed_at', null)

    if (expiredError) {
      console.error('❌ Error checking expired sessions:', expiredError)
    } else {
      console.log(`   Expired Sessions: ${expiredSessions.length}`)
      if (expiredSessions.length > 0) {
        console.log('   💡 Run cleanup function to remove expired sessions')
      }
    }

    console.log('\n✅ Monitoring complete!')

  } catch (err) {
    console.error('❌ Unexpected error during monitoring:', err)
  }
}

// Run the monitoring
monitorOnboarding()
