#!/usr/bin/env node

/**
 * Database Relationship Fix Script
 * 
 * This script helps you fix the UUID relationships in your database
 * to ensure <PERSON> can properly access user context.
 */

const https = require('https');
const http = require('http');

// Configuration
const BASE_URL = process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000';

function makeRequest(path, method = 'GET', data = null) {
  return new Promise((resolve, reject) => {
    const url = new URL(path, BASE_URL);
    const isHttps = url.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const options = {
      hostname: url.hostname,
      port: url.port || (isHttps ? 443 : 80),
      path: url.pathname,
      method: method,
      headers: {
        'Content-Type': 'application/json',
      }
    };

    const req = client.request(options, (res) => {
      let body = '';
      res.on('data', (chunk) => body += chunk);
      res.on('end', () => {
        try {
          const parsed = JSON.parse(body);
          resolve({ status: res.statusCode, data: parsed });
        } catch (e) {
          resolve({ status: res.statusCode, data: body });
        }
      });
    });

    req.on('error', reject);
    
    if (data) {
      req.write(JSON.stringify(data));
    }
    
    req.end();
  });
}

async function checkStatus() {
  console.log('🔍 Checking current database relationship status...\n');
  
  try {
    const response = await makeRequest('/api/fix-user-relationships', 'GET');
    
    if (response.status === 200) {
      const status = response.data;
      
      console.log('📊 Current Status:');
      console.log(`   Sessions missing user_id: ${status.issues_found.sessions_missing_user_id}`);
      console.log(`   Users missing auth_id: ${status.issues_found.users_missing_auth_id}`);
      console.log(`   Orphaned family members: ${status.issues_found.orphaned_family_members}`);
      console.log(`   Total issues: ${status.total_issues}\n`);
      
      if (status.needs_fixing) {
        console.log('⚠️  Issues found that need fixing!');
        return true;
      } else {
        console.log('✅ All relationships are properly configured!');
        return false;
      }
    } else {
      console.error('❌ Failed to check status:', response.data);
      return false;
    }
  } catch (error) {
    console.error('❌ Error checking status:', error.message);
    return false;
  }
}

async function runFixes() {
  console.log('🔧 Running database relationship fixes...\n');
  
  try {
    const response = await makeRequest('/api/fix-user-relationships', 'POST');
    
    if (response.status === 200) {
      const report = response.data;
      
      console.log('📊 Fix Results:');
      console.log('\n🗣️  Audio Chat Sessions:');
      console.log(`   Total checked: ${report.audio_chat_sessions.total_checked}`);
      console.log(`   Updated: ${report.audio_chat_sessions.updated}`);
      console.log(`   Failed: ${report.audio_chat_sessions.failed}`);
      console.log(`   Status: ${report.audio_chat_sessions.status}`);
      
      console.log('\n👤 Users:');
      console.log(`   Missing auth links: ${report.users.missing_auth_user_id}`);
      console.log(`   Status: ${report.users.status}`);
      
      console.log('\n👨‍👩‍👧‍👦 Family Members:');
      console.log(`   Orphaned count: ${report.family_members.orphaned_count}`);
      console.log(`   Status: ${report.family_members.status}`);
      
      console.log('\n📋 Recommendations:');
      report.recommendations.forEach(rec => {
        console.log(`   ${rec}`);
      });
      
      return report;
    } else {
      console.error('❌ Failed to run fixes:', response.data);
      return null;
    }
  } catch (error) {
    console.error('❌ Error running fixes:', error.message);
    return null;
  }
}

async function testMaeContext(email) {
  console.log(`🤖 Testing Mae context access for: ${email}\n`);
  
  try {
    const response = await makeRequest('/api/mae-user-context', 'POST', {
      userEmail: email
    });
    
    if (response.status === 200) {
      const context = response.data;
      
      if (context.success && context.user_authenticated && context.user_in_database) {
        console.log('✅ Mae can access user context successfully!');
        console.log(`   User: ${context.user.name} (${context.user.email})`);
        console.log(`   Family members: ${context.family_members.length}`);
        console.log(`   Recent sessions: ${context.recent_sessions_count || 0}`);
        console.log(`   Onboarding completed: ${context.user.onboarding_completed ? 'Yes' : 'No'}`);
        return true;
      } else {
        console.log('⚠️  Mae cannot access user context:');
        console.log(`   User authenticated: ${context.user_authenticated}`);
        console.log(`   User in database: ${context.user_in_database}`);
        console.log(`   Message: ${context.message}`);
        return false;
      }
    } else {
      console.error('❌ Failed to test Mae context:', response.data);
      return false;
    }
  } catch (error) {
    console.error('❌ Error testing Mae context:', error.message);
    return false;
  }
}

async function main() {
  console.log('🚀 Database Relationship Fix Script\n');
  console.log('This script will help fix UUID relationships for Mae user context access.\n');
  
  const args = process.argv.slice(2);
  const command = args[0];
  
  switch (command) {
    case 'check':
      await checkStatus();
      break;
      
    case 'fix':
      const needsFix = await checkStatus();
      if (needsFix) {
        console.log('\n🔧 Proceeding with fixes...\n');
        await runFixes();
      }
      break;
      
    case 'test':
      const email = args[1];
      if (!email) {
        console.error('❌ Please provide an email address to test');
        console.log('Usage: node scripts/fix-database-relationships.<NAME_EMAIL>');
        process.exit(1);
      }
      await testMaeContext(email);
      break;
      
    case 'all':
      console.log('🔄 Running complete fix and test cycle...\n');
      
      // Step 1: Check status
      const hasIssues = await checkStatus();
      
      // Step 2: Fix if needed
      if (hasIssues) {
        console.log('\n🔧 Running fixes...\n');
        await runFixes();
      }
      
      // Step 3: Test with provided email
      const testEmail = args[1];
      if (testEmail) {
        console.log('\n🧪 Testing Mae context access...\n');
        await testMaeContext(testEmail);
      } else {
        console.log('\n✅ Fixes completed! Use "test" command with an email to verify Mae access.');
      }
      break;
      
    default:
      console.log('Usage:');
      console.log('  node scripts/fix-database-relationships.js check');
      console.log('  node scripts/fix-database-relationships.js fix');
      console.log('  node scripts/fix-database-relationships.<NAME_EMAIL>');
      console.log('  node scripts/fix-database-relationships.js all [<EMAIL>]');
      console.log('');
      console.log('Commands:');
      console.log('  check  - Check current database relationship status');
      console.log('  fix    - Fix database relationships');
      console.log('  test   - Test Mae context access for a specific user');
      console.log('  all    - Run check, fix, and test in sequence');
      break;
  }
}

// Run the script
main().catch(error => {
  console.error('❌ Script failed:', error);
  process.exit(1);
});
