#!/bin/bash

echo "🔍 Verifying Clerk DNS Configuration for our-kidz.com"
echo "======================================================="
echo ""

# Color codes
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to check DNS record
check_dns() {
    local subdomain=$1
    local expected=$2
    local full_domain="${subdomain}.our-kidz.com"
    
    echo -n "Checking ${full_domain}... "
    
    # Use dig to check CNAME record
    result=$(dig +short CNAME ${full_domain} 2>/dev/null)
    
    if [ -z "$result" ]; then
        # Try without CNAME flag for A records
        result=$(dig +short ${full_domain} 2>/dev/null)
    fi
    
    if [ -z "$result" ]; then
        echo -e "${RED}✗ NOT FOUND${NC}"
        echo "  Expected: ${expected}"
        return 1
    else
        if [[ "$result" == *"$expected"* ]]; then
            echo -e "${GREEN}✓ CONFIGURED${NC}"
            echo "  Points to: ${result}"
        else
            echo -e "${YELLOW}⚠ MISCONFIGURED${NC}"
            echo "  Expected: ${expected}"
            echo "  Found: ${result}"
            return 1
        fi
    fi
    echo ""
}

echo "1. Frontend API (Required for authentication)"
echo "----------------------------------------------"
check_dns "clerk" "frontend-api.clerk.services"

echo "2. Account Portal (Optional)"
echo "----------------------------"
check_dns "accounts" "accounts.clerk.services"

echo "3. Email Configuration (Optional but recommended)"
echo "-------------------------------------------------"
check_dns "clkmail" "mail.j2b44sewwn0v.clerk.services"
check_dns "clk._domainkey" "dkim1.j2b44sewwn0v.clerk.services"
check_dns "clk2._domainkey" "dkim2.j2b44sewwn0v.clerk.services"

echo ""
echo "📝 DNS Configuration Instructions:"
echo "=================================="
echo ""
echo "If any records are missing or misconfigured, add these CNAME records in your DNS provider:"
echo ""
echo "1. Frontend API (REQUIRED):"
echo "   - Name: clerk"
echo "   - Type: CNAME"
echo "   - Value: frontend-api.clerk.services"
echo ""
echo "2. Account Portal (Optional):"
echo "   - Name: accounts"
echo "   - Type: CNAME"
echo "   - Value: accounts.clerk.services"
echo ""
echo "3. Email - Main (Optional):"
echo "   - Name: clkmail"
echo "   - Type: CNAME"
echo "   - Value: mail.j2b44sewwn0v.clerk.services"
echo ""
echo "4. Email - DKIM 1 (Optional):"
echo "   - Name: clk._domainkey"
echo "   - Type: CNAME"
echo "   - Value: dkim1.j2b44sewwn0v.clerk.services"
echo ""
echo "5. Email - DKIM 2 (Optional):"
echo "   - Name: clk2._domainkey"
echo "   - Type: CNAME"
echo "   - Value: dkim2.j2b44sewwn0v.clerk.services"
echo ""
echo "⏱️  Note: DNS propagation can take up to 48 hours, but usually completes within 1-2 hours."
echo ""

# Test if clerk.our-kidz.com is accessible
echo "🌐 Testing clerk.our-kidz.com accessibility..."
echo "----------------------------------------------"
if curl -s -o /dev/null -w "%{http_code}" https://clerk.our-kidz.com | grep -q "200\|301\|302"; then
    echo -e "${GREEN}✓ clerk.our-kidz.com is accessible${NC}"
else
    echo -e "${YELLOW}⚠ clerk.our-kidz.com may not be fully configured yet${NC}"
    echo "  This is normal if you just added the DNS records."
fi

echo ""
echo "✅ Verification complete!"