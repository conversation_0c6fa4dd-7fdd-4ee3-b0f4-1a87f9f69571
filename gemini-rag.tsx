import React, { useState, useEffect, useRef } from 'react';
import {
    LineChart, Line, BarChart, Bar, PieChart, Pie, AreaChart, Area,
    XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer,
    Cell, Sector, RadarChart, Radar, PolarGrid, PolarAngleAxis,
    PolarRadiusAxis, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>er, ZAxis
} from 'recharts';
import { PieSectorDataItem } from 'recharts/types/polar/Pie';
import { ActiveShape } from 'recharts/types/util/types';
import ReactMarkdown from 'react-markdown';

// Our Kidz color palette based on the brand
const COLORS = {
    primary: '#8E44AD', // Purple
    secondary: '#F1C40F', // Yellow
    tertiary: '#1ABC9C', // Teal
    accent1: '#E74C3C', // Red
    accent2: '#F39C12', // Orange
    success: '#2ECC71',
    warning: '#F39C12',
    danger: '#E74C3C',
    light: '#F2F2F2',
    dark: '#111827',
    darkAlt: '#1E293B',
    text: '#FFFFFF',
    textMuted: '#94A3B8',
};

// Gradient definitions
const primaryGradient = (id: string | undefined) => (
    <linearGradient id={id} x1="0" y1="0" x2="0" y2="1">
        <stop offset="5%" stopColor={COLORS.primary} stopOpacity={0.8} />
        <stop offset="95%" stopColor={COLORS.primary} stopOpacity={0.2} />
    </linearGradient>
);

const secondaryGradient = (id: string | undefined) => (
    <linearGradient id={id} x1="0" y1="0" x2="0" y2="1">
        <stop offset="5%" stopColor={COLORS.secondary} stopOpacity={0.8} />
        <stop offset="95%" stopColor={COLORS.secondary} stopOpacity={0.2} />
    </linearGradient>
);

const ourKidzGradient = (id: string | undefined) => (
    <linearGradient id={id} x1="0" y1="0" x2="1" y2="1">
        <stop offset="0%" stopColor={COLORS.secondary} />
        <stop offset="25%" stopColor={COLORS.tertiary} />
        <stop offset="50%" stopColor={COLORS.accent1} />
        <stop offset="75%" stopColor={COLORS.accent2} />
        <stop offset="100%" stopColor={COLORS.primary} />
    </linearGradient>
);

// Mockup data
const workforceGapData = [
    { year: 2025, needed: 14000, projected: 2800 * 1, deficit: 14000 - (2800 * 1) },
    { year: 2026, needed: 14400, projected: 2800 * 2, deficit: 14400 - (2800 * 2) },
    { year: 2027, needed: 14800, projected: 2800 * 3, deficit: 14800 - (2800 * 3) },
    { year: 2028, needed: 15200, projected: 2800 * 4, deficit: 15200 - (2800 * 4) },
    { year: 2029, needed: 15600, projected: 2800 * 5, deficit: 15600 - (2800 * 5) },
    { year: 2030, needed: 16000, projected: 2800 * 6, deficit: 16000 - (2800 * 6) },
];

const marketSegmentData = [
    { name: 'Telehealth', value: 1200, growth: 28, future: 1536 },
    { name: 'Digital Tools', value: 900, growth: 22, future: 1098 },
    { name: 'Remote Monitoring', value: 700, growth: 35, future: 945 },
    { name: 'AI Diagnostics', value: 500, growth: 42, future: 710 },
    { name: 'Parent Support', value: 800, growth: 20, future: 960 },
];

const businessModelData = [
    { name: 'SaaS Subscriptions', value: 2.1, fill: COLORS.primary, value_prop: 'Premium parent experience' },
    { name: 'Targeted Advertising', value: 1.3, fill: COLORS.primary, value_prop: 'Targeted parent demographics' },
    { name: 'Pediatrician Ads', value: 0.9, fill: COLORS.primary, value_prop: 'Provider marketing' },
    { name: 'Healthcare Data', value: 0.7, fill: COLORS.primary, value_prop: 'Research insights' },
    { name: 'Total Revenue', value: 5.0, fill: COLORS.secondary, value_prop: 'Combined revenue streams' },
];

const funnelData = [
    { name: 'Awareness', value: 100, strategy: 'Social Media & SEO' },
    { name: 'Consideration', value: 40, strategy: 'Content Marketing & Community' },
    { name: 'Signup', value: 15, strategy: 'Personalized Onboarding' },
    { name: 'Active Users', value: 10, strategy: 'Targeted Engagement' },
    { name: 'Paying Subscribers', value: 5, strategy: 'Premium Features' },
];

const teamData = [
    { name: 'Steven Reinhart', role: 'Founder & Strategic Lead', experience: 15, expertise: [{ name: 'Healthcare Tech', value: 80 }, { name: 'Product Strategy', value: 85 }, { name: 'Business Development', value: 80 }] },
    { name: 'Chadwick Jones', role: 'CTO & Cofounder', experience: 18, expertise: [{ name: 'Healthcare Tech', value: 90 }, { name: 'AI & ML', value: 80 }, { name: 'Medical & Data Science', value: 85 }] },
    { name: 'Dr. Christina Johns', role: 'Chief Mom Advisor', experience: 20, expertise: [{ name: 'Pediatric Emergency', value: 85 }, { name: 'Medical Education', value: 85 }, { name: 'Parent Communication', value: 95 }] },
    { name: 'Advisors', role: 'Clinical & Business Experts', experience: 25, expertise: [{ name: 'Medical Research', value: 95 }, { name: 'Healthcare Policy', value: 85 }, { name: 'Medical Guidelines', value: 90 }] },
];

// Reinstated specific tooltip components to fix the TypeError.
const WorkforceGapTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
        return (
            <div className="bg-gray-800 p-3 border border-gray-700 rounded shadow-md text-xs text-white">
                <p className="font-bold text-sm text-purple-300">{`Year: ${label}`}</p>
                <p className="text-xs text-teal-300">{`Pediatricians Needed: ${payload[0]?.value?.toLocaleString()}`}</p>
                <p className="text-xs text-yellow-300">{`Projected Supply: ${payload[1]?.value?.toLocaleString()}`}</p>
                {payload[2] && <p className="text-xs text-red-300 font-semibold">{`Deficit: ${payload[2]?.value?.toLocaleString()}`}</p>}
            </div>
        );
    }
    return null;
};

const MarketSegmentTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
        const data = payload[0].payload;
        return (
            <div className="bg-gray-800 p-3 border border-gray-700 rounded shadow-md text-white">
                <p className="font-bold text-purple-300">{data.name}</p>
                <p className="text-xs">{`Current Market: $${data.value}M`}</p>
                <p className="text-xs">{`Growth Rate: ${data.growth}%`}</p>
            </div>
        );
    }
    return null;
};


// Render active shape for pie chart
const renderActiveShape = (props: { cx: any; cy: any; midAngle: any; innerRadius: any; outerRadius: any; startAngle: any; endAngle: any; fill: any; payload: any; percent: any; value: any; }) => {
    const { cx, cy, midAngle, innerRadius, outerRadius, startAngle, endAngle, fill, payload, percent, value } = props;
    const sin = Math.sin(-midAngle * Math.PI / 180);
    const cos = Math.cos(-midAngle * Math.PI / 180);
    const sx = cx + (outerRadius + 10) * cos;
    const sy = cy + (outerRadius + 10) * sin;
    const mx = cx + (outerRadius + 30) * cos;
    const my = cy + (outerRadius + 30) * sin;
    const ex = mx + (cos >= 0 ? 1 : -1) * 22;
    const ey = my;
    const textAnchor = cos >= 0 ? 'start' : 'end';

    return (
        <g>
            <text x={cx} y={cy} dy={8} textAnchor="middle" fill={fill} fontSize={14}>{payload.name}</text>
            <Sector cx={cx} cy={cy} innerRadius={innerRadius} outerRadius={outerRadius} startAngle={startAngle} endAngle={endAngle} fill={fill} />
            <Sector cx={cx} cy={cy} startAngle={startAngle} endAngle={endAngle} innerRadius={outerRadius + 6} outerRadius={outerRadius + 10} fill={fill} />
            <path d={`M${sx},${sy}L${mx},${my}L${ex},${ey}`} stroke={fill} fill="none" />
            <circle cx={ex} cy={ey} r={2} fill={fill} stroke="none" />
            <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} textAnchor={textAnchor} fill="#FFFFFF" fontSize={12}>{`${value}M`}</text>
            <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} dy={18} textAnchor={textAnchor} fill="#94A3B8" fontSize={10}>
                {`(${(percent * 100).toFixed(0)}%)`}
            </text>
        </g>
    );
};

// RAG Component
const RAGComponent = () => {
    const [projectId, setProjectId] = useState('moms-kidz');
    const [corpusId, setCorpusId] = useState('1290281293241647104');
    const [location, setLocation] = useState('us-central1');
    const [bearerToken, setBearerToken] = useState('');
    const [query, setQuery] = useState('What is the pediatric workforce gap?');
    const [response, setResponse] = useState('');
    const [groundingChunks, setGroundingChunks] = useState<any[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const queryRag = async () => {
        const trimmedCorpusId = corpusId.trim();

        if (!projectId || !trimmedCorpusId || !location || !bearerToken) {
            setError("Please fill in all configuration details, including a valid RAG Corpus ID.");
            return;
        }
        setIsLoading(true);
        setError(null);
        setResponse('');
        setGroundingChunks([]);

        const MODEL = "gemini-2.0-flash-exp";
        const API_ENDPOINT = `https://${location}-aiplatform.googleapis.com/v1beta1/projects/${projectId}/locations/${location}/publishers/google/models/${MODEL}:generateContent`;

        const requestBody = {
            "contents": [{
                "role": "user",
                "parts": [{ "text": query }]
            }],
            "tools": [{
                "retrieval": {
                    "vertex_rag_store": {
                        "rag_resources": [{
                            "rag_corpus": `projects/${projectId}/locations/${location}/ragCorpora/${trimmedCorpusId}`
                        }]
                    }
                }
            }]
        };

        try {
            const res = await fetch(API_ENDPOINT, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${bearerToken}`
                },
                body: JSON.stringify(requestBody)
            });

            const data = await res.json();

            if (!res.ok) {
                let errorMessage = `Request failed with status ${res.status}.`;
                if (data?.error?.message) {
                    errorMessage = data.error.message;
                    if (res.status === 401 || res.status === 403) {
                       errorMessage += " Please check your Bearer Token and ensure it has the 'Vertex AI User' role.";
                    }
                    if (errorMessage.includes("Rag Corpus not exist") || errorMessage.includes("Invalid rag corpus ID")) {
                        errorMessage += " Please verify your RAG Corpus ID is correct.";
                    }
                }
                throw new Error(errorMessage);
            }
            
            const responseText = data.candidates?.[0]?.content?.parts?.[0]?.text || "";
            setResponse(responseText);

            const groundingMetadata = data.candidates?.[0]?.groundingMetadata;
            if (groundingMetadata?.groundingChunks) {
                setGroundingChunks(groundingMetadata.groundingChunks);
            }

        } catch (e: any) {
            console.error("RAG Query Error:", e);
            setError(e.message || "An unknown network error occurred. Check the browser console for details.");
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <div className="col-span-12 bg-gray-800 p-6 rounded-lg shadow border border-gray-700">
            <h2 className="text-xl font-bold mb-6 text-white">Vertex AI RAG Integration</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h3 className="text-lg font-semibold text-purple-300 mb-4">Configuration</h3>
                    <div className="space-y-4">
                        <div>
                            <label className="text-sm font-medium text-gray-300 block mb-1">Project ID</label>
                            <input type="text" value={projectId} onChange={e => setProjectId(e.target.value)} className="w-full bg-gray-700 border border-gray-600 rounded p-2 text-sm text-white" />
                        </div>
                        <div>
                            <label className="text-sm font-medium text-gray-300 block mb-1">RAG Corpus ID</label>
                            <input type="text" value={corpusId} onChange={e => setCorpusId(e.target.value)} className="w-full bg-gray-700 border border-gray-600 rounded p-2 text-sm text-white" />
                             <p className="text-xs text-gray-400 mt-1">This must be the numerical ID of your RAG Corpus from the Vertex AI UI.</p>
                        </div>
                        <div>
                            <label className="text-sm font-medium text-gray-300 block mb-1">Location</label>
                            <input type="text" value={location} onChange={e => setLocation(e.target.value)} className="w-full bg-gray-700 border border-gray-600 rounded p-2 text-sm text-white" />
                        </div>
                        <div>
                            <label className="text-sm font-medium text-gray-300 block mb-1">Bearer Token</label>
                            <input type="password" value={bearerToken} onChange={e => setBearerToken(e.target.value)} placeholder="Enter your gcloud auth print-access-token" className="w-full bg-gray-700 border border-gray-600 rounded p-2 text-sm text-white" />
                             <p className="text-xs text-gray-400 mt-1">Get this by running `gcloud auth print-access-token` in your terminal.</p>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 className="text-lg font-semibold text-purple-300 mb-4">Query</h3>
                    <div className="space-y-4">
                        <div>
                            <textarea
                                value={query}
                                onChange={e => setQuery(e.target.value)}
                                className="w-full bg-gray-700 border border-gray-600 rounded p-2 text-sm text-white"
                                rows={4}
                                placeholder="Enter your query here"
                            />
                        </div>
                        <button onClick={queryRag} disabled={isLoading} className="w-full bg-gradient-to-r from-purple-600 to-pink-500 text-white font-bold py-2 px-4 rounded-lg hover:from-purple-700 hover:to-pink-600 disabled:opacity-50 disabled:cursor-not-allowed">
                            {isLoading ? 'Querying...' : 'Send Query'}
                        </button>
                    </div>
                     {error && <div className="mt-4 p-3 bg-red-900 border border-red-700 text-white rounded-lg text-sm">{error}</div>}
                </div>
            </div>

            <div className="mt-8">
                <h3 className="text-lg font-semibold text-purple-300 mb-4">Results</h3>
                 {isLoading && <div className="text-center text-gray-400">Loading response...</div>}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                   <div className="bg-gray-700 p-4 rounded-lg">
                         <h4 className="font-bold text-white mb-2">Generated Response</h4>
                         <div className="text-sm text-gray-300 prose prose-invert max-w-none"><ReactMarkdown>{response || "Awaiting response..."}</ReactMarkdown></div>
                    </div>
                    <div className="bg-gray-700 p-4 rounded-lg">
                        <h4 className="font-bold text-white mb-2">Grounding Information</h4>
                         {groundingChunks.length > 0 ? (
                            <div className="space-y-3 max-h-64 overflow-y-auto">
                                {groundingChunks.map((chunk, index) => (
                                    chunk && chunk.retrieval && (
                                        <div key={index} className="bg-gray-800 p-3 rounded-md text-xs">
                                            <p className="text-gray-300">{chunk.retrieval.content}</p>
                                            <p className="text-purple-400 mt-1 text-right">Source: {chunk.retrieval.source}</p>
                                        </div>
                                    )
                                ))}
                            </div>
                        ) : (
                            <p className="text-sm text-gray-400">No grounding information available.</p>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
};

// FIX: Changed LiveAssistant from a tab to a Floating Action Button component
const LiveAssistantComponent = ({ dashboardContext }: { dashboardContext: string }) => {
    const [bearerToken, setBearerToken] = useState('');
    const [status, setStatus] = useState('idle'); // idle, connecting, listening, processing, speaking
    const [transcript, setTranscript] = useState('');
    const [error, setError] = useState<string | null>(null);
    const [isChatOpen, setIsChatOpen] = useState(false);
    const ws = useRef<WebSocket | null>(null);
    const mediaRecorder = useRef<MediaRecorder | null>(null);
    const audioQueue = useRef<ArrayBuffer[]>([]);
    const isPlaying = useRef(false);
    const audioContext = useRef<AudioContext | null>(null);

    const toggleConversation = () => {
        if (status === 'idle') {
            startConversation();
        } else {
            stopConversation();
        }
    };
    
    const startConversation = async () => {
        if (!bearerToken) {
            setError("Please provide a Bearer Token to start the assistant.");
            return;
        }
        
        setError(null);
        setStatus('connecting');
        setTranscript('');

        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            mediaRecorder.current = new MediaRecorder(stream, { mimeType: 'audio/webm' });
            
            mediaRecorder.current.ondataavailable = (event) => {
                if(event.data.size > 0 && ws.current?.readyState === WebSocket.OPEN) {
                    const message = {"client_content": {"audio_chunk": event.data}};
                    ws.current?.send(event.data);
                }
            };
            
            setupWebSocket();
        } catch (err) {
            console.error('Error accessing microphone:', err);
            setError('Could not access the microphone. Please check permissions.');
            setStatus('idle');
        }
    };

    const stopConversation = () => {
        if (mediaRecorder.current && mediaRecorder.current.state === 'recording') {
            mediaRecorder.current.stop();
        }
        if (ws.current) {
            ws.current.close();
        }
        setStatus('idle');
    };

    const setupWebSocket = () => {
        const PROJECT_ID = "moms-kidz";
        const LOCATION = "us-central1";
        const MODEL = "gemini-2.0-flash-exp"; 
        
        const HOST = `${LOCATION}-aiplatform.googleapis.com`;
        const SERVICE_URL = `wss://${HOST}/ws/google.cloud.aiplatform.v1beta1.LlmBidiService/BidiGenerateContent?authorization=Bearer%20${bearerToken}`;

        ws.current = new WebSocket(SERVICE_URL);
        ws.current.binaryType = "arraybuffer";
        
        ws.current.onopen = () => {
            // Send setup and context message
            const setupPayload = {
                "setup": {
                    "model": `projects/${PROJECT_ID}/locations/${LOCATION}/publishers/google/models/${MODEL}`,
                    "generation_config": { "response_modalities": ["AUDIO"] },
                    "speech_config": { "language_code": "en-US" },
                    "turns": [
                        {
                            "role": "user",
                            "parts": [
                                { "text": `You are a helpful AI assistant for the 'Our Kidz' dashboard. Answer questions based on the following data context. Do not mention this context prompt in your response. Dashboard data: ${dashboardContext}` }
                            ]
                        }
                    ]
                }
            };
            ws.current?.send(JSON.stringify(setupPayload));
            setStatus('listening');
            mediaRecorder.current?.start(500);
        };

        ws.current.onmessage = async (event) => {
            const response = JSON.parse(new TextDecoder().decode(event.data));

            if (response.error) {
                setError(`API Error: ${response.error.message}`);
                stopConversation();
                return;
            }
            
            const serverContent = response.serverContent;
            if (serverContent?.modelTurn?.parts) {
                const part = serverContent.modelTurn.parts[0];
                if(part.text) {
                     setTranscript(prev => prev + part.text + " ");
                }
                if (part.audio) {
                    const audioData = atob(part.audio);
                    const audioBytes = new Uint8Array(audioData.length);
                    for (let i = 0; i < audioData.length; i++) {
                        audioBytes[i] = audioData.charCodeAt(i);
                    }
                    audioQueue.current.push(audioBytes.buffer);
                    if (!isPlaying.current) {
                        playNextInQueue();
                    }
                }
            }
        };

        ws.current.onclose = () => setStatus('idle');
        ws.current.onerror = (err) => {
            console.error("WebSocket Error:", err);
            setError("WebSocket connection failed. Check your Bearer token and network.");
            setStatus('idle');
        };
    };

    const playNextInQueue = async () => {
        if (audioQueue.current.length === 0) {
            isPlaying.current = false;
            return;
        }

        isPlaying.current = true;
        setStatus('speaking');
        
        if (!audioContext.current) {
            audioContext.current = new (window.AudioContext || (window as any).webkitAudioContext)();
        }
        
        const audioBuffer = audioQueue.current.shift();
        if (audioBuffer) {
            try {
                const decodedAudio = await audioContext.current.decodeAudioData(audioBuffer);
                const source = audioContext.current.createBufferSource();
                source.buffer = decodedAudio;
                source.connect(audioContext.current.destination);
                source.onended = playNextInQueue;
                source.start();
            } catch (e) {
                console.error("Error decoding audio data", e);
                isPlaying.current = false;
                playNextInQueue();
            }
        }
    };
    
    return (
        <div className="fixed bottom-5 right-5 z-50">
            {isChatOpen && (
                <div className="w-96 h-auto bg-gray-800 rounded-lg shadow-2xl p-4 mb-4 border border-gray-600">
                    <h3 className="text-lg font-bold text-white mb-2">Live Assistant</h3>
                     <div className="mb-4">
                        <label className="text-xs font-medium text-gray-300 block mb-1">Bearer Token</label>
                        <input type="password" value={bearerToken} onChange={e => setBearerToken(e.target.value)} placeholder="gcloud auth print-access-token" className="w-full bg-gray-900 border border-gray-600 rounded p-2 text-xs text-white" disabled={status !== 'idle'}/>
                    </div>
                     <div className="bg-gray-900 p-3 rounded-md min-h-[120px] max-h-48 overflow-y-auto">
                        <p className="text-sm text-gray-200">{transcript || "Transcript..."}</p>
                    </div>
                     <p className="text-center text-sm text-purple-300 mt-2 capitalize">{status}</p>
                     {error && <p className="mt-2 text-red-400 text-xs text-center">{error}</p>}
                </div>
            )}
            <button onClick={() => setIsChatOpen(!isChatOpen)} className="w-16 h-16 rounded-full flex items-center justify-center bg-teal-500 hover:bg-teal-600 text-white shadow-lg transition-transform hover:scale-110">
                 <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" /></svg>
            </button>
             {isChatOpen && (
                 <button onClick={toggleConversation} className={`absolute -top-4 -right-4 w-20 h-20 rounded-full flex items-center justify-center transition-all duration-300 shadow-xl border-4 border-gray-800 ${status === 'listening' || status === 'speaking' ? 'bg-red-500 animate-pulse' : 'bg-purple-600 hover:bg-purple-700'}`}>
                    <svg className="w-10 h-10 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M7 4a3 3 0 016 0v6a3 3 0 11-6 0V4zm5 10.73V16h-4v-1.27A6.974 6.974 0 012 8V7h2v1a5 5 0 0010 0V7h2v1a6.974 6.974 0 01-5 5.73z" clipRule="evenodd" />
                    </svg>
                </button>
             )}
        </div>
    )
}

// Main Dashboard Component
const PediatricDashboard = () => {
    const [activeTabIndex, setActiveTabIndex] = useState(0);
    const [activePieIndex, setActivePieIndex] = useState(0);
    const [activeTeamMember, setActiveTeamMember] = useState(0);
    const [yearFilter, setYearFilter] = useState(2030);
    const [isReportModalOpen, setIsReportModalOpen] = useState(false);
    const [reportPrompt, setReportPrompt] = useState('Summarize the key findings and opportunities from the dashboard data.');
    const [generatedReport, setGeneratedReport] = useState('');
    const [isGenerating, setIsGenerating] = useState(false);
    const [suggestedStrategies, setSuggestedStrategies] = useState('');
    const [isSuggesting, setIsSuggesting] = useState(false);

    const filteredWorkforceData = workforceGapData.filter(item => item.year <= yearFilter);
    
    const dashboardContextForAI = `
        Workforce Gap Data: ${JSON.stringify(workforceGapData)}.
        Market Segment Data: ${JSON.stringify(marketSegmentData)}.
        Business Model Data: ${JSON.stringify(businessModelData)}.
        Funnel Data: ${JSON.stringify(funnelData)}.
        Team Data: ${JSON.stringify(teamData)}.
    `;

    const handleGenerateReport = async () => {
        setIsGenerating(true);
        setGeneratedReport('');
        
        const fullPrompt = `
            As an expert business analyst, generate a professional report based on the following data for a pediatric healthcare company called "Our Kidz".

            **Dashboard Data:**
            ${dashboardContextForAI}

            **User Request:** "${reportPrompt}"

            **Formatting Instructions:**
            - Use clear headings for each section (e.g., "## Key Findings", "## Opportunities", "## Risks").
            - Use bullet points (using '*') for lists.
            - Use bold text ('**text**') for emphasis on key metrics and conclusions.
            - Write in a clear, professional, and easy-to-read style.
        `;

        try {
            let chatHistory = [{ role: "user", parts: [{ text: fullPrompt }] }];
            const payload = { contents: chatHistory };
            const apiKey = "";
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });
            const result = await response.json();
            if (result.candidates && result.candidates.length > 0 && result.candidates[0].content && result.candidates[0].content.parts && result.candidates[0].content.parts.length > 0) {
                const text = result.candidates[0].content.parts[0].text;
                setGeneratedReport(text);
            }
        } catch (error) {
            console.error("Error generating report:", error);
            setGeneratedReport("Sorry, an error occurred while generating the report.");
        } finally {
            setIsGenerating(false);
        }
    };

    const handleSuggestStrategies = async () => {
        setIsSuggesting(true);
        setSuggestedStrategies('');

        const prompt = `
            Given the following user acquisition funnel data for a company called "Our Kidz": ${JSON.stringify(funnelData)}.

            Please act as a marketing expert and suggest 2-3 creative and actionable strategies for each stage of the funnel to improve conversion rates.

            **Formatting Instructions:**
            - Create a main heading for the overall strategy.
            - For each funnel stage (e.g., Awareness, Consideration), use a sub-heading (e.g., "### Awareness Stage").
            - Under each stage, use bullet points for each suggested strategy.
            - Use bold text to highlight the core idea of each strategy.
            - The tone should be creative and encouraging.
        `;

        try {
             let chatHistory = [{ role: "user", parts: [{ text: prompt }] }];
            const payload = { contents: chatHistory };
            const apiKey = "";
            const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key=${apiKey}`;
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });
            const result = await response.json();
            if (result.candidates && result.candidates.length > 0 && result.candidates[0].content && result.candidates[0].content.parts && result.candidates[0].content.parts.length > 0) {
                const text = result.candidates[0].content.parts[0].text;
                setSuggestedStrategies(text);
            }
        } catch (error) {
            console.error("Error suggesting strategies:", error);
            setSuggestedStrategies("Sorry, an error occurred while suggesting strategies.");
        } finally {
            setIsSuggesting(false);
        }
    };


    const tabs = [
        { name: 'The Nest', icon: '🏠' },
        { name: 'Workforce Gap', icon: '👨‍⚕️' },
        { name: 'Market Stats', icon: '📈' },
        { name: 'Revenue Model', icon: '💰' },
        { name: 'Parent Journey', icon: '🗺️' },
        { name: 'Our Team', icon: '👥' },
        { name: 'RAG AI', icon: '🤖' },
    ];

    return (
        <div className="font-sans bg-gradient-to-br from-gray-900 to-gray-800 p-4 w-full rounded-xl shadow-lg text-white min-h-screen">
            <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                    <div className="mr-4">
                        <img src="https://storage.googleapis.com/momc_served_public/OKdarkTsp.png" alt="Our Kidz Logo" className="w-10 h-10" onError={(e) => { e.currentTarget.src = 'https://placehold.co/40x40/111827/FFFFFF?text=OK'; e.currentTarget.onerror = null; }} />
                    </div>
                    <div>
                        <h1 className="text-2xl font-bold text-white">Our Kidz <span className="text-gray-400 font-normal">Dashboard</span></h1>
                        <p className="text-sm text-purple-300">better tools for parents, better healthcare for kidz</p>
                    </div>
                </div>
                <div className="flex items-center space-x-4">
                     <button onClick={() => setIsReportModalOpen(true)} className="px-4 py-2 rounded-lg text-sm font-medium bg-gradient-to-r from-teal-500 to-cyan-500 text-white shadow-md hover:from-teal-600 hover:to-cyan-600 transition-all duration-200">
                        Generate Report ✨
                    </button>
                    <div className="flex space-x-2">
                        {tabs.map((tab, index) => (
                            <button
                                key={index}
                                onClick={() => setActiveTabIndex(index)}
                                className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center ${activeTabIndex === index ? 'bg-gradient-to-r from-purple-600 to-pink-500 text-white shadow-md' : 'bg-gray-800 text-gray-300 hover:bg-gray-700'}`}
                            >
                                <span className="mr-1">{tab.icon}</span>
                                {tab.name}
                            </button>
                        ))}
                    </div>
                </div>
            </div>

            <div className="grid grid-cols-12 gap-6">
                {activeTabIndex === 0 && (
                     <React.Fragment>
                        <div className="col-span-12 grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                            <div className="bg-gradient-to-br from-gray-800 to-gray-900 p-4 rounded-lg shadow border border-gray-700 flex flex-col">
                                <span className="text-sm text-gray-400">Pediatricians Needed (2030)</span>
                                <span className="text-2xl font-bold text-white">16,000</span>
                                <div className="mt-2 text-xs text-red-400">{16000 - 2800 * 6} deficit projected</div>
                            </div>
                            <div className="bg-gradient-to-br from-gray-800 to-gray-900 p-4 rounded-lg shadow border border-gray-700 flex flex-col">
                                <span className="text-sm text-gray-400">Market Opportunity</span>
                                <span className="text-2xl font-bold text-white">$5 Billion</span>
                                <div className="mt-2 text-xs text-teal-400">27% average growth</div>
                            </div>
                            <div className="bg-gradient-to-br from-gray-800 to-gray-900 p-4 rounded-lg shadow border border-gray-700 flex flex-col">
                                <span className="text-sm text-gray-400">Revenue Sources</span>
                                <span className="text-2xl font-bold text-white">4 Streams</span>
                                <div className="mt-2 text-xs text-yellow-400">SaaS is primary (42%)</div>
                            </div>
                            <div className="bg-gradient-to-br from-gray-800 to-gray-900 p-4 rounded-lg shadow border border-gray-700 flex flex-col">
                                <span className="text-sm text-gray-400">Team Experience</span>
                                <span className="text-2xl font-bold text-white">70+ Years</span>
                                <div className="mt-2 text-xs text-purple-400">Combined expertise</div>
                            </div>
                        </div>
                        <div className="col-span-12 md:col-span-6 bg-gray-800 p-4 rounded-lg shadow border border-gray-700">
                             <h2 className="text-lg font-semibold mb-4 text-white">Pediatric Workforce Projection</h2>
                             <ResponsiveContainer width="100%" height={200}>
                                 <AreaChart data={workforceGapData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                                     <defs>
                                        {ourKidzGradient('workforceGapFill')}
                                        {secondaryGradient('workforceProjectedFill')}
                                     </defs>
                                     <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                                     <XAxis dataKey="year" stroke="#94A3B8" />
                                     <YAxis stroke="#94A3B8" />
                                     <Tooltip content={<WorkforceGapTooltip />} />
                                     <Legend wrapperStyle={{ color: "#FFFFFF" }} />
                                     <Area type="monotone" dataKey="needed" name="Pediatricians Needed" stroke={COLORS.tertiary} fillOpacity={1} fill="url(#workforceGapFill)" />
                                     <Area type="monotone" dataKey="projected" name="Projected Supply" stroke={COLORS.secondary} fillOpacity={1} fill="url(#workforceProjectedFill)" />
                                 </AreaChart>
                             </ResponsiveContainer>
                        </div>
                        <div className="col-span-12 md:col-span-6 bg-gray-800 p-4 rounded-lg shadow border border-gray-700">
                             <h2 className="text-lg font-semibold mb-4 text-white">Market Segment Breakdown</h2>
                              <ResponsiveContainer width="100%" height={200}>
                                 <PieChart>
                                     <Pie
                                         activeIndex={activePieIndex}
                                         activeShape={renderActiveShape as ActiveShape<PieSectorDataItem>}
                                         data={marketSegmentData}
                                         cx="50%"
                                         cy="50%"
                                         innerRadius={40}
                                         outerRadius={60}
                                         fill={COLORS.primary}
                                         dataKey="value"
                                         onMouseEnter={(_, index) => setActivePieIndex(index)}
                                     >
                                        {marketSegmentData.map((entry, index) => (
                                            <Cell key={`cell-${index}`} fill={index === activePieIndex? '#E91E63' : [COLORS.secondary, COLORS.tertiary, COLORS.primary, COLORS.accent1, COLORS.accent2][index % 5]} />
                                        ))}
                                    </Pie>
                                 </PieChart>
                             </ResponsiveContainer>
                         </div>
                    </React.Fragment>
                )}
                
                {activeTabIndex === 1 && (
                    <div className="col-span-12 bg-gray-800 p-6 rounded-lg shadow border border-gray-700">
                         <div className="flex justify-between items-center mb-6">
                             <h2 className="text-xl font-bold text-white">Pediatric Workforce Gap Analysis</h2>
                             <div className="flex items-center space-x-2">
                                 <span className="text-sm text-gray-400">Project to year:</span>
                                 <select value={yearFilter} onChange={(e) => setYearFilter(Number(e.target.value))} className="bg-gray-700 border border-gray-600 rounded p-1 text-sm text-white">
                                     {workforceGapData.map(item => (<option key={item.year} value={item.year}>{item.year}</option>))}
                                 </select>
                             </div>
                         </div>
                         <ResponsiveContainer width="100%" height={400}>
                            <AreaChart data={filteredWorkforceData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
                                <defs>
                                    {primaryGradient('workforceNeededFill')}
                                    {secondaryGradient('workforceProjectedFill')}
                                </defs>
                                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                                <XAxis dataKey="year" stroke={COLORS.textMuted} />
                                <YAxis stroke={COLORS.textMuted} />
                                <Tooltip content={<WorkforceGapTooltip />} />
                                <Legend wrapperStyle={{ color: COLORS.text }} />
                                <Area type="monotone" dataKey="needed" name="Pediatricians Needed" stroke={COLORS.primary} fill="url(#workforceNeededFill)" />
                                <Area type="monotone" dataKey="projected" name="Projected Supply" stroke={COLORS.secondary} fill="url(#workforceProjectedFill)" />
                                <Area type="monotone" dataKey="deficit" name="Workforce Deficit" stroke={COLORS.danger} fill={COLORS.danger} fillOpacity={0.2} />
                            </AreaChart>
                        </ResponsiveContainer>
                    </div>
                )}

                {activeTabIndex === 2 && (
                    <div className="col-span-12 bg-gray-800 p-6 rounded-lg shadow border border-gray-700">
                        <h2 className="text-xl font-bold text-white">Market Opportunity Analysis</h2>
                         <ResponsiveContainer width="100%" height={400}>
                            <ScatterChart margin={{top: 20, right: 20, bottom: 20, left: 20,}}>
                                <CartesianGrid stroke="#374151"/>
                                <XAxis type="number" dataKey="growth" name="Growth Rate" unit="%" stroke={COLORS.textMuted}/>
                                <YAxis type="number" dataKey="value" name="Market Size" unit="M" stroke={COLORS.textMuted}/>
                                <ZAxis type="number" dataKey="future" range={[100, 600]} name="Future Market Size" unit="M" />
                                <Tooltip cursor={{ strokeDasharray: '3 3' }} content={<MarketSegmentTooltip />}/>
                                <Legend wrapperStyle={{ color: COLORS.text }} />
                                <Scatter name="Market Segments" data={marketSegmentData} fill={COLORS.primary}>
                                    {marketSegmentData.map((entry, index) => <Cell key={`cell-${index}`} fill={[COLORS.secondary, COLORS.tertiary, COLORS.primary, COLORS.accent1, COLORS.accent2][index % 5]} />)}
                                </Scatter>
                            </ScatterChart>
                        </ResponsiveContainer>
                    </div>
                )}

                {activeTabIndex === 3 && (
                    <div className="col-span-12 bg-gray-800 p-6 rounded-lg shadow border border-gray-700">
                        <h2 className="text-xl font-bold text-white">Revenue Breakdown</h2>
                         <ResponsiveContainer width="100%" height={400}>
                            <BarChart data={businessModelData.slice(0, 4)} margin={{top: 20, right: 30, left: 20, bottom: 5}}>
                                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                                <XAxis dataKey="name" stroke={COLORS.textMuted} />
                                <YAxis stroke={COLORS.textMuted} />
                                <Tooltip wrapperStyle={{ backgroundColor: '#333', border: '1px solid #555' }} />
                                <Legend wrapperStyle={{ color: COLORS.text }} />
                                <Bar dataKey="value" name="Revenue ($B)" radius={[4, 4, 0, 0]}>
                                    {businessModelData.slice(0, 4).map((entry, index) => <Cell key={`cell-${index}`} fill={entry.fill} />)}
                                </Bar>
                            </BarChart>
                        </ResponsiveContainer>
                    </div>
                )}

                {activeTabIndex === 4 && (
                    <div className="col-span-12">
                         <div className="bg-gray-800 p-6 rounded-lg shadow border border-gray-700 mb-6">
                            <h2 className="text-xl font-bold mb-6 text-white">User Acquisition & Conversion Strategy</h2>
                            <ResponsiveContainer width="100%" height={300}>
                                <BarChart layout="vertical" data={funnelData} margin={{top: 5, right: 30, left: 40, bottom: 5}}>
                                    <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                                    <XAxis type="number" domain={[0, 100]} stroke={COLORS.textMuted} />
                                    <YAxis dataKey="name" type="category" width={120} stroke={COLORS.textMuted} />
                                    <Tooltip wrapperStyle={{ backgroundColor: '#333', border: '1px solid #555' }}/>
                                    <Bar dataKey="value" fill={COLORS.tertiary} radius={[0, 4, 4, 0]} />
                                </BarChart>
                            </ResponsiveContainer>
                        </div>
                        <button onClick={handleSuggestStrategies} disabled={isSuggesting} className="mb-4 px-4 py-2 rounded-lg text-sm font-medium bg-gradient-to-r from-pink-500 to-orange-500 text-white shadow-md hover:from-pink-600 hover:to-orange-600 transition-all duration-200 disabled:opacity-50">
                            {isSuggesting ? 'Thinking...' : '✨ Suggest Strategies'}
                        </button>
                        {isSuggesting && <div className="text-center text-gray-400">Generating ideas...</div>}
                        {suggestedStrategies && (
                            <div className="bg-gray-800 p-6 rounded-lg shadow border border-gray-700 mt-4 prose prose-invert max-w-none">
                                <h3 className="text-lg font-semibold text-white mb-4">Suggested Strategies</h3>
                                <ReactMarkdown>{suggestedStrategies}</ReactMarkdown>
                            </div>
                        )}
                    </div>
                )}
                
                {activeTabIndex === 5 && (
                     <div className="col-span-12 bg-gray-800 p-6 rounded-lg shadow border border-gray-700">
                        <h2 className="text-xl font-bold text-white">Team Expertise & Experience</h2>
                        <div className="flex mt-6">
                            <div className="w-1/3 pr-4">
                                {teamData.map((member, index) => (
                                    <div key={index} onClick={() => setActiveTeamMember(index)} className={`p-3 rounded-lg cursor-pointer transition-all mb-2 ${activeTeamMember === index ? 'bg-blue-600' : 'bg-gray-700 hover:bg-gray-600'}`}>
                                        <h3 className="font-bold">{member.name}</h3>
                                        <p className="text-sm opacity-80">{member.role}</p>
                                    </div>
                                ))}
                            </div>
                            <div className="w-2/3">
                                <ResponsiveContainer width="100%" height={300}>
                                    <RadarChart cx="50%" cy="50%" outerRadius="80%" data={teamData[activeTeamMember].expertise}>
                                        <PolarGrid stroke="#4A5568" />
                                        <PolarAngleAxis dataKey="name" stroke={COLORS.textMuted} />
                                        <PolarRadiusAxis angle={30} domain={[0, 100]} stroke={COLORS.textMuted} />
                                        <Radar name={teamData[activeTeamMember].name} dataKey="value" stroke={COLORS.primary} fill={COLORS.primary} fillOpacity={0.6} />
                                    </RadarChart>
                                </ResponsiveContainer>
                            </div>
                        </div>
                    </div>
                )}

                {activeTabIndex === 6 && <RAGComponent />}
            </div>

            {isReportModalOpen && (
                <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
                    <div className="bg-gray-800 rounded-lg shadow-2xl p-6 w-full max-w-2xl text-white">
                        <h2 className="text-2xl font-bold mb-4">Generate Dashboard Report ✨</h2>
                        <p className="text-sm text-gray-400 mb-4">Enter a prompt to generate a report based on the dashboard data. The AI will use all available data for context.</p>
                        <textarea
                            value={reportPrompt}
                            onChange={(e) => setReportPrompt(e.target.value)}
                            className="w-full bg-gray-700 border border-gray-600 rounded p-2 text-sm text-white h-24"
                            placeholder="e.g., What are the biggest risks and opportunities?"
                        />
                        <div className="flex justify-end space-x-4 mt-4">
                            <button onClick={() => setIsReportModalOpen(false)} className="px-4 py-2 rounded-lg text-sm font-medium bg-gray-600 hover:bg-gray-500">Cancel</button>
                            <button onClick={handleGenerateReport} disabled={isGenerating} className="px-4 py-2 rounded-lg text-sm font-medium bg-gradient-to-r from-purple-600 to-pink-500 hover:from-purple-700 hover:to-pink-600 disabled:opacity-50">
                                {isGenerating ? 'Generating...' : 'Generate'}
                            </button>
                        </div>

                        {isGenerating && <div className="text-center text-gray-400 mt-4">Generating report, please wait...</div>}
                        
                        {generatedReport && (
                             <div className="mt-6 p-4 bg-gray-900 border border-gray-700 rounded-lg max-h-96 overflow-y-auto prose prose-invert max-w-none">
                                <h3 className="font-bold text-lg mb-2">Generated Report:</h3>
                                <ReactMarkdown>{generatedReport}</ReactMarkdown>
                                <button onClick={() => {
                                    const el = document.createElement('textarea');
                                    el.value = generatedReport;
                                    document.body.appendChild(el);
                                    el.select();
                                    document.execCommand('copy');
                                    document.body.removeChild(el);
                                }} className="mt-4 px-3 py-1 text-xs bg-teal-600 hover:bg-teal-500 rounded">Copy to Clipboard</button>
                            </div>
                        )}
                    </div>
                </div>
            )}
            
            <LiveAssistantComponent dashboardContext={dashboardContextForAI} />

            <div className="mt-6 text-center text-xs text-gray-400">
                <p>better tools for parents, better healthcare for kidz | Confidential & Proprietary © 2025</p>
            </div>
        </div>
    );
};

export default PediatricDashboard;
